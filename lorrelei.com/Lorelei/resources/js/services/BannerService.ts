import { Banner } from '@/models/Banner';
import { useTranslation } from '@/hooks/use-translation';

/**
 * Service pour gérer les opérations liées aux bannières
 *
 * Cette classe fournit des méthodes pour récupérer les bannières depuis l'API
 */
export class BannerService {
  /**
   * Récupère toutes les bannières actives
   *
   * @returns Une promesse qui résout avec un tableau de bannières
   */
  async getAllBanners(): Promise<Banner[]> {
    try {
      const response = await fetch('/api/banners');
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des bannières');
      }
      const data = await response.json();
      return this.mapApiBannersToModel(data);
    } catch (error) {
      console.error('Erreur dans getAllBanners():', error);
      return [];
    }
  }

  /**
   * Récupère les bannières pour une position spécifique
   *
   * @param position - Position des bannières à récupérer (ex: 'accueil', 'categorie-vetements')
   * @returns Une promesse qui résout avec un tableau de bannières
   */
  async getBannersByPosition(position: string): Promise<Banner[]> {
    try {
      const response = await fetch(`/api/banners/position/${position}`);
      if (!response.ok) {
        throw new Error(`Erreur lors de la récupération des bannières pour la position ${position}`);
      }
      const data = await response.json();
      return this.mapApiBannersToModel(data);
    } catch (error) {
      console.error(`Erreur dans getBannersByPosition(${position}):`, error);
      return [];
    }
  }

  /**
   * Convertit les données de l'API en instances du modèle Banner
   *
   * @param apiData - Données brutes de l'API
   * @returns Un tableau d'instances de Banner
   */
  private mapApiBannersToModel(apiData: any[]): Banner[] {
    // Récupérer la langue actuelle
    const locale = localStorage.getItem('locale') || 'fr';

    return apiData.map((item) => {
      // Extraire les champs traduisibles dans la langue actuelle
      let title = null;
      let description = null;
      let buttonText = null;

      try {
        // Essayer de parser le titre comme JSON (pour les traductions)
        if (item.title) {
          const titleObj = typeof item.title === 'string' ? JSON.parse(item.title) : item.title;
          title = titleObj[locale] || titleObj['fr'] || null;
        }
      } catch (e) {
        // Si ce n'est pas du JSON, utiliser la valeur telle quelle
        title = item.title || null;
      }

      try {
        // Essayer de parser la description comme JSON (pour les traductions)
        if (item.description) {
          const descObj = typeof item.description === 'string' ? JSON.parse(item.description) : item.description;
          description = descObj[locale] || descObj['fr'] || null;
        }
      } catch (e) {
        // Si ce n'est pas du JSON, utiliser la valeur telle quelle
        description = item.description || null;
      }

      try {
        // Essayer de parser le texte du bouton comme JSON (pour les traductions)
        if (item.button_text) {
          const btnTextObj = typeof item.button_text === 'string' ? JSON.parse(item.button_text) : item.button_text;
          buttonText = btnTextObj[locale] || btnTextObj['fr'] || null;
        }
      } catch (e) {
        // Si ce n'est pas du JSON, utiliser la valeur telle quelle
        buttonText = item.button_text || null;
      }

      return new Banner(
        item.id.toString(),
        item.position,
        title,
        description,
        buttonText,
        item.type || null,
        item.full_image_url || null,
        item.target_url || null,
        item.is_active
      );
    });
  }
}
