import { Product } from '../models/Product';
import { ProductVariant, ProductAttribute } from '../models/ProductVariant';

/**
 * Interface pour les données paginées
 */
interface PaginatedData<T> {
    data: T[];
    current_page: number;
    last_page: number;
    total: number;
    allowed_limits?: number[];
}

/**
 * Interface pour les attributs de type couleur
 */
interface ColorAttribute {
    type: 'couleur';
    nom: string;
    code: string;
    with_image?: boolean;
    color_image?: string;
}

/**
 * Interface pour les autres types d'attributs
 */
interface OtherAttribute {
    type: string;
    valeur: string;
}

/**
 * Service pour gérer les opérations liées aux produits
 *
 * Cette classe fournit des méthodes pour récupérer, filtrer et rechercher des produits
 */
export class ProductService {
    // Cache pour stocker les résultats des requêtes
    private static cache: Map<string, { data: any, timestamp: number }> = new Map();

    // Durée de validité du cache en millisecondes (5 minutes)
    private static cacheDuration: number = 5 * 60 * 1000;

    // Méthode pour vérifier si une clé est dans le cache et si elle est valide
    private getCachedData<T>(cacheKey: string): T | null {
        const cachedData = ProductService.cache.get(cacheKey);
        const now = Date.now();

        if (cachedData && (now - cachedData.timestamp) < ProductService.cacheDuration) {
            console.log('Utilisation des données en cache pour:', cacheKey);
            return cachedData.data as T;
        }

        return null;
    }

    // Méthode pour mettre en cache des données
    private setCachedData<T>(cacheKey: string, data: T): void {
        ProductService.cache.set(cacheKey, {
            data,
            timestamp: Date.now()
        });
    }
    /**
     * Récupère tous les produits disponibles
     *
     * @returns Une promesse qui résout avec un tableau de produits
     */
    async getAllProducts(page: number = 1, limit: number = 12): Promise<Product[]> {
        try {
            const response = await fetch(`/api/produits?page=${page}&limit=${limit}`);
            if (!response.ok) {
                throw new Error('Erreur lors de la récupération des produits');
            }
            const data = await response.json();

            return this.mapApiProductsToModel(data);
        } catch (error) {
            console.error('Erreur dans getAllProducts:', error);
            // En cas d'erreur, retourner un tableau vide
            return [];
        }
    }

    /**
     * Récupère les produits avec pagination et retourne des informations sur la pagination
     *
     * @param page - Numéro de la page à récupérer
     * @param limit - Nombre d'éléments par page
     * @param filters - Filtres à appliquer à la requête
     * @returns Une promesse qui résout avec un objet contenant les produits et les informations de pagination
     */
    async getProductsPaginated(
        page: number = 1,
        limit: number = 10,
        filters: Record<string, string | number | boolean | string[]> = {}
    ) {
        try {
            // Construire l'URL avec les paramètres de requête
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString()
            });

            // Ajouter la devise depuis le localStorage si disponible
            if (typeof window !== 'undefined') {
                const storedCurrency = localStorage.getItem('currency');
                if (storedCurrency && !filters.currency) {
                    filters.currency = storedCurrency;
                }
            }

            // Ajouter les filtres à l'URL
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                    if (Array.isArray(value)) {
                        // Pour les tableaux (comme les catégories ou marques sélectionnées)
                        if (value.length > 0) {
                            value.forEach(item => {
                                params.append(`${key}[]`, item.toString());
                            });
                        }
                    } else {
                        // Pour les valeurs simples
                        params.append(key, value.toString());
                    }
                }
            });

            // Créer une clé de cache unique basée sur les paramètres
            const cacheKey = `/api/produits?${params.toString()}`;

            // Vérifier si les données sont dans le cache
            const cachedData = this.getCachedData<{
                products: Product[];
                currentPage: number;
                totalPages: number;
                totalItems: number;
                hasMore: boolean;
                allowedLimits: number[];
            }>(cacheKey);

            if (cachedData) {
                return cachedData;
            }

            // Si les données ne sont pas en cache ou sont expirées, effectuer la requête
            console.log('Récupération des données depuis l\'API pour:', cacheKey);
            const response = await fetch(`/api/produits?${params.toString()}`);

            if (!response.ok) {
                throw new Error(`Erreur lors de la récupération des produits: ${response.status} ${response.statusText}`);
            }

            const data = await response.json() as PaginatedData<unknown>;

            // Préparer le résultat
            const result = {
                products: this.mapApiProductsToModel(data.data || data),
                currentPage: data.current_page || page,
                totalPages: data.last_page || 1,
                totalItems: data.total || 0,
                hasMore: data.current_page < data.last_page,
                allowedLimits: data.allowed_limits || [10, 20, 50, 100]
            };

            // Stocker le résultat dans le cache
            this.setCachedData(cacheKey, result);

            return result;
        } catch (error) {
            console.error('Erreur dans getProductsPaginated:', error);

            // En cas d'erreur, retourner un résultat vide mais valide
            return {
                products: [],
                currentPage: page,
                totalPages: 0,
                totalItems: 0,
                hasMore: false,
                allowedLimits: [10, 20, 50, 100]
            };
        }
    }

    /**
     * Récupère un produit par son identifiant
     *
     * @param id - L'identifiant du produit à récupérer
     * @returns Une promesse qui résout avec le produit ou null s'il n'existe pas
     */
    async getProductById(id: string): Promise<Product | null> {
        try {
            const response = await fetch(`/api/produits/${id}`);
            if (!response.ok) {
                throw new Error(`Erreur lors de la récupération du produit ${id}`);
            }
            const data = await response.json();
            return this.mapApiProductToModel(data);
        } catch (error) {
            console.error(`Erreur dans getProductById(${id}):`, error);
            // En cas d'erreur, retourner null
            return null;
        }
    }

    /**
     * Récupère un produit par son slug
     *
     * @param slug - Le slug du produit à récupérer
     * @returns Une promesse qui résout avec le produit ou null s'il n'existe pas
     */
    async getProductBySlug(slug: string): Promise<Product | null> {
        try {
            const response = await fetch(`/api/produits/slug/${slug}`);
            if (!response.ok) {
                throw new Error(`Erreur lors de la récupération du produit avec le slug ${slug}`);
            }
            const data = await response.json();
            return this.mapApiProductToModel(data);
        } catch (error) {
            console.error(`Erreur dans getProductBySlug(${slug}):`, error);
            // En cas d'erreur, retourner null
            return null;
        }
    }

    /**
     * Récupère les produits d'une catégorie spécifique
     *
     * @param categoryId - L'identifiant de la catégorie
     * @param limit - Nombre maximum de produits à récupérer (0 pour tous)
     * @param page - Numéro de la page à récupérer
     * @param excludeProductId - ID du produit à exclure des résultats (utile pour les produits similaires)
     * @returns Une promesse qui résout avec un tableau de produits de la catégorie
     */
    async getProductsByCategory(
        categoryId: string,
        limit: number = 0,
        page: number = 1,
        excludeProductId?: string
    ): Promise<Product[]> {
        try {
            // Construire l'URL avec les paramètres
            let url = `/api/produits/categorie/${categoryId}`;
            const params = new URLSearchParams();

            if (limit > 0) {
                params.append('limit', limit.toString());
            }

            if (page > 1) {
                params.append('page', page.toString());
            }

            if (excludeProductId) {
                params.append('exclude_product', excludeProductId);
            }

            if (params.toString()) {
                url += `?${params.toString()}`;
            }

            // Créer une clé de cache unique basée sur l'URL
            const cacheKey = url;

            // Vérifier si les données sont dans le cache
            const cachedData = this.getCachedData<Product[]>(cacheKey);

            if (cachedData) {
                return cachedData;
            }

            // Si les données ne sont pas en cache ou sont expirées, effectuer la requête
            console.log('Récupération des données depuis l\'API pour:', cacheKey);
            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`Erreur lors de la récupération des produits de la catégorie ${categoryId}`);
            }

            const data = await response.json();
            const products = this.mapApiProductsToModel(data);

            // Stocker le résultat dans le cache
            this.setCachedData(cacheKey, products);

            return products;
        } catch (error) {
            console.error(`Erreur dans getProductsByCategory(${categoryId}):`, error);
            // En cas d'erreur, retourner un tableau vide
            return [];
        }
    }

    /**
     * Récupère les produits d'une catégorie spécifique avec pagination
     *
     * @param categoryId - L'identifiant de la catégorie
     * @param page - Numéro de la page à récupérer
     * @param limit - Nombre d'éléments par page
     * @param filters - Filtres à appliquer à la requête
     * @returns Une promesse qui résout avec un objet contenant les produits et les informations de pagination
     */
    async getProductsByCategoryPaginated(
        categoryId: string,
        page: number = 1,
        limit: number = 12,
        filters: Record<string, string | number | boolean> = {}
    ) {
        try {
            // Construire l'URL avec les paramètres de requête
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString()
            });

            // Ajouter la devise depuis le localStorage si disponible
            if (typeof window !== 'undefined') {
                const storedCurrency = localStorage.getItem('currency');
                if (storedCurrency && !filters.currency) {
                    filters.currency = storedCurrency;
                }
            }

            // Ajouter les filtres à l'URL
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                    params.append(key, value.toString());
                }
            });

            // Créer une clé de cache unique basée sur l'URL
            const cacheKey = `/api/produits/categorie/${categoryId}?${params.toString()}`;

            // Vérifier si les données sont dans le cache
            const cachedData = this.getCachedData<{
                products: Product[];
                currentPage: number;
                totalPages: number;
                totalItems: number;
                hasMore: boolean;
                allowedLimits: number[];
            }>(cacheKey);

            if (cachedData) {
                return cachedData;
            }

            // Si les données ne sont pas en cache ou sont expirées, effectuer la requête
            console.log('Récupération des données depuis l\'API pour:', cacheKey);
            const response = await fetch(cacheKey);

            if (!response.ok) {
                throw new Error(`Erreur lors de la récupération des produits de la catégorie ${categoryId}: ${response.status} ${response.statusText}`);
            }

            const data = await response.json() as PaginatedData<unknown>;

            // Préparer le résultat
            const result = {
                products: this.mapApiProductsToModel(data),
                currentPage: data.current_page || page,
                totalPages: data.last_page || 1,
                totalItems: data.total || 0,
                hasMore: data.current_page < data.last_page,
                allowedLimits: data.allowed_limits || [10, 20, 50, 100]
            };

            // Stocker le résultat dans le cache
            this.setCachedData(cacheKey, result);

            return result;
        } catch (error) {
            console.error(`Erreur dans getProductsByCategoryPaginated(${categoryId}):`, error);

            // En cas d'erreur, retourner un résultat vide mais valide
            return {
                products: [],
                currentPage: page,
                totalPages: 0,
                totalItems: 0,
                hasMore: false,
                allowedLimits: [10, 20, 50, 100]
            };
        }
    }

    /**
     * Recherche des produits par terme de recherche
     *
     * @param searchTerm - Le terme de recherche
     * @returns Une promesse qui résout avec un tableau de produits correspondants
     */
    async searchProducts(searchTerm: string): Promise<Product[]> {
        try {
            const response = await fetch(`/api/produits/search?q=${encodeURIComponent(searchTerm)}`);
            if (!response.ok) {
                throw new Error(`Erreur lors de la recherche de produits avec le terme "${searchTerm}"`);
            }
            const data = await response.json();
            return this.mapApiProductsToModel(data);
        } catch (error) {
            console.error(`Erreur dans searchProducts(${searchTerm}):`, error);
            // En cas d'erreur, retourner un tableau vide
            return [];
        }
    }

    /**
     * Récupère les produits similaires à un produit donné
     *
     * @param product - Le produit pour lequel trouver des produits similaires
     * @param page - Numéro de la page à récupérer
     * @param limit - Nombre maximum de produits à récupérer par page
     * @returns Une promesse qui résout avec un objet contenant les produits et les informations de pagination
     */
    async getSimilarProducts(
        product: Product,
        page: number = 1,
        limit: number = 4
    ): Promise<{
        products: Product[];
        currentPage: number;
        totalPages: number;
        totalItems: number;
    }> {
        try {
            // Récupérer les produits de la même catégorie
            const products = await this.getProductsByCategory(product.category, limit, page, product.id);

            // Pour l'API réelle, nous pourrions avoir des informations de pagination
            // Pour le moment, nous simulons ces informations
            const totalItems = Math.min(20, products.length * 5); // Simulation: 5 pages de produits similaires
            const totalPages = Math.ceil(totalItems / limit);

            return {
                products,
                currentPage: page,
                totalPages,
                totalItems
            };
        } catch (error) {
            console.error(`Erreur dans getSimilarProducts:`, error);
            return {
                products: [],
                currentPage: page,
                totalPages: 1,
                totalItems: 0
            };
        }
    }

    /**
     * Récupère les produits en vedette
     *
     * @param limit - Nombre maximum de produits à récupérer
     * @returns Une promesse qui résout avec un tableau de produits en vedette
     */
    async getFeaturedProducts(limit: number = 8): Promise<Product[]> {
        try {
            const response = await fetch(`/api/produits/featured/${limit}`);
            if (!response.ok) {
                throw new Error(`Erreur lors de la récupération des produits en vedette`);
            }
            const data = await response.json();
            return this.mapApiProductsToModel(data);
        } catch (error) {
            console.error(`Erreur dans getFeaturedProducts(${limit}):`, error);
            // En cas d'erreur, retourner un tableau vide
            return [];
        }
    }



    /**
     * Génère des produits fictifs pour les tests
     *
     * @returns Un tableau de produits fictifs
     * @private
     */
    /**
     * Convertit les données API en modèles de produits
     *
     * Cette méthode gère différents formats de données :
     * - Un tableau de produits
     * - Un objet avec une propriété 'data' contenant un tableau de produits
     * - Un objet unique représentant un seul produit
     *
     * @param apiProducts - Les données de produits de l'API (tableau, objet avec 'data', ou objet unique)
     * @returns Un tableau de modèles de produits
     * @private
     */
    private mapApiProductsToModel(apiProducts: unknown): Product[] {
        // Vérifier si apiProducts est un tableau
        if (Array.isArray(apiProducts)) {
            return apiProducts.map(item => this.mapApiProductToModel(item as Record<string, unknown>));
        }

        // Vérifier si apiProducts est un objet avec une propriété 'data' qui est un tableau (données paginées)
        if (apiProducts && typeof apiProducts === 'object' && 'data' in apiProducts) {
            const paginatedData = apiProducts as Partial<PaginatedData<Record<string, unknown>>>;
            if (Array.isArray(paginatedData.data)) {
                return paginatedData.data.map(item => this.mapApiProductToModel(item));
            }
        }

        // Si c'est un objet unique, le convertir en tableau d'un seul élément
        if (apiProducts && typeof apiProducts === 'object') {
            return [this.mapApiProductToModel(apiProducts as Record<string, unknown>)];
        }

        // Fallback à un tableau vide
        console.error('Format de données inattendu dans mapApiProductsToModel:', apiProducts);
        return [];
    }

    /**
     * Convertit les données API d'un produit en modèle de produit
     *
     * @param apiProduct - Les données d'un produit de l'API
     * @returns Un modèle de produit
     * @private
     */
    private mapApiProductToModel(apiProduct: Record<string, unknown>): Product {
        let imageUrls: string[] = [];

        // Récupérer la langue actuelle
        const locale = localStorage.getItem('locale') || 'fr';
        // Utiliser les URLs d'images complètes si disponibles
        if (apiProduct.image_urls && Array.isArray(apiProduct.image_urls)) {
            imageUrls = apiProduct.image_urls;
        } else {
            // Fallback au parsing des images si nécessaire
            try {
                // Si images est déjà un tableau, l'utiliser directement
                if (Array.isArray(apiProduct.images)) {
                    imageUrls = apiProduct.images.map((img: string) =>
                        img.startsWith('http') ? img : `/images/products/${img}`
                    );
                } else {
                    // Sinon, essayer de parser comme JSON
                    const imagesStr = typeof apiProduct.images === 'string' ? apiProduct.images : '[]';
                    const images = JSON.parse(imagesStr);
                    if (Array.isArray(images)) {
                        imageUrls = images.map((img: string) =>
                            img.startsWith('http') ? img : `/images/products/${img}`
                        );
                    }
                }
            } catch (error) {
                console.error('Erreur lors du parsing des images:', error);
                // Fallback à un tableau vide en cas d'erreur
                imageUrls = [];
            }
        }

        // Convertir les dates de promotion si elles existent
        const discountStartDate = apiProduct.discount_start_date
            ? new Date(typeof apiProduct.discount_start_date === 'number'
                ? apiProduct.discount_start_date * 1000
                : String(apiProduct.discount_start_date))
            : null;

        const discountEndDate = apiProduct.discount_end_date
            ? new Date(typeof apiProduct.discount_end_date === 'number'
                ? apiProduct.discount_end_date * 1000
                : String(apiProduct.discount_end_date))
            : null;

        // Convertir le prix promotionnel
        const discountPrice = apiProduct.discount_price
            ? parseFloat(String(apiProduct.discount_price))
            : null;

        // Extraire le nom et la description dans la langue actuelle
        let name = '';
        let description = '';

        // Fonction pour extraire une valeur traduite
        const extractTranslatedValue = (value: unknown, defaultValue: string = ''): string => {
            if (!value) return defaultValue;

            // Si c'est déjà un objet, pas besoin de parser
            if (typeof value === 'object' && value !== null) {
                const translationObj = value as Record<string, string>;
                return translationObj[locale] || translationObj['fr'] || defaultValue;
            }

            // Si c'est une chaîne, essayer de la parser comme JSON
            if (typeof value === 'string') {
                try {
                    // Vérifier si la chaîne ressemble à du JSON
                    if (value.trim().startsWith('{') && value.trim().endsWith('}')) {
                        const translationObj = JSON.parse(value);
                        return translationObj[locale] || translationObj['fr'] || defaultValue;
                    }
                    // Si ce n'est pas du JSON, retourner la chaîne telle quelle
                    return value || defaultValue;
                } catch (error) {
                    console.warn('Erreur lors du parsing de la traduction:', error);
                    return value || defaultValue;
                }
            }

            // Fallback pour les autres types
            return String(value) || defaultValue;
        };

        // Extraire le nom traduit
        name = extractTranslatedValue(apiProduct.nom, 'Produit sans nom');

        // Extraire la description traduite
        description = extractTranslatedValue(apiProduct.description, 'Aucune description disponible');
        // Séparer les images principales et additionnelles
        let mainImageUrls: string[] = [];
        let additionalImageUrls: string[] = [];

        // Récupérer les images principales si disponibles
        if (apiProduct.main_image_urls && Array.isArray(apiProduct.main_image_urls)) {
            mainImageUrls = apiProduct.main_image_urls;
        } else {
            // Sinon, prendre les deux premières images
            mainImageUrls = imageUrls.slice(0, Math.min(2, imageUrls.length));
        }

        // S'assurer que mainImageUrls n'est pas vide
        if (mainImageUrls.length === 0 && imageUrls.length > 0) {
            mainImageUrls = [imageUrls[0]];
        }

        // Récupérer les images additionnelles si disponibles
        if (apiProduct.additional_image_urls && Array.isArray(apiProduct.additional_image_urls)) {
            additionalImageUrls = apiProduct.additional_image_urls;
        } else {
            // Sinon, prendre toutes les images après les deux premières
            additionalImageUrls = imageUrls.slice(Math.min(2, imageUrls.length));
        }

        // Préparer les valeurs pour le constructeur
        const id = String(apiProduct.id || '');
        const slug = String(apiProduct.slug || '');
        const productCode = apiProduct.product_code ? String(apiProduct.product_code) : null;
        const brand = apiProduct.marque ? String(apiProduct.marque) : null;
        const prix = typeof apiProduct.prix === 'number' || typeof apiProduct.prix === 'string'
            ? parseFloat(String(apiProduct.prix))
            : 0;
        const currency = String(apiProduct.currency || 'FCFA');
        const categorieId = apiProduct.categorie_id ? String(apiProduct.categorie_id) : '';
        const stock = typeof apiProduct.stock === 'number' ? apiProduct.stock : 0;
        const inStock = stock > 0;

        // Récupérer le nom du marchand
        let marchandNom = 'Boutique';
        if (apiProduct.marchand && typeof apiProduct.marchand === 'object') {
            const marchand = apiProduct.marchand as Record<string, unknown>;
            marchandNom = String(marchand.nomEntreprise || 'Boutique');
        }

        // Récupérer la note moyenne et le nombre d'avis depuis l'API
        const average_rating = typeof apiProduct.average_rating === 'number' ? apiProduct.average_rating :0;
        const reviews_count = typeof apiProduct.reviews_count === 'number' ? apiProduct.reviews_count : 0;

        // Récupérer le nombre de zones de livraison disponibles
        const availableZones = typeof apiProduct.available_zones_count === 'number' ? apiProduct.available_zones_count : 0;

        // Récupérer les attributs du produit
        const attributes: ProductAttribute[] = [];
        if (apiProduct.attributs && typeof apiProduct.attributs === 'object') {
            // Convertir les attributs du format JSON au format attendu par le frontend
            const attributsArray = Array.isArray(apiProduct.attributs)
                ? apiProduct.attributs
                : Object.entries(apiProduct.attributs).map(([key, value]) => {
                    // Si c'est un attribut de type couleur
                    if (key.toLowerCase() === 'couleur' || (typeof value === 'object' && value !== null && 'nom' in value && 'code' in value)) {
                        const colorAttr: ColorAttribute = {
                            type: 'couleur',
                            nom: typeof value === 'object' && value !== null && 'nom' in value ? value.nom : String(value),
                            code: typeof value === 'object' && value !== null && 'code' in value ? value.code : '#000000'
                        };

                        // Ajouter les nouveaux champs s'ils existent
                        if (typeof value === 'object' && value !== null) {
                            if ('with_image' in value) {
                                colorAttr.with_image = Boolean(value.with_image);
                            }
                            if ('color_image' in value) {
                                colorAttr.color_image = String(value.color_image);
                            }

                            // Si with_image est true mais qu'il n'y a pas de color_image,
                            // on utilise la première image du produit si disponible
                            if (colorAttr.with_image && (!colorAttr.color_image || colorAttr.color_image === '')) {
                                if (apiProduct.images && apiProduct.images.length > 0) {
                                    colorAttr.color_image = apiProduct.images[0];
                                }
                            }
                        }

                        return colorAttr;
                    }
                    // Pour les autres types d'attributs
                    else {
                        const type = key.toLowerCase() === 'taille' ? 'taille' :
                                    key.toLowerCase() === 'matiere' || key.toLowerCase() === 'matière' ? 'matiere' : 'autre';
                        return {
                            type,
                            valeur: String(value)
                        } as OtherAttribute;
                    }
                });

            attributes.push(...attributsArray);
        }

        // Récupérer les variantes du produit
        const variants: ProductVariant[] = [];
        if (apiProduct.variants && Array.isArray(apiProduct.variants)) {
            for (const variant of apiProduct.variants) {
                const variantAttributes: ProductAttribute[] = [];

                // Convertir les attributs de la variante
                if (variant.attributs && typeof variant.attributs === 'object') {
                    const variantAttributsArray = Array.isArray(variant.attributs)
                        ? variant.attributs
                        : Object.entries(variant.attributs).map(([key, value]) => {
                            // Si c'est un attribut de type couleur
                            if (key.toLowerCase() === 'couleur' || (typeof value === 'object' && value !== null && 'nom' in value && 'code' in value)) {
                                const colorAttr: ColorAttribute = {
                                    type: 'couleur',
                                    nom: typeof value === 'object' && value !== null && 'nom' in value ? value.nom : String(value),
                                    code: typeof value === 'object' && value !== null && 'code' in value ? value.code : '#000000'
                                };

                                // Ajouter les nouveaux champs s'ils existent
                                if (typeof value === 'object' && value !== null) {
                                    if ('with_image' in value) {
                                        colorAttr.with_image = Boolean(value.with_image);
                                    }
                                    if ('color_image' in value) {
                                        colorAttr.color_image = String(value.color_image);
                                    }

                                    // Si with_image est true mais qu'il n'y a pas de color_image,
                                    // on utilise la première image de la variante si disponible
                                    if (colorAttr.with_image && (!colorAttr.color_image || colorAttr.color_image === '')) {
                                        // Chercher la variante correspondante
                                        const variant = apiProduct.variants?.find(v => {
                                            if (!v.attributs) return false;
                                            const attrs = Array.isArray(v.attributs) ? v.attributs : Object.values(v.attributs);
                                            return attrs.some(a =>
                                                typeof a === 'object' &&
                                                a !== null &&
                                                'type' in a &&
                                                a.type === 'couleur' &&
                                                'nom' in a &&
                                                a.nom === colorAttr.nom
                                            );
                                        });

                                        // Si la variante a des images, utiliser la première
                                        if (variant && variant.images && variant.images.length > 0) {
                                            colorAttr.color_image = variant.images[0];
                                        }
                                    }
                                }

                                return colorAttr;
                            }
                            // Pour les autres types d'attributs
                            else {
                                const type = key.toLowerCase() === 'taille' ? 'taille' :
                                            key.toLowerCase() === 'matiere' || key.toLowerCase() === 'matière' ? 'matiere' : 'autre';
                                return {
                                    type,
                                    valeur: String(value)
                                } as OtherAttribute;
                            }
                        });

                    variantAttributes.push(...variantAttributsArray);
                }

                // Créer la variante
                variants.push(new ProductVariant(
                    String(variant.id || ''),
                    id,
                    variant.sku || null,
                    typeof variant.prix_supplement === 'number' || typeof variant.prix_supplement === 'string'
                        ? parseFloat(String(variant.prix_supplement))
                        : 0,
                    typeof variant.stock === 'number' ? variant.stock : 0,
                    variantAttributes,
                    variant.image_urls || []
                ));
            }
        }

        return new Product(
            id,
            name,
            slug,
            productCode,
            brand,
            description,
            prix,
            currency,
            discountPrice,
            discountStartDate,
            discountEndDate,
            mainImageUrls.length > 0 ? mainImageUrls[0] : (imageUrls.length > 0 ? imageUrls[0] : ''),
            imageUrls,
            categorieId,
            inStock,
            average_rating, // Utiliser la valeur réelle de l'API
            reviews_count, // Utiliser le nombre réel d'avis de l'API
            marchandNom,
            mainImageUrls,
            additionalImageUrls,
            attributes,
            variants,
            availableZones
        );
    }


}
