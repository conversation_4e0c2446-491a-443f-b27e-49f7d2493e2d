import api from './api';

/**
 * Interface pour les données de zone de livraison
 */
export interface ZoneLivraisonData {
  id: number;
  nom: string;
  type: 'Pays' | 'Region' | 'Ville' | 'Quartier';
  parent_id: number | null;
  code: string | null;
  actif: boolean;
  enfants?: ZoneLivraisonData[];
}

/**
 * Interface pour les données de disponibilité de livraison
 */
export interface DeliveryAvailabilityData {
  available: boolean;
  livraison?: {
    frais_livraison: number;
    delai_livraison_min: number;
    delai_livraison_max: number;
    frais_livraison_specifique?: number;
  };
}

/**
 * Interface pour les données de disponibilité de livraison avec zones enfants
 */
export interface DeliveryAvailabilityWithChildrenData extends DeliveryAvailabilityData {
  has_children: boolean;
  available_children: ZoneLivraisonData[];
  all_children: ZoneLivraisonData[];
}

/**
 * Interface pour les informations hiérarchiques d'une zone
 */
export interface ZoneHierarchyInfo {
  id: number;
  nom: string;
  type: 'Pays' | 'Region' | 'Ville' | 'Quartier';
}

/**
 * Interface pour les zones disponibles pour un produit
 */
export interface AvailableZoneData {
  zone: {
    id: number;
    nom: string;
    type: 'Pays' | 'Region' | 'Ville' | 'Quartier';
    code: string | null;
    hierarchy: ZoneHierarchyInfo[];
  };
  livraison: {
    frais_livraison: number;
    delai_livraison_min: number;
    delai_livraison_max: number;
  };
}

/**
 * Service de zone de livraison pour communiquer avec l'API
 */
const zoneLivraisonService = {
  /**
   * Récupère toutes les zones de livraison
   * @param params Paramètres de filtrage optionnels
   * @returns Liste des zones de livraison
   */
  getZonesLivraison: async (params?: { type?: string; parent_id?: number; actif?: boolean; search?: string }): Promise<ZoneLivraisonData[]> => {
    try {
      const response = await api.get('/zones-livraison', { params });
      return response.data.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des zones de livraison:', error);
      throw error;
    }
  },

  /**
   * Récupère l'arborescence complète des zones de livraison
   * @returns Arborescence des zones de livraison
   */
  getZonesLivraisonTree: async (): Promise<ZoneLivraisonData[]> => {
    try {
      const response = await api.get('/zones-livraison/tree');
      return response.data.data;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'arborescence des zones de livraison:', error);
      throw error;
    }
  },

  /**
   * Récupère une zone de livraison spécifique
   * @param id Identifiant de la zone de livraison
   * @returns Données de la zone de livraison
   */
  getZoneLivraison: async (id: number): Promise<ZoneLivraisonData> => {
    try {
      const response = await api.get(`/zones-livraison/${id}`);
      return response.data.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération de la zone de livraison ${id}:`, error);
      throw error;
    }
  },

  /**
   * Récupère les enfants d'une zone de livraison
   * @param id Identifiant de la zone de livraison parente
   * @returns Liste des zones de livraison enfants
   */
  getZoneLivraisonChildren: async (id: number): Promise<ZoneLivraisonData[]> => {
    try {
      const response = await api.get(`/zones-livraison/${id}/children`);
      return response.data.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération des enfants de la zone de livraison ${id}:`, error);
      throw error;
    }
  },

  /**
   * Vérifie la disponibilité de livraison pour un produit dans une zone
   * @param produitId Identifiant du produit
   * @param zoneId Identifiant de la zone de livraison
   * @returns Informations sur la disponibilité de livraison
   */
  checkDeliveryAvailability: async (produitId: number, zoneId: number): Promise<DeliveryAvailabilityData> => {
    try {
      console.log(`Vérification de la livraison pour produit ${produitId} dans la zone ${zoneId}`);

      const response = await api.get('/produits/delivery-check', {
        params: {
          produit_id: produitId,
          zone_id: zoneId
        }
      });

      console.log('Raw API response:', response); // Log pour déboguer

      if (response.data && response.data.data) {
        return response.data.data;
      } else {
        console.error('Format de réponse API inattendu:', response.data);
        return { available: false };
      }
    } catch (error: any) {
      console.error(`Erreur lors de la vérification de la disponibilité de livraison:`, error);

      // Afficher plus de détails sur l'erreur
      if (error.response) {
        console.error('Détails de l\'erreur:', {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers
        });
      } else if (error.request) {
        console.error('Pas de réponse reçue:', error.request);
      } else {
        console.error('Erreur de configuration:', error.message);
      }

      return { available: false };
    }
  },

  /**
   * Vérifie la disponibilité de livraison pour un produit dans une zone et ses zones enfants
   * @param produitId Identifiant du produit
   * @param zoneId Identifiant de la zone de livraison
   * @returns Informations sur la disponibilité de livraison avec les zones enfants
   */
  checkDeliveryAvailabilityWithChildren: async (produitId: number, zoneId: number): Promise<DeliveryAvailabilityWithChildrenData> => {
    try {
      console.log(`Vérification de la livraison avec enfants pour produit ${produitId} dans la zone ${zoneId}`);

      const response = await api.get('/produits/delivery-check-with-children', {
        params: {
          produit_id: produitId,
          zone_id: zoneId
        }
      });

      console.log('Raw API response for delivery check with children:', response); // Log pour déboguer

      if (response.data && response.data.data) {
        return response.data.data;
      } else {
        console.error('Format de réponse API inattendu:', response.data);
        return {
          available: false,
          has_children: false,
          available_children: [],
          all_children: []
        };
      }
    } catch (error: any) {
      console.error(`Erreur lors de la vérification de la disponibilité de livraison avec enfants:`, error);

      // Afficher plus de détails sur l'erreur
      if (error.response) {
        console.error('Détails de l\'erreur:', {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers
        });
      } else if (error.request) {
        console.error('Pas de réponse reçue:', error.request);
      } else {
        console.error('Erreur de configuration:', error.message);
      }

      return {
        available: false,
        has_children: false,
        available_children: [],
        all_children: []
      };
    }
  },

  /**
   * Récupère les zones de livraison d'un produit
   * @param produitId Identifiant du produit
   * @returns Liste des zones de livraison du produit
   */
  getZonesByProduit: async (produitId: number): Promise<any[]> => {
    try {
      const response = await api.get(`/produits/${produitId}/zones-livraison`);
      return response.data.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération des zones de livraison du produit ${produitId}:`, error);
      throw error;
    }
  },

  /**
   * Récupère toutes les zones disponibles pour un produit avec leurs informations hiérarchiques
   * @param produitId Identifiant du produit
   * @returns Liste des zones disponibles avec leurs informations hiérarchiques
   */
  getAvailableZonesForProduct: async (produitId: number): Promise<AvailableZoneData[]> => {
    try {
      console.log(`Récupération des zones disponibles pour le produit ${produitId}`);

      const response = await api.get(`/produits/${produitId}/available-zones`);

      console.log('Raw API response for available zones:', response); // Log pour déboguer

      if (response.data && response.data.data) {
        return response.data.data;
      } else {
        console.error('Format de réponse API inattendu:', response.data);
        return [];
      }
    } catch (error: any) {
      console.error(`Erreur lors de la récupération des zones disponibles pour le produit:`, error);

      // Afficher plus de détails sur l'erreur
      if (error.response) {
        console.error('Détails de l\'erreur:', {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers
        });
      } else if (error.request) {
        console.error('Pas de réponse reçue:', error.request);
      } else {
        console.error('Erreur de configuration:', error.message);
      }

      return [];
    }
  },

  /**
   * Récupère les zones de livraison d'un marchand
   * @param marchandId Identifiant du marchand
   * @returns Liste des zones de livraison du marchand
   */
  getZonesByMarchand: async (marchandId: number): Promise<any[]> => {
    try {
      const response = await api.get(`/marchands/${marchandId}/zones-livraison`);
      return response.data.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération des zones de livraison du marchand ${marchandId}:`, error);
      throw error;
    }
  }
};

export default zoneLivraisonService;
