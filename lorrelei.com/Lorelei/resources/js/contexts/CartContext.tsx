import { CartService } from '@/services/CartService';
import { Product } from '@/models/Product';
import { CartItem, DeliveryInfo } from '@/models/CartItem';
import { createContext, useContext, useEffect, useState, ReactNode } from 'react';

/**
 * Interface définissant les propriétés et méthodes exposées par le contexte du panier
 */
interface CartContextType {
  items: CartItem[];
  addItem: (product: Product, quantity?: number, deliveryInfo?: DeliveryInfo) => void;
  removeItem: (productId: string, variantKey?: string) => void;
  updateQuantity: (productId: string, quantity: number, variantKey?: string) => void;
  clearCart: () => void;
  subtotal: number;
  formattedSubtotal: string;
  total: number;
  formattedTotal: string;
  itemCount: number;
  isCartOpen: boolean;
  setCartOpen: (isOpen: boolean) => void;
  deliveryFees: number;
  formattedDeliveryFees: string;
  grandTotal: number;
  formattedGrandTotal: string;
  estimatedDeliveryTime: { min: number; max: number } | null;
}

/**
 * Contexte React pour le panier d'achat
 */
const CartContext = createContext<CartContextType | undefined>(undefined);

/**
 * Props pour le fournisseur de contexte du panier
 */
interface CartProviderProps {
  children: ReactNode;
}

/**
 * Fournisseur de contexte pour le panier d'achat
 *
 * Expose les fonctionnalités du panier à tous les composants enfants
 *
 * @param children - Les composants enfants qui auront accès au contexte
 */
export function CartProvider({ children }: CartProviderProps) {
  // Initialisation du service de panier
  const [cartService] = useState(() => new CartService());

  // États locaux pour le panier
  const [items, setItems] = useState<CartItem[]>([]);
  const [isCartOpen, setIsCartOpen] = useState(false);
  // Nous gardons ces états pour la compatibilité avec le code existant
  // Ils sont utilisés pour les frais de livraison globaux (non liés aux produits)
  const [deliveryFees, setDeliveryFees] = useState(0);
  const [estimatedDeliveryTime, setEstimatedDeliveryTime] = useState<{ min: number; max: number } | null>(null);

  // Mise à jour de l'état local à partir du service
  const refreshCart = () => {
    setItems(cartService.getItems());

    // Déclencher un événement personnalisé pour informer le DeliveryContext
    window.dispatchEvent(new CustomEvent('cartUpdated'));
  };

  // Chargement initial du panier
  useEffect(() => {
    refreshCart();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Écouter les événements de mise à jour des frais de livraison
  useEffect(() => {
    const handleDeliveryUpdate = (e: CustomEvent) => {
      if (e.detail) {
        const { fees, time } = e.detail;
        setDeliveryFees(fees || 0);
        setEstimatedDeliveryTime(time || null);
      }
    };

    window.addEventListener('deliveryUpdated', handleDeliveryUpdate as EventListener);

    return () => {
      window.removeEventListener('deliveryUpdated', handleDeliveryUpdate as EventListener);
    };
  }, []);

  /**
   * Ajoute un produit au panier
   *
   * @param product - Le produit à ajouter
   * @param quantity - La quantité à ajouter (par défaut: 1)
   * @param deliveryInfo - Les informations de livraison pour ce produit (optionnel)
   */
  const addItem = (product: Product, quantity: number = 1, deliveryInfo?: DeliveryInfo) => {
    cartService.addItem(product, quantity, deliveryInfo);
    refreshCart();
    setIsCartOpen(true); // Ouvre automatiquement la sidebar du panier
  };

  /**
   * Supprime un élément du panier
   *
   * @param productId - L'identifiant du produit à supprimer
   * @param variantKey - Clé optionnelle de la variante à supprimer
   */
  const removeItem = (productId: string, variantKey?: string) => {
    cartService.removeItem(productId, variantKey);
    refreshCart();
  };

  /**
   * Met à jour la quantité d'un élément du panier
   *
   * @param productId - L'identifiant du produit à mettre à jour
   * @param quantity - La nouvelle quantité
   * @param variantKey - Clé optionnelle de la variante à mettre à jour
   */
  const updateQuantity = (productId: string, quantity: number, variantKey?: string) => {
    cartService.updateQuantity(productId, quantity, variantKey);
    refreshCart();
  };

  /**
   * Vide complètement le panier
   */
  const clearCart = () => {
    cartService.clearCart();
    refreshCart();
  };

  // Calcul des valeurs dérivées
  const subtotal = cartService.getSubtotal();
  const deliveryFeesFromItems = cartService.getDeliveryFees();
  const total = cartService.getTotal();
  const formattedTotal = cartService.formattedTotal();
  const itemCount = cartService.getItemCount();

  // Calcul du total avec frais de livraison
  // Nous combinons les frais de livraison des produits et les frais de livraison globaux
  // Les frais de livraison des produits sont calculés à partir des informations de livraison de chaque produit
  // Les frais de livraison globaux sont mis à jour par des événements (pour la compatibilité avec le code existant)
  const totalDeliveryFees = deliveryFeesFromItems + deliveryFees;
  const grandTotal = subtotal + totalDeliveryFees;

  // Formatage des montants
  const formatPrice = (amount: number): string => {
    return `${amount.toLocaleString('fr-FR', { minimumFractionDigits: 0, maximumFractionDigits: 0 })} FCFA`;
  };

  const formattedSubtotal = formatPrice(subtotal);
  const formattedDeliveryFees = formatPrice(totalDeliveryFees);
  const formattedGrandTotal = formatPrice(grandTotal);

  // Valeur du contexte exposée aux composants
  const value = {
    items,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    subtotal,
    formattedSubtotal,
    total,
    formattedTotal,
    itemCount,
    isCartOpen,
    setCartOpen: setIsCartOpen,
    deliveryFees: totalDeliveryFees,
    formattedDeliveryFees,
    grandTotal,
    formattedGrandTotal,
    estimatedDeliveryTime,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
}

/**
 * Hook personnalisé pour accéder au contexte du panier
 *
 * @returns Le contexte du panier
 * @throws Error si utilisé en dehors d'un CartProvider
 */
export function useCart(): CartContextType {
  const context = useContext(CartContext);

  if (context === undefined) {
    throw new Error('useCart doit être utilisé à l\'intérieur d\'un CartProvider');
  }

  return context;
}
