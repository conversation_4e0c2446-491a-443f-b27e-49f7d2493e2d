/**
 * Type pour les traductions
 */
export type Translation = {
  fr: string;
  en?: string;
};

/**
 * Classe représentant une catégorie de produits dans l'application e-commerce
 *
 * Cette classe encapsule toutes les propriétés et méthodes liées à une catégorie
 */
export class Category {
  /**
   * Crée une nouvelle instance de catégorie
   *
   * @param id - Identifiant unique de la catégorie
   * @param name - Nom de la catégorie (chaîne simple ou objet de traduction)
   * @param slug - Slug SEO-friendly de la catégorie
   * @param description - Description de la catégorie (chaîne simple ou objet de traduction)
   * @param imageUrl - URL de l'image représentant la catégorie
   * @param parentId - Identifiant de la catégorie parente (si sous-catégorie)
   * @param rawName - Objet de traduction brut pour le nom
   * @param rawDescription - Objet de traduction brut pour la description
   */
  /**
   * Sous-catégories de cette catégorie
   */
  public children: Category[] = [];

  /**
   * Niveau de la catégorie dans la hiérarchie
   */
  public niveau: number = 1;

  /**
   * Chemin de la catégorie (format: "id_parent/id_category")
   */
  public categoryPath: string = '';

  constructor(
    public id: string,
    public name: string,
    public slug: string = '',
    public description: string = '',
    public imageUrl: string = '',
    public parentId: string | null = null,
    public rawName: Translation | null = null,
    public rawDescription: Translation | null = null
  ) {
    // Si c'est une catégorie principale, le niveau est 1 et le chemin est l'ID
    if (!parentId) {
      this.niveau = 1;
      this.categoryPath = id;
    }
  }

  /**
   * Obtient le nom traduit dans la langue spécifiée
   *
   * @param locale - Code de la langue (fr, en)
   * @returns Le nom traduit ou une valeur par défaut
   */
  getTranslatedName(locale: string = 'fr'): string {
    if (this.rawName) {
      return this.rawName[locale as keyof Translation] || this.rawName.fr || this.name || 'Catégorie sans nom';
    }
    return this.name || 'Catégorie sans nom';
  }

  /**
   * Obtient la description traduite dans la langue spécifiée
   *
   * @param locale - Code de la langue (fr, en)
   * @returns La description traduite ou une valeur par défaut
   */
  getTranslatedDescription(locale: string = 'fr'): string {
    if (this.rawDescription) {
      return this.rawDescription[locale as keyof Translation] ||
             this.rawDescription.fr ||
             this.description ||
             '';
    }
    return this.description || '';
  }

  /**
   * Vérifie si la catégorie est une sous-catégorie
   *
   * @returns true si la catégorie a un parent, false sinon
   */
  isSubcategory(): boolean {
    return this.parentId !== null;
  }

  /**
   * Vérifie si la catégorie a des sous-catégories
   *
   * @returns true si la catégorie a des sous-catégories, false sinon
   */
  hasChildren(): boolean {
    return this.children.length > 0;
  }
}
