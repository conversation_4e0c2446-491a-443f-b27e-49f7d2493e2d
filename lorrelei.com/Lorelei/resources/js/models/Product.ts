import { ProductVariant, ProductAttribute } from './ProductVariant';

/**
 * Classe représentant un produit dans l'application e-commerce
 *
 * Cette classe encapsule toutes les propriétés et méthodes liées à un produit
 */
export class Product {
  /**
   * Images principales du produit (maximum 2)
   */
  public mainImageUrls: string[] = [];

  /**
   * Images additionnelles du produit
   */
  public additionalImageUrls: string[] = [];

  /**
   * Attributs du produit
   */
  public attributes: ProductAttribute[] = [];

  /**
   * Variantes du produit
   */
  public variants: ProductVariant[] = [];

  /**
   * Nombre de zones de livraison disponibles pour ce produit
   */
  public availableZones: number = 0;

  /**
   * Crée une nouvelle instance de produit
   *
   * @param id - Identifiant unique du produit
   * @param name - Nom du produit
   * @param slug - Slug SEO-friendly du produit
   * @param description - Description détaillée du produit
   * @param price - Prix du produit
   * @param discountPrice - Prix promotionnel du produit (si applicable)
   * @param discountStartDate - Date de début de la promotion
   * @param discountEndDate - Date de fin de la promotion
   * @param imageUrl - URL de l'image principale du produit
   * @param imageUrls - Liste des URLs des images du produit
   * @param mainImageUrls - Liste des URLs des images principales du produit (max 2)
   * @param additionalImageUrls - Liste des URLs des images additionnelles du produit
   * @param category - Catégorie du produit
   * @param inStock - Indique si le produit est en stock
   * @param rating - Note moyenne du produit (sur 5)
   * @param reviews - Nombre d'avis sur le produit
   * @param seller - Vendeur du produit
   */

  constructor(
    public id: string,
    public name: string,
    public slug: string = '',
    public productCode: string | null = null,
    public brand: string | null = null,
    public description: string,
    public price: number,
    public currency: string = 'FCFA',
    public discountPrice: number | null = null,
    public discountStartDate: Date | null = null,
    public discountEndDate: Date | null = null,
    public imageUrl: string,
    public imageUrls: string[] = [],
    public category: string,
    public inStock: boolean = true,
    public rating: number = 0,
    public reviews: number = 0,
    public seller: string = '',
    mainImageUrls: string[] = [],
    additionalImageUrls: string[] = [],
    attributes: ProductAttribute[] = [],
    variants: ProductVariant[] = [],
    public availableZones: number = 0
  ) {
    // S'assurer que imageUrls est un tableau
    const safeImageUrls = Array.isArray(imageUrls) ? imageUrls : [];

    // Initialiser les images principales et additionnelles
    this.mainImageUrls = mainImageUrls.length > 0 ? mainImageUrls : safeImageUrls.slice(0, 2);
    this.additionalImageUrls = additionalImageUrls.length > 0 ? additionalImageUrls : safeImageUrls.slice(2);

    // Initialiser les attributs et les variantes
    this.attributes = attributes;
    this.variants = variants;
  }

  /**
   * Vérifie si le produit est en promotion
   *
   * @returns true si le produit est en promotion, false sinon
   */
  isOnSale(): boolean {
    if (!this.discountPrice) return false;

    const now = new Date();

    // Si pas de dates de promotion, vérifier seulement si le prix promotionnel existe
    if (!this.discountStartDate && !this.discountEndDate) {
      return this.discountPrice > 0 && this.discountPrice < this.price;
    }

    // Vérifier si la date actuelle est dans la période de promotion
    const isAfterStart = !this.discountStartDate || now >= this.discountStartDate;
    const isBeforeEnd = !this.discountEndDate || now <= this.discountEndDate;

    return isAfterStart && isBeforeEnd && this.discountPrice > 0 && this.discountPrice < this.price;
  }

  /**
   * Obtient le prix actuel du produit (prix promotionnel si en promotion, sinon prix normal)
   *
   * @returns Le prix actuel du produit
   */
  getCurrentPrice(): number {
    if (this.isOnSale() && this.discountPrice !== null) {
      const discountPrice = Number(this.discountPrice);
      return isNaN(discountPrice) ? Number(this.price) || 0 : discountPrice;
    }
    return Number(this.price) || 0;
  }

  /**
   * Formate le prix du produit avec sa propre devise
   *
   * @returns Le prix formaté avec la devise du produit
   */
  formattedPrice(): string {
    const price = Number(this.getCurrentPrice());
    return `${isNaN(price) ? '0' : price.toFixed(0)} ${this.currency || 'FCFA'}`;
  }

  /**
   * Formate le prix original du produit avec sa propre devise
   *
   * @returns Le prix original formaté avec la devise du produit
   */
  formattedOriginalPrice(): string {
    const price = Number(this.price);
    return `${isNaN(price) ? '0' : price.toFixed(0)} ${this.currency || 'FCFA'}`;
  }



  /**
   * Calcule le pourcentage de réduction
   *
   * @returns Le pourcentage de réduction ou null si pas de promotion
   */
  getDiscountPercentage(): number | null {
    if (!this.isOnSale() || this.discountPrice === null) return null;

    const price = Number(this.price);
    const discountPrice = Number(this.discountPrice);

    if (isNaN(price) || isNaN(discountPrice) || price <= 0) return null;

    const reduction = price - discountPrice;
    const percentage = (reduction / price) * 100;

    return Math.round(percentage);
  }
}
