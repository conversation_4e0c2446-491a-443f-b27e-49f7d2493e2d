/**
 * <PERSON><PERSON><PERSON><PERSON> représentant une bannière
 */
export class Banner {
  /**
   * Constructeur du modèle Banner
   *
   * @param id - Identifiant unique de la bannière
   * @param position - Position de la bannière sur le site
   * @param title - Titre de la bannière
   * @param description - Description de la bannière
   * @param buttonText - Texte du bouton de la bannière
   * @param type - Type de bannière (primary, secondary, promotional)
   * @param imageUrl - URL de l'image de la bannière (optionnel)
   * @param targetUrl - URL de destination lors du clic sur la bannière
   * @param isActive - Indique si la bannière est active
   */
  constructor(
    public id: string,
    public position: string,
    public title: string | null,
    public description: string | null,
    public buttonText: string | null,
    public type: string | null,
    public imageUrl: string | null,
    public targetUrl: string | null,
    public isActive: boolean = true
  ) {}

  /**
   * Retourne l'URL complète de l'image
   */
  get fullImageUrl(): string | null {
    return this.imageUrl;
  }
}
