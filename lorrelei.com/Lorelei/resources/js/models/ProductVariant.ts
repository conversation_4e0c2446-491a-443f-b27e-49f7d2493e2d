/**
 * Interface pour les attributs de type couleur
 */
export interface ColorAttribute {
  type: 'couleur';
  nom: string;
  code: string;
  with_image?: boolean;
  color_image?: string;
}

/**
 * Interface pour les autres types d'attributs
 */
export interface OtherAttribute {
  type: 'taille' | 'matiere' | 'autre';
  valeur: string;
}

/**
 * Type pour tous les types d'attributs possibles
 */
export type ProductAttribute = ColorAttribute | OtherAttribute;

/**
 * Classe représentant une variante de produit
 */
export class ProductVariant {
  /**
   * Crée une nouvelle instance de variante de produit
   */
  constructor(
    public id: string,
    public productId: string,
    public sku: string | null,
    public priceSupplément: number,
    public stock: number,
    public attributes: ProductAttribute[],
    public imageUrls: string[] = []
  ) {}

  /**
   * Vérifie si la variante est en stock
   */
  get inStock(): boolean {
    return this.stock > 0;
  }

  /**
   * Récupère le prix total (prix du produit + supplément)
   * @param basePrice Prix de base du produit
   */
  getTotalPrice(basePrice: number): number {
    return basePrice + this.priceSupplément;
  }

  /**
   * Formate le prix total avec le symbole de devise
   * @param basePrice Prix de base du produit
   * @param currency Devise
   */
  formattedTotalPrice(basePrice: number, currency: string = 'FCFA'): string {
    const totalPrice = this.getTotalPrice(basePrice);
    return `${totalPrice.toFixed(0)} ${currency}`;
  }

  /**
   * Récupère les attributs de type couleur
   */
  getColorAttributes(): ColorAttribute[] {
    return this.attributes.filter(
      (attr): attr is ColorAttribute => attr.type === 'couleur'
    );
  }

  /**
   * Récupère les attributs de type taille
   */
  getSizeAttributes(): OtherAttribute[] {
    return this.attributes.filter(
      (attr): attr is OtherAttribute => attr.type === 'taille'
    );
  }

  /**
   * Récupère les attributs de type matière
   */
  getMaterialAttributes(): OtherAttribute[] {
    return this.attributes.filter(
      (attr): attr is OtherAttribute => attr.type === 'matiere'
    );
  }

  /**
   * Récupère les autres attributs
   */
  getOtherAttributes(): OtherAttribute[] {
    return this.attributes.filter(
      (attr): attr is OtherAttribute => attr.type === 'autre'
    );
  }

  /**
   * Récupère une description textuelle des attributs
   */
  getAttributesDescription(): string {
    return this.attributes
      .map(attr => {
        if (attr.type === 'couleur') {
          return `Couleur: ${attr.nom}`;
        } else {
          return `${attr.type.charAt(0).toUpperCase() + attr.type.slice(1)}: ${attr.valeur}`;
        }
      })
      .join(', ');
  }
}
