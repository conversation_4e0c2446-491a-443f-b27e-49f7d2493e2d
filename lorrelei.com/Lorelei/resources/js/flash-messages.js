// Initialiser les messages flash
window.initFlashMessages = function() {
    // Récupérer les messages flash de la session
    const flashMessages = {
        success: document.querySelector('meta[name="flash-success"]')?.getAttribute('content'),
        error: document.querySelector('meta[name="flash-error"]')?.getAttribute('content'),
        warning: document.querySelector('meta[name="flash-warning"]')?.getAttribute('content'),
    };

    // Stocker les messages flash dans l'objet window.flash
    window.flash = flashMessages;
};
