import { useState } from 'react';
import { User, ShoppingCart, Heart, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Link, usePage } from '@inertiajs/react';
import { useCart } from '@/contexts/CartContext';
import { useWishlist } from '@/contexts/WishlistContext';
import { type SharedData } from '@/types';
import { useTranslation } from '@/hooks/use-translation';
import LanguageSwitcher from './LanguageSwitcher';
import CurrencySwitcher from './CurrencySwitcher';
import AppearanceToggleDropdown from '@/components/appearance-dropdown';
import UserMenuEcommerce from './UserMenuEcommerce';
import UserMenuGuest from './UserMenuGuest';

/**
 * Barre de navigation fixe en bas de l'écran pour les appareils mobiles
 */
export default function MobileBottomBar() {
  const { itemCount, setCartOpen } = useCart();
  const { itemCount: wishlistCount } = useWishlist();
  const { auth } = usePage<SharedData>().props;
  const isAuthenticated = !!auth.user;
  const { t } = useTranslation();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // Fonction pour ouvrir le modal du panier
  const openCartModal = () => {
    setCartOpen(true);
  };

  return (
    <>
      {/* Barre de navigation fixe en bas de l'écran */}
      <div className="fixed bottom-0 left-0 right-0 z-50 border-t bg-background shadow-md md:hidden">
        <div className="grid grid-cols-4 items-center">

          {/* Bouton Wishlist */}
          <Link href={route('wishlist')}>
            <Button variant="ghost" size="icon" className="relative h-14 w-full rounded-none">
              <div className="flex flex-col items-center justify-center">
                <Heart className="h-5 w-5" />
                {wishlistCount > 0 && (
                  <Badge
                    variant="secondary"
                    className="absolute right-1/4 top-1 flex h-5 w-5 items-center justify-center rounded-full p-0 text-xs"
                  >
                    {wishlistCount > 99 ? '99+' : wishlistCount}
                  </Badge>
                )}
                <span className="mt-1 text-[10px]">{t('header.wishlist')}</span>
              </div>
            </Button>
          </Link>

          {/* Bouton Panier */}
          <Button
            variant="ghost"
            size="icon"
            className="relative h-14 w-full rounded-none"
            onClick={openCartModal}
          >
            <div className="flex flex-col items-center justify-center">
              <ShoppingCart className="h-5 w-5" />
              {itemCount > 0 && (
                <Badge
                  variant="secondary"
                  className="absolute right-1/4 top-1 flex h-5 w-5 items-center justify-center rounded-full p-0 text-xs"
                >
                  {itemCount > 99 ? '99+' : itemCount}
                </Badge>
              )}
              <span className="mt-1 text-[10px]">{t('header.cart')}</span>
            </div>
          </Button>

          {/* Bouton Compte */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-14 w-full rounded-none">
                <div className="flex flex-col items-center justify-center">
                  <User className="h-5 w-5" />
                  <span className="mt-1 text-[10px]">{t('header.account')}</span>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {isAuthenticated ? (
                <UserMenuEcommerce user={auth.user} />
              ) : (
                <UserMenuGuest />
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Bouton Paramètres */}
          <Sheet open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="h-14 w-full rounded-none">
                <div className="flex flex-col items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                  <span className="mt-1 text-[10px]">{t('header.settings')}</span>
                </div>
              </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-auto rounded-t-xl">
              <div className="flex flex-col space-y-4 p-4">
                <h3 className="text-lg font-medium">{t('header.settings')}</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="flex flex-col items-center">
                    <span className="mb-2 text-sm">{t('header.language')}</span>
                    <LanguageSwitcher />
                  </div>
                  <div className="flex flex-col items-center">
                    <span className="mb-2 text-sm">{t('header.currency')}</span>
                    <CurrencySwitcher />
                  </div>
                  <div className="flex flex-col items-center">
                    <span className="mb-2 text-sm">{t('header.theme')}</span>
                    <AppearanceToggleDropdown />
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Espace pour compenser la hauteur de la barre en bas */}
      <div className="h-14 md:hidden"></div>
    </>
  );
}
