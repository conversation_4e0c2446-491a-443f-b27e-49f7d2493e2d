import { useState, useEffect, useRef } from 'react';
import { Link } from '@inertiajs/react';
import { Category } from '@/models/Category';
import { CategoryService } from '@/services/CategoryService';
import { ChevronRight, Grid2X2 } from 'lucide-react';

interface MegaMenuProps {
  categories: Category[];
}

export default function MegaMenu({ categories }: MegaMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState<Category | null>(null);
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [categoriesWithSubs, setCategoriesWithSubs] = useState<Set<string>>(new Set());
  const menuRef = useRef<HTMLDivElement>(null);
  const categoryService = new CategoryService();
  // Gérer la fermeture du menu lorsque l'utilisateur clique en dehors
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Charger les sous-catégories lorsque la catégorie active change
  useEffect(() => {
    if (!activeCategory) {
      setSubcategories([]);
      return;
    }

    const fetchSubcategories = async () => {
      setIsLoading(true);
      try {
        const subCats = await categoryService.getSubcategories(activeCategory.id);
        setSubcategories(subCats);
      } catch (error) {
        console.error(`Erreur lors de la récupération des sous-catégories pour ${activeCategory.getTranslatedName()}:`, error);
        setSubcategories([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubcategories();
  }, [activeCategory]);

  // Gérer le survol d'une catégorie
  const handleCategoryHover = (category: Category) => {
    setActiveCategory(category);
  };

  // Définir la première catégorie comme active par défaut et vérifier quelles catégories ont des sous-catégories
  useEffect(() => {
    if (categories.length > 0) {
      // Définir la première catégorie comme active si aucune n'est sélectionnée
      if (!activeCategory) {
        setActiveCategory(categories[0]);
      }

      // Vérifier quelles catégories ont des sous-catégories
      const checkSubcategories = async () => {
        const withSubsSet = new Set<string>();

        for (const category of categories) {
          try {
            const subs = await categoryService.getSubcategories(category.id);
            if (subs.length > 0) {
              withSubsSet.add(category.id);
            }
          } catch (error) {
            console.error(`Erreur lors de la vérification des sous-catégories pour ${category.getTranslatedName()}:`, error);
          }
        }

        setCategoriesWithSubs(withSubsSet);
      };

      checkSubcategories();
    }
  }, [categories, activeCategory]);

  return (
    <div className="relative" ref={menuRef}>
      {/* Navigation des catégories */}
      <nav className="flex items-center gap-6 overflow-x-auto whitespace-nowrap py-2 scrollbar-styled">
        {/* Lien vers toutes les catégories */}
        <Link
          href={route('categories')}
          className="mega-menu-category flex items-center gap-1 py-3 text-sm font-medium transition-colors hover:text-primary"
        >
          <Grid2X2 className="mr-1 h-4 w-4" />
          Toutes les catégories
        </Link>

        {categories.map((category) => (
          <div
            key={category.id}
            className="relative"
            onMouseEnter={() => {
              if (categoriesWithSubs.has(category.id)) {
                handleCategoryHover(category);
                setIsOpen(true);
              }
            }}
          >
            <Link
              href={route('category', { categorySlug: category.slug })}
              className={`mega-menu-category flex items-center py-3 text-sm font-medium transition-colors ${
                activeCategory?.id === category.id && isOpen ? 'active text-primary' : 'hover:text-primary'
              }`}
            >
              {category.name}
              {categoriesWithSubs.has(category.id) && (
                <ChevronRight className="ml-1 h-3 w-3 transition-transform" />
              )}
            </Link>
          </div>
        ))}
      </nav>

      {/* Mega menu */}
      <div
        className={`mega-menu-container absolute left-0 right-0 top-full z-50 mx-auto w-full max-w-screen-xl rounded-b-lg border bg-background shadow-lg ${
          isOpen ? 'open' : ''
        }`}
        onMouseLeave={() => setIsOpen(false)}
      >
        <div className="grid grid-cols-12 gap-4 p-6">
          {/* Colonne des catégories principales */}
          <div className="col-span-3 border-r pr-4">
            <h3 className="mb-4 text-lg font-semibold">Catégories</h3>
            <ul className="space-y-1">
              {categories.map((category) => (
                <li key={category.id}>
                  <button
                    className={`flex w-full items-center justify-between rounded-md px-3 py-2 text-left text-sm transition-colors ${
                      activeCategory?.id === category.id
                        ? 'bg-primary/10 text-primary'
                        : 'hover:bg-muted'
                    }`}
                    onMouseEnter={() => handleCategoryHover(category)}
                  >
                    <span>{category.name}</span>
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Colonne des sous-catégories */}
          <div className="col-span-9">
            {activeCategory && (
              <div>
                <div className="mb-4 flex items-center justify-between">
                  <h3 className="text-lg font-semibold">{activeCategory.name}</h3>
                  <div className="flex items-center gap-4">
                    <Link
                      href={route('categories')}
                      className="text-sm font-medium text-muted-foreground hover:text-primary hover:underline"
                    >
                      Toutes les catégories
                    </Link>
                    <Link
                      href={route('category', { categorySlug: activeCategory.slug })}
                      className="text-sm font-medium text-primary hover:underline"
                    >
                      Voir tout {activeCategory.name}
                    </Link>
                  </div>
                </div>

                {isLoading ? (
                  <div className="grid grid-cols-3 gap-6">
                    {Array.from({ length: 6 }).map((_, index) => (
                      <div key={index} className="flex flex-col items-center">
                        <div className="mb-2 h-24 w-24 animate-pulse rounded-full bg-muted"></div>
                        <div className="h-4 w-20 animate-pulse rounded bg-muted"></div>
                      </div>
                    ))}
                  </div>
                ) : subcategories.length > 0 ? (
                  <div className="grid grid-cols-4 gap-6">
                    {subcategories.map((subcategory) => (
                      <Link
                        key={subcategory.id}
                        href={route('category', { categorySlug: subcategory.slug })}
                        className="mega-menu-subcategory group flex flex-col items-center text-center"
                      >
                        <div className="mb-2 overflow-hidden rounded-full border p-1 transition-all group-hover:border-primary group-hover:shadow-md">
                          {subcategory.imageUrl ? (
                            <img
                              src={subcategory.imageUrl}
                              alt={subcategory.getTranslatedName()}
                              className="mega-menu-subcategory-image h-24 w-24 rounded-full object-cover transition-transform group-hover:scale-105"
                              loading="lazy"
                            />
                          ) : (
                            <div className="flex h-24 w-24 items-center justify-center rounded-full bg-muted">
                              <span className="text-xs md:text-sm font-semibold uppercase text-muted-foreground">{subcategory.getTranslatedName().charAt(0)}</span>
                            </div>
                          )}
                        </div>
                        <span className="mega-menu-subcategory-name text-sm font-medium group-hover:text-primary">{subcategory.getTranslatedName()}</span>
                      </Link>
                    ))}
                  </div>
                ) : (
                  <div className="flex h-40 items-center justify-center">
                    <p className="text-muted-foreground">Aucune sous-catégorie disponible</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
