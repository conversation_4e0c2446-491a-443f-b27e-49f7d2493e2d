import { Link } from '@inertiajs/react';
import { Facebook, Instagram, Twitter, Mail, Phone, MapPin } from 'lucide-react';
import AppearanceTabs from '@/components/appearance-tabs';
import { useTranslation } from '@/hooks/use-translation';

/**
 * Props pour le composant EcommerceFooter
 */
interface EcommerceFooterProps {
  className?: string;
}

/**
 * Composant de pied de page pour l'application e-commerce
 *
 * @param className - Classes CSS additionnelles
 */
export default function EcommerceFooter({ className = '' }: EcommerceFooterProps) {
  const currentYear = new Date().getFullYear();
  const { translate } = useTranslation();

  return (
    <footer className={`border-t bg-muted/30 ${className}`}>
      <div className="container mx-auto px-4 py-8">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* À propos */}
          <div>
            <h3 className="mb-4 text-lg font-semibold">{translate('footer.about_us_title')}</h3>
            <p className="mb-4 text-sm text-muted-foreground">
              {translate('footer.about_us_description')}
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-muted-foreground hover:text-primary">
                <Facebook className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </a>
              <a href="#" className="text-muted-foreground hover:text-primary">
                <Instagram className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </a>
              <a href="#" className="text-muted-foreground hover:text-primary">
                <Twitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </a>
            </div>
          </div>

          {/* Liens rapides */}
          <div>
            <h3 className="mb-4 text-lg font-semibold">{translate('footer.quick_links')}</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href={route('home')} className="text-muted-foreground hover:text-primary">
                  {translate('footer.home')}
                </Link>
              </li>
              <li>
                <Link href={route('products')} className="text-muted-foreground hover:text-primary">
                  {translate('footer.all_products')}
                </Link>
              </li>
              <li>
                <Link href={route('categories')} className="text-muted-foreground hover:text-primary">
                  {translate('footer.categories')}
                </Link>
              </li>
              <li>
                <Link href={route('cart')} className="text-muted-foreground hover:text-primary">
                  {translate('footer.cart')}
                </Link>
              </li>
              <li>
                <Link href={route('my-account')} className="text-muted-foreground hover:text-primary">
                  {translate('footer.my_account')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Informations */}
          <div>
            <h3 className="mb-4 text-lg font-semibold">{translate('footer.information')}</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/about" className="text-muted-foreground hover:text-primary">
                  {translate('footer.about_us')}
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-muted-foreground hover:text-primary">
                  {translate('footer.terms_conditions')}
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-muted-foreground hover:text-primary">
                  {translate('footer.privacy_policy')}
                </Link>
              </li>
              <li>
                <Link href="/shipping" className="text-muted-foreground hover:text-primary">
                  {translate('footer.shipping_returns')}
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-muted-foreground hover:text-primary">
                  {translate('footer.faq')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="mb-4 text-lg font-semibold">{translate('footer.contact')}</h3>
            <ul className="space-y-3 text-sm">
              <li className="flex items-start gap-3 text-muted-foreground">
                <MapPin className="mt-0.5 h-5 w-5 shrink-0" />
                <span>{translate('footer.address')}</span>
              </li>
              <li className="flex items-center gap-3 text-muted-foreground">
                <Phone className="h-5 w-5 shrink-0" />
                <span>{translate('footer.phone')}</span>
              </li>
              <li className="flex items-center gap-3 text-muted-foreground">
                <Mail className="h-5 w-5 shrink-0" />
                <span>{translate('footer.email')}</span>
              </li>
            </ul>

            <div className="mt-6">
              <h4 className="mb-2 text-sm font-medium">{translate('footer.appearance')}</h4>
              <AppearanceTabs />
            </div>
          </div>
        </div>

        <div className="mt-8 border-t pt-6 text-center text-sm text-muted-foreground">
          <p>{translate('footer.copyright', { year: currentYear })}</p>
        </div>
      </div>
    </footer>
  );
}
