import React, { useState, useEffect } from 'react';
import { useDelivery } from '@/contexts/DeliveryContext';
import { Product } from '@/models/Product';
import { Truck, AlertCircle, CheckCircle, XCircle, Loader2, MapPin } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/hooks/use-translation';
import { AvailableZoneData } from '@/services/zoneLivraisonService';
import zoneLivraisonService from '@/services/zoneLivraisonService';
import { formatPrice } from '@/utils/format';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface ProductAvailableZonesProps {
  product: Product;
  className?: string;
  showIcon?: boolean;
  compact?: boolean;
}

/**
 * Composant pour afficher les zones de livraison disponibles pour un produit
 */
export default function ProductAvailableZones({
  product,
  className = '',
  showIcon = true,
  compact = false
}: ProductAvailableZonesProps) {
  const { selectedZone, setSelectedZone } = useDelivery();
  const { translate } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [availableZones, setAvailableZones] = useState<AvailableZoneData[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // Fonction pour récupérer les zones disponibles pour le produit
  const fetchAvailableZones = async () => {
    if (!product.id) {
      setAvailableZones([]);
      return;
    }

    // Si nous avons déjà les zones disponibles, ne pas refaire l'appel
    if (availableZones.length > 0) {
      console.log('Using existing available zones from state');
      return;
    }

    // Identifiant unique pour ce produit
    console.log(`Fetching available zones for product ${product.id}`);

    try {
      setIsLoading(true);
      setError(null);

      const zones = await zoneLivraisonService.getAvailableZonesForProduct(parseInt(product.id.toString()));
      console.log(`Fetched ${zones.length} available zones for product ${product.id}`);
      setAvailableZones(zones);
    } catch (err) {
      console.error('Erreur lors de la récupération des zones disponibles:', err);
      setError(translate('Impossible de récupérer les zones de livraison disponibles'));
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour formater l'affichage de la hiérarchie d'une zone
  const formatHierarchy = (zone: AvailableZoneData) => {
    if (!zone.zone.hierarchy || zone.zone.hierarchy.length === 0) {
      return zone.zone.nom;
    }

    // Si c'est un pays, afficher juste le nom
    if (zone.zone.type === 'Pays') {
      return zone.zone.nom;
    }

    // Sinon, afficher le nom avec la hiérarchie entre parenthèses
    const hierarchyText = zone.zone.hierarchy
      .map(item => item.nom)
      .join(' > ');

    return `${zone.zone.nom} (${hierarchyText})`;
  };

  // Fonction pour obtenir une couleur en fonction du type de zone
  const getZoneTypeColor = (type: string) => {
    switch (type) {
      case 'Pays':
        return 'bg-blue-100 text-blue-800';
      case 'Region':
        return 'bg-green-100 text-green-800';
      case 'Ville':
        return 'bg-purple-100 text-purple-800';
      case 'Quartier':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Fonction pour obtenir un emoji en fonction du type de zone
  const getZoneTypeEmoji = (type: string) => {
    switch (type) {
      case 'Pays':
        return '🌍';
      case 'Region':
        return '🏞️';
      case 'Ville':
        return '🏙️';
      case 'Quartier':
        return '🏘️';
      default:
        return '📍';
    }
  };

  // Si les zones n'ont pas encore été chargées, afficher un bouton pour les charger
  if (availableZones.length === 0 && !isLoading && !error) {
    return (
      <div className={`flex flex-col items-center gap-2 ${className}`}>
        <Button
          variant="outline"
          size="sm"
          className="w-full cursor-pointer"
          onClick={fetchAvailableZones}
        >
          {showIcon && <MapPin className="h-4 w-4 mr-2" />}
          {translate('common.check_delivery_availability')}
        </Button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`flex items-center ${className}`}>
        {showIcon && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
        <span className="text-sm">{translate('common.checking_delivery_availability')}</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center text-destructive ${className}`}>
        {showIcon && <AlertCircle className="h-4 w-4 mr-2" />}
        <span className="text-sm">{error}</span>
      </div>
    );
  }

  if (availableZones.length === 0) {
    return (
      <div className={`flex items-center text-destructive ${className}`}>
        {showIcon && <XCircle className="h-4 w-4 mr-2" />}
        <span className="text-sm">{translate('common.no_delivery_zones')}</span>
      </div>
    );
  }

  // Si on est en mode compact, afficher juste le nombre de zones disponibles
  if (compact) {
    return (
      <div className={`flex items-center ${className}`}>
        {showIcon && <CheckCircle className="h-4 w-4 mr-2 text-green-600" />}
        <span className="text-sm">
          {translate('common.available_in')} {availableZones.length} {translate('common.zones')}
        </span>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="link" size="sm" className="ml-1 p-0 h-auto">
              <span className="text-xs underline cursor-pointer hover:text-green-600">{translate('Voir')}</span>
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>{translate('common.available_zones')}</DialogTitle>
            </DialogHeader>
            <div className="max-h-[60vh] overflow-y-auto">
              <Accordion type="single" collapsible className="w-full">
                {availableZones.map((zone, index) => (
                  <AccordionItem key={zone.zone.id} value={`zone-${zone.zone.id}`}>
                    <AccordionTrigger className="hover:no-underline">
                      <div className="flex items-center">
                        <span className="mr-2">{getZoneTypeEmoji(zone.zone.type)}</span>
                        <span className="font-medium">{zone.zone.nom}</span>
                        <Badge variant="outline" className={`ml-2 ${getZoneTypeColor(zone.zone.type)}`}>
                          {zone.zone.type}
                        </Badge>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-2 p-2">
                        {zone.zone.hierarchy.length > 1 && (
                          <div className="text-sm text-muted-foreground">
                            <span className="font-medium">{translate('common.hierarchy')}:</span>{' '}
                            {zone.zone.hierarchy.map(item => item.nom).join(' > ')}
                          </div>
                        )}
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">{translate('common.delivery_fee')}:</span>
                          <Badge variant="outline">
                            {formatPrice(zone.livraison.frais_livraison)}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">{translate('common.estimated_delivery_time')}:</span>
                          <Badge variant="outline">
                            {zone.livraison.delai_livraison_min === zone.livraison.delai_livraison_max
                              ? `${zone.livraison.delai_livraison_min} ${translate('common.days')}`
                              : `${zone.livraison.delai_livraison_min}-${zone.livraison.delai_livraison_max} ${translate('common.days')}`}
                          </Badge>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full mt-2 cursor-pointer"
                          onClick={() => {
                            setSelectedZone({
                              id: zone.zone.id,
                              nom: zone.zone.nom,
                              type: zone.zone.type,
                              parent_id: zone.zone.hierarchy.length > 1 ? zone.zone.hierarchy[zone.zone.hierarchy.length - 2].id : null,
                              code: zone.zone.code,
                              actif: true
                            });
                            setDialogOpen(false);
                          }}
                        >
                          {translate('common.select_this_zone')}
                        </Button>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  // Affichage complet
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {showIcon && <MapPin className="h-4 w-4 mr-2" />}
          <span className="text-sm font-medium">{translate('common.available_zones')}</span>
        </div>
        <Badge variant="outline" className="bg-green-50">
          {availableZones.length} {translate('common.zones')}
        </Badge>
      </div>

      <div className="space-y-2">
        {availableZones.slice(0, 3).map((zone) => (
          <Button
            key={zone.zone.id}
            variant="outline"
            size="sm"
            className="w-full justify-start text-left"
            onClick={() => {
              setSelectedZone({
                id: zone.zone.id,
                nom: zone.zone.nom,
                type: zone.zone.type,
                parent_id: zone.zone.hierarchy.length > 1 ? zone.zone.hierarchy[zone.zone.hierarchy.length - 2].id : null,
                code: zone.zone.code,
                actif: true
              });
            }}
          >
            <span className="mr-2">{getZoneTypeEmoji(zone.zone.type)}</span>
            <span className="truncate">{formatHierarchy(zone)}</span>
          </Button>
        ))}

        {availableZones.length > 3 && (
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="w-full">
                {translate('common.see_all_zones')} ({availableZones.length})
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>{translate('common.available_zones')}</DialogTitle>
              </DialogHeader>
              <div className="max-h-[60vh] overflow-y-auto">
                <Accordion type="single" collapsible className="w-full">
                  {availableZones.map((zone) => (
                    <AccordionItem key={zone.zone.id} value={`zone-${zone.zone.id}`}>
                      <AccordionTrigger className="hover:no-underline">
                        <div className="flex items-center">
                          <span className="mr-2">{getZoneTypeEmoji(zone.zone.type)}</span>
                          <span className="font-medium">{zone.zone.nom}</span>
                          <Badge variant="outline" className={`ml-2 ${getZoneTypeColor(zone.zone.type)}`}>
                            {zone.zone.type}
                          </Badge>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-2 p-2">
                          {zone.zone.hierarchy.length > 1 && (
                            <div className="text-sm text-muted-foreground">
                              <span className="font-medium">{translate('common.hierarchy')}:</span>{' '}
                              {zone.zone.hierarchy.map(item => item.nom).join(' > ')}
                            </div>
                          )}
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">{translate('common.delivery_fee')}:</span>
                            <Badge variant="outline">
                              {formatPrice(zone.livraison.frais_livraison)}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">{translate('common.estimated_delivery_time')}:</span>
                            <Badge variant="outline">
                              {zone.livraison.delai_livraison_min === zone.livraison.delai_livraison_max
                                ? `${zone.livraison.delai_livraison_min} ${translate('common.days')}`
                                : `${zone.livraison.delai_livraison_min}-${zone.livraison.delai_livraison_max} ${translate('common.days')}`}
                            </Badge>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full mt-2"
                            onClick={() => {
                              setSelectedZone({
                                id: zone.zone.id,
                                nom: zone.zone.nom,
                                type: zone.zone.type,
                                parent_id: zone.zone.hierarchy.length > 1 ? zone.zone.hierarchy[zone.zone.hierarchy.length - 2].id : null,
                                code: zone.zone.code,
                                actif: true
                              });
                            }}
                          >
                            {translate('common.select_this_zone')}
                          </Button>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  );
}
