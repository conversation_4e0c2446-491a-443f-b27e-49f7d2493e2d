import React, { useState, useEffect } from 'react';
import { Category } from '@/models/Category';
import { CategoryService } from '@/services/CategoryService';
import { Link } from '@inertiajs/react';

interface CategoriesSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CategoriesSidebar({ isOpen, onClose }: CategoriesSidebarProps) {
  const [mainCategories, setMainCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [subCategories, setSubCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const categoryService = new CategoryService();

  useEffect(() => {
    const fetchMainCategories = async () => {
      try {
        const categories = await categoryService.getMainCategories();
        setMainCategories(categories);
      } catch (error) {
        console.error('Erreur lors de la récupération des catégories principales:', error);
      }
    };

    fetchMainCategories();
  }, []);

  const handleCategoryClick = async (category: Category) => {
    setSelectedCategory(category);
    setLoading(true);

    try {
      const subcategoriesWithProducts = await categoryService.getSubcategoriesWithFeaturedProducts(category.id);
      setSubCategories(subcategoriesWithProducts);
    } catch (error) {
      console.error(`Erreur lors de la récupération des sous-catégories de ${category.id}:`, error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`fixed inset-0 z-50 ${isOpen ? 'block' : 'hidden'}`}>
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />

      {/* Sidebar */}
      <div className="absolute top-0 left-0 h-full w-80 bg-white shadow-lg transform transition-transform duration-300 ease-in-out">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">Catégories</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="flex h-[calc(100%-64px)]">
          {/* Main Categories */}
          <div className="w-1/3 border-r overflow-y-auto">
            {mainCategories.map((category) => (
              <div
                key={category.id}
                className={`p-3 cursor-pointer hover:bg-gray-100 ${selectedCategory?.id === category.id ? 'bg-gray-100 font-semibold' : ''}`}
                onClick={() => handleCategoryClick(category)}
              >
                {category.getTranslatedName()}
              </div>
            ))}
          </div>

          {/* Subcategories */}
          <div className="w-2/3 overflow-y-auto p-2">
            {selectedCategory ? (
              loading ? (
                <div className="flex justify-center items-center h-full">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
              ) : (
                <div>
                  <h3 className="text-lg font-semibold mb-3">{selectedCategory.getTranslatedName()}</h3>
                  <div className="grid grid-cols-1 gap-4">
                    {subCategories.map((item) => (
                      <Link
                        key={item.category.id}
                        href={route('category', { categorySlug: item.category.slug })}
                        className="block"
                      >
                        <div className="flex items-center p-2 hover:bg-gray-50 rounded">
                          {item.featuredProduct ? (
                            <div className="w-16 h-16 mr-3 flex-shrink-0">
                              <img
                                src={item.featuredProduct.images[0] || '/images/placeholder.png'}
                                alt={item.category.getTranslatedName()}
                                className="w-full h-full object-cover rounded"
                              />
                            </div>
                          ) : null}
                          <div>
                            <span className="font-medium">{item.category.getTranslatedName()}</span>
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              )
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500">
                Sélectionnez une catégorie
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
