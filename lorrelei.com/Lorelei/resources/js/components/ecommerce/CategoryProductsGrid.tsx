import { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react';
import { Category } from '@/models/Category';
import { Product } from '@/models/Product';
import { CategoryService } from '@/services/CategoryService';
import { ProductService } from '@/services/ProductService';
import { ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import ProductCard from './ProductCard';

interface CategoryProductsGridProps {
  category: Category;
}

export default function CategoryProductsGrid({ category }: CategoryProductsGridProps) {
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [productsBySubcategory, setProductsBySubcategory] = useState<Record<string, Product[]>>({});
  const [isLoading, setIsLoading] = useState(true);
  const categoryService = new CategoryService();
  const productService = new ProductService();

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Récupérer les sous-catégories
        const subs = await categoryService.getSubcategories(category.id);
        setSubcategories(subs);

        // Récupérer les produits pour chaque sous-catégorie
        const productsData: Record<string, Product[]> = {};

        // Récupérer les produits de la catégorie principale
        productsData[category.id] = await productService.getProductsByCategory(category.id, 4);

        // Récupérer les produits pour chaque sous-catégorie
        for (const sub of subs) {
          productsData[sub.id] = await productService.getProductsByCategory(sub.id, 4);
        }

        setProductsBySubcategory(productsData);
      } catch (error) {
        console.error('Erreur lors de la récupération des données:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [category.id]);

  if (isLoading) {
    return (
      <div className="space-y-8">
        {/* Placeholder pour la catégorie principale */}
        <div className="space-y-4">
          <div className="h-6 w-48 animate-pulse rounded bg-muted"></div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="h-64 animate-pulse rounded-lg bg-muted"></div>
            ))}
          </div>
        </div>

        {/* Placeholders pour les sous-catégories */}
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="space-y-4">
            <div className="h-6 w-48 animate-pulse rounded bg-muted"></div>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
              {Array.from({ length: 4 }).map((_, idx) => (
                <div key={idx} className="h-64 animate-pulse rounded-lg bg-muted"></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-12">
      {/* Produits de la catégorie principale */}
      {productsBySubcategory[category.id]?.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">{category.getTranslatedName()}</h2>
            <Link
              href={route('category', { categorySlug: category.slug })}
              className="flex items-center text-sm font-medium text-primary hover:underline"
            >
              Voir tout <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {productsBySubcategory[category.id].map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
          <Separator className="mt-8" />
        </div>
      )}

      {/* Produits par sous-catégorie */}
      {subcategories.map((subcategory) => (
        productsBySubcategory[subcategory.id]?.length > 0 && (
          <div key={subcategory.id} className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold">{subcategory.getTranslatedName()}</h2>
              <Link
                href={route('category', { categorySlug: subcategory.slug })}
                className="flex items-center text-sm font-medium text-primary hover:underline"
              >
                Voir tout <ChevronRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
              {productsBySubcategory[subcategory.id].map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
            {subcategory !== subcategories[subcategories.length - 1] && (
              <Separator className="mt-8" />
            )}
          </div>
        )
      ))}

      {/* Si aucun produit n'est trouvé */}
      {Object.values(productsBySubcategory).every(products => products.length === 0) && (
        <div className="flex h-64 flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
          <p className="mb-4 text-lg font-medium">Aucun produit trouvé dans cette catégorie</p>
          <p className="text-muted-foreground">Revenez bientôt pour découvrir nos nouveaux produits.</p>
          <Button asChild className="mt-6">
            <Link href={route('home')}>Retour à l'accueil</Link>
          </Button>
        </div>
      )}
    </div>
  );
}
