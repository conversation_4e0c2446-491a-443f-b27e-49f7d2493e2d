import React, { useState, useEffect, useCallback, ReactNode } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

/**
 * Props pour le composant Carousel
 */
interface CarouselProps {
  children: ReactNode | ReactNode[];
  autoPlay?: boolean;
  interval?: number;
  showArrows?: boolean;
  showDots?: boolean;
  className?: string;
}

/**
 * Composant de carousel pour afficher des éléments défilants
 *
 * @param children - Les éléments à afficher dans le carousel
 * @param autoPlay - Indique si le carousel doit défiler automatiquement
 * @param interval - Intervalle de défilement automatique en millisecondes
 * @param showArrows - Indique si les flèches de navigation doivent être affichées
 * @param showDots - Indique si les points de navigation doivent être affichés
 * @param className - Classes CSS additionnelles
 */
export default function Carousel({
  children,
  autoPlay = true,
  interval = 5000,
  showArrows = true,
  showDots = true,
  className = '',
}: CarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovering, setIsHovering] = useState(false);

  // Convertir children en tableau
  const childrenArray = React.Children.toArray(children);

  /**
   * Passe à la diapositive suivante
   */
  const nextSlide = useCallback(() => {
    setCurrentIndex((prevIndex) =>
      prevIndex === childrenArray.length - 1 ? 0 : prevIndex + 1
    );
  }, [childrenArray.length]);

  /**
   * Passe à la diapositive précédente
   */
  const prevSlide = useCallback(() => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? childrenArray.length - 1 : prevIndex - 1
    );
  }, [childrenArray.length]);

  /**
   * Passe à une diapositive spécifique
   *
   * @param index - L'index de la diapositive
   */
  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  // Défilement automatique
  useEffect(() => {
    if (autoPlay && !isHovering) {
      const timer = setInterval(() => {
        nextSlide();
      }, interval);

      return () => clearInterval(timer);
    }
  }, [autoPlay, interval, nextSlide, isHovering]);

  return (
    <div
      className={`relative overflow-hidden rounded-xl ${className}`}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div
        className="flex transition-transform duration-500 ease-in-out"
        style={{ transform: `translateX(-${currentIndex * 100}%)` }}
      >
        {childrenArray.map((child, index) => (
          <div key={index} className="min-w-full flex-shrink-0">
            {child}
          </div>
        ))}
      </div>

      {/* Flèches de navigation */}
      {showArrows && childrenArray.length > 1 && (
        <>
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-background/80 p-2 opacity-70 shadow-md hover:opacity-100 cursor-pointer"
            onClick={prevSlide}
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-background/80 p-2 opacity-70 shadow-md hover:opacity-100 cursor-pointer"
            onClick={nextSlide}
          >
            <ChevronRight className="h-6 w-6" />
          </Button>
        </>
      )}

      {/* Points de navigation */}
      {showDots && childrenArray.length > 1 && (
        <div className="absolute bottom-4 left-1/2 z-10 flex -translate-x-1/2 space-x-2">
          {childrenArray.map((_, index) => (
            <button
              key={index}
              className={`h-2 w-2 rounded-full transition-all ${
                index === currentIndex
                  ? 'bg-primary w-4'
                  : 'bg-primary/50 hover:bg-primary/70'
              }`}
              onClick={() => goToSlide(index)}
              aria-label={`Aller à la diapositive ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
}
