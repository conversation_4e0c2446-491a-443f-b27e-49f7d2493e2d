import { useState, useEffect } from 'react';
import { useTranslation } from '@/hooks/use-translation';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, X } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

export interface FilterOption {
    id: string;
    name: string;
    count?: number;
}

export interface PriceRange {
    min: number;
    max: number;
    current: [number, number];
}

export interface FilterCategory {
    id: string;
    name: string;
    options: FilterOption[];
    expanded?: boolean;
}

export interface ProductFiltersProps {
    categories: FilterCategory[];
    brands: FilterOption[];
    priceRange: PriceRange;
    selectedFilters: Record<string, string[]>;
    onFilterChange: (filters: Record<string, string[]>) => void;
    onPriceChange: (range: [number, number]) => void;
    onReset?: () => void;
    className?: string;
    inStockOnly?: boolean;
    onInStockChange?: (checked: boolean) => void;
    selectedRatings?: number[];
    onRatingChange?: (rating: number, checked: boolean) => void;
    sortBy?: string;
    onSortChange?: (value: string) => void;
}

/**
 * Composant de filtres pour les produits
 */
export default function ProductFilters({
    categories = [],
    brands = [],
    priceRange,
    selectedFilters = {},
    onFilterChange,
    onPriceChange,
    onReset,
    className = '',
}: ProductFiltersProps) {
    const { tDefault } = useTranslation();
    const [localFilters, setLocalFilters] = useState<Record<string, string[]>>(selectedFilters);
    const [localPriceRange, setLocalPriceRange] = useState<[number, number]>(priceRange.current);
    const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
    const [mobileFiltersVisible, setMobileFiltersVisible] = useState(false);

    // Mettre à jour les filtres locaux lorsque les props changent
    useEffect(() => {
        setLocalFilters(selectedFilters);
    }, [selectedFilters]);

    // Mettre à jour la plage de prix locale lorsque les props changent
    useEffect(() => {
        setLocalPriceRange(priceRange.current);
    }, [priceRange]);

    // Gérer le changement de filtre
    const handleFilterChange = (categoryId: string, optionId: string, checked: boolean) => {
        const newFilters = { ...localFilters };

        if (!newFilters[categoryId]) {
            newFilters[categoryId] = [];
        }

        if (checked) {
            // Ajouter l'option si elle n'est pas déjà présente
            if (!newFilters[categoryId].includes(optionId)) {
                newFilters[categoryId] = [...newFilters[categoryId], optionId];
            }
        } else {
            // Supprimer l'option
            newFilters[categoryId] = newFilters[categoryId].filter(id => id !== optionId);

            // Supprimer la catégorie si elle est vide
            if (newFilters[categoryId].length === 0) {
                delete newFilters[categoryId];
            }
        }

        setLocalFilters(newFilters);

        // Appliquer les filtres automatiquement
        onFilterChange(newFilters);
    };

    // Gérer le changement de prix
    const handlePriceChange = (value: number[]) => {
        const range: [number, number] = [value[0], value[1]];
        setLocalPriceRange(range);

        // Appliquer le filtre de prix automatiquement
        onPriceChange(range);
    };

    // Réinitialiser tous les filtres
    const resetFilters = () => {
        const emptyFilters = {};
        setLocalFilters(emptyFilters);
        setLocalPriceRange([priceRange.min, priceRange.max]);

        // Appliquer les filtres réinitialisés
        onFilterChange(emptyFilters);
        onPriceChange([priceRange.min, priceRange.max]);

        // Appeler la fonction de réinitialisation externe si elle existe
        if (onReset) {
            onReset();
        }
    };

    // Vérifier si des filtres sont appliqués
    const hasActiveFilters = () => {
        return Object.keys(localFilters).length > 0 ||
               localPriceRange[0] !== priceRange.min ||
               localPriceRange[1] !== priceRange.max;
    };

    // Basculer la visibilité des filtres sur mobile
    const toggleMobileFilters = () => {
        setMobileFiltersVisible(!mobileFiltersVisible);
    };

    return (
        <div className={`${className}`}>
            {/* Bouton pour afficher/masquer les filtres sur mobile */}
            <div className="mb-4 flex items-center justify-between md:hidden">
                <Button
                    variant="outline"
                    onClick={toggleMobileFilters}
                    className="w-full"
                >
                    {mobileFiltersVisible ? (
                        <>
                            <X className="mr-2 h-4 w-4" />
                            {tDefault('filters.hide_filters', 'Masquer les filtres')}
                        </>
                    ) : (
                        <>
                            <ChevronDown className="mr-2 h-4 w-4" />
                            {tDefault('filters.show_filters', 'Afficher les filtres')}
                        </>
                    )}
                </Button>
            </div>

            {/* Conteneur de filtres (toujours visible sur desktop, conditionnel sur mobile) */}
            <div className={`${mobileFiltersVisible ? 'block' : 'hidden'} md:block`}>
                {/* En-tête des filtres avec bouton de réinitialisation */}
                <div className="mb-4 flex items-center justify-between">
                    <h3 className="text-lg font-medium">
                        {tDefault('filters.title', 'Filtres')}
                    </h3>
                    {hasActiveFilters() && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={resetFilters}
                            className="text-sm text-muted-foreground hover:text-foreground"
                        >
                            {tDefault('filters.reset', 'Réinitialiser')}
                        </Button>
                    )}
                </div>

                {/* Filtre de prix */}
                <div className="mb-6">
                    <h4 className="mb-2 font-medium">
                        {tDefault('filters.price_range', 'Fourchette de prix')}
                    </h4>
                    <div className="mb-4">
                        <Slider
                            defaultValue={localPriceRange}
                            min={priceRange.min}
                            max={priceRange.max}
                            step={100}
                            value={localPriceRange}
                            onValueChange={handlePriceChange}
                            className="my-6"
                        />
                        <div className="flex items-center justify-between text-sm">
                            <span>{localPriceRange[0]} FCFA</span>
                            <span>{localPriceRange[1]} FCFA</span>
                        </div>
                    </div>
                </div>

                {/* Filtres de catégories */}
                <div className="mb-6">
                    <h4 className="mb-2 font-medium">
                        {tDefault('filters.categories', 'Catégories')}
                    </h4>
                    <div className="space-y-4">
                        {categories.map(category => (
                            <div key={category.id} className="border-b pb-2">
                                <div
                                    className="flex items-center justify-between cursor-pointer py-2"
                                    onClick={() => {
                                        setExpandedCategories(prev => ({
                                            ...prev,
                                            [category.id]: !prev[category.id]
                                        }));
                                    }}
                                >
                                    <span className="font-medium text-sm">{category.name}</span>
                                    {expandedCategories[category.id] ? (
                                        <ChevronUp className="h-4 w-4" />
                                    ) : (
                                        <ChevronDown className="h-4 w-4" />
                                    )}
                                </div>

                                {expandedCategories[category.id] && (
                                    <div className="space-y-2 pl-2 mt-2">
                                        {category.options.map(option => (
                                            <div key={option.id} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={`${category.id}-${option.id}`}
                                                    checked={localFilters[category.id]?.includes(option.id) || false}
                                                    onCheckedChange={(checked) =>
                                                        handleFilterChange(category.id, option.id, checked === true)
                                                    }
                                                />
                                                <Label
                                                    htmlFor={`${category.id}-${option.id}`}
                                                    className="text-sm cursor-pointer"
                                                >
                                                    {option.name}
                                                    {option.count !== undefined && (
                                                        <span className="ml-1 text-muted-foreground">
                                                            ({option.count})
                                                        </span>
                                                    )}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                </div>

                {/* Filtres de marques */}
                {brands.length > 0 && (
                    <div className="mb-6">
                        <h4 className="mb-2 font-medium">
                            {tDefault('filters.brands', 'Marques')}
                        </h4>
                        <div className="space-y-2 pl-2">
                            {brands.map(brand => (
                                <div key={brand.id} className="flex items-center space-x-2">
                                    <Checkbox
                                        id={`brand-${brand.id}`}
                                        checked={localFilters['brand']?.includes(brand.id) || false}
                                        onCheckedChange={(checked) =>
                                            handleFilterChange('brand', brand.id, checked === true)
                                        }
                                    />
                                    <Label
                                        htmlFor={`brand-${brand.id}`}
                                        className="text-sm cursor-pointer"
                                    >
                                        {brand.name}
                                        {brand.count !== undefined && (
                                            <span className="ml-1 text-muted-foreground">
                                                ({brand.count})
                                            </span>
                                        )}
                                    </Label>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
