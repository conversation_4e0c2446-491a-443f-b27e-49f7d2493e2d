import { DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { type User } from '@/types';
import { Link } from '@inertiajs/react';
import { LogOut, User as UserIcon } from 'lucide-react';

interface UserMenuEcommerceProps {
  user: User;
}

export default function UserMenuEcommerce({ user }: UserMenuEcommerceProps) {
  return (
    <>
      <DropdownMenuLabel className="p-0 font-normal">
        <div className="flex flex-col px-3 py-2">
          <span className="font-medium">{user.name}</span>
          <span className="text-xs text-muted-foreground">{user.email}</span>
        </div>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        <DropdownMenuItem asChild>
          <Link href={route('my-account')} className="flex w-full cursor-pointer items-center">
            <UserIcon className="mr-2 h-4 w-4" />
            Mon profil
          </Link>
        </DropdownMenuItem>
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuItem asChild>
        <Link method="post" href={route('logout')} className="flex w-full cursor-pointer items-center text-destructive">
          <LogOut className="mr-2 h-4 w-4" />
          Déconnexion
        </Link>
      </DropdownMenuItem>
    </>
  );
}
