import { useState, useEffect } from 'react';
import { useTranslation } from '@/hooks/use-translation';
import { Review } from '@/models/Review';
import { ReviewService, ReviewOptions, PaginatedData } from '@/services/ReviewService';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import ReviewItem from './ReviewItem';
import ReviewModal from './ReviewModal';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface ReviewsListProps {
  productId: string;
  productName: string;
  initialReviewsCount?: number;
}

export default function ReviewsList({ productId, productName, initialReviewsCount = 0 }: ReviewsListProps) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const reviewService = new ReviewService();

  // États pour la pagination et le filtrage
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalReviews, setTotalReviews] = useState(0); // Utilisé pour afficher le nombre total d'avis
  const [perPage, setPerPage] = useState(5);

  // États pour les options de filtrage et de tri
  const [filterWithImages, setFilterWithImages] = useState(false);
  const [filterRating, setFilterRating] = useState<number | undefined>(undefined);
  const [sortBy, setSortBy] = useState<'created_at' | 'rating' | 'likes' | 'dislikes'>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const { t } = useTranslation();

  const fetchReviews = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Préparer les options de requête
      const options: ReviewOptions = {
        page: currentPage,
        perPage: perPage,
        sortBy: sortBy,
        sortOrder: sortOrder,
        withImages: filterWithImages
      };

      // Ajouter le filtre par note si défini
      if (filterRating !== undefined) {
        options.rating = filterRating;
      }

      // Récupérer les avis avec les options
      const result = await reviewService.getProductReviews(productId, options);

      // Mettre à jour les états
      setReviews(result.reviews);
      setTotalPages(result.pagination.last_page);
      setTotalReviews(result.pagination.total);
    } catch (err) {
      console.error('Erreur lors de la récupération des avis:', err);
      setError(t('reviews.review_error'));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchReviews();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productId]);

  const handleReviewAdded = () => {
    fetchReviews();
  };

  // Mettre à jour les filtres et recharger les avis
  useEffect(() => {
    fetchReviews();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, perPage, sortBy, sortOrder, filterWithImages, filterRating]);

  const handleVoteChange = () => {
    // Pas besoin de rafraîchir toute la liste pour un vote
    // Les compteurs sont mis à jour localement dans le composant ReviewItem
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg border border-destructive/50 bg-destructive/10 p-4 text-center text-destructive">
        <p>{error}</p>
        <Button variant="outline" className="mt-2" onClick={fetchReviews}>
          Réessayer
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t('reviews.title')}</h3>
          <p className="text-sm text-muted-foreground">
            {t('reviews.reviews_count', { count: totalReviews })}
          </p>
        </div>
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={filterWithImages}
              onChange={(e) => setFilterWithImages(e.target.checked)}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            {t('reviews.with_images')}
          </label>
          <ReviewModal
            productId={productId}
            productName={productName}
            onReviewAdded={handleReviewAdded}
          />
        </div>
      </div>

      {/* Options de filtrage et de tri */}
      <div className="mb-6 flex flex-wrap items-center gap-4">
        {/* Sélecteur de nombre d'avis par page */}
        <div className="flex items-center gap-2">
          <label htmlFor="per-page" className="text-sm font-medium">
            {t('reviews.per_page')}:
          </label>
          <Select
            value={perPage.toString()}
            onValueChange={(value) => {
              setPerPage(parseInt(value));
              setCurrentPage(1); // Réinitialiser à la première page
            }}
          >
            <SelectTrigger id="per-page" className="h-8 w-24">
              <SelectValue placeholder={perPage.toString()} />
            </SelectTrigger>
            <SelectContent>
              {[2, 3, 5, 10].map((value) => (
                <SelectItem key={value} value={value.toString()}>
                  {value}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Filtre par note */}
        <div className="flex items-center gap-2">
          <label htmlFor="rating-filter" className="text-sm font-medium">
            {t('reviews.rating')}:
          </label>
          <Select
            value={filterRating !== undefined ? filterRating.toString() : 'all'}
            onValueChange={(value) => {
              setFilterRating(value === 'all' ? undefined : parseInt(value));
              setCurrentPage(1); // Réinitialiser à la première page
            }}
          >
            <SelectTrigger id="rating-filter" className="h-8 w-24">
              <SelectValue placeholder={t('reviews.all_ratings')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('reviews.all_ratings')}</SelectItem>
              {[5, 4, 3, 2, 1].map((value) => (
                <SelectItem key={value} value={value.toString()}>
                  {value} {value === 1 ? t('reviews.star') : t('reviews.stars')}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Tri par */}
        <div className="flex items-center gap-2">
          <label htmlFor="sort-by" className="text-sm font-medium">
            {t('reviews.sort_by')}:
          </label>
          <Select
            value={sortBy}
            onValueChange={(value: 'created_at' | 'rating' | 'likes' | 'dislikes') => {
              setSortBy(value);
              setCurrentPage(1); // Réinitialiser à la première page
            }}
          >
            <SelectTrigger id="sort-by" className="h-8 w-32">
              <SelectValue placeholder={t('reviews.sort_by_date')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="created_at">{t('reviews.sort_by_date')}</SelectItem>
              <SelectItem value="rating">{t('reviews.sort_by_rating')}</SelectItem>
              <SelectItem value="likes">{t('reviews.sort_by_likes')}</SelectItem>
              <SelectItem value="dislikes">{t('reviews.sort_by_dislikes')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Ordre de tri */}
        <div className="flex items-center gap-2">
          <label htmlFor="sort-order" className="text-sm font-medium">
            {t('reviews.sort_order')}:
          </label>
          <Select
            value={sortOrder}
            onValueChange={(value: 'asc' | 'desc') => {
              setSortOrder(value);
              setCurrentPage(1); // Réinitialiser à la première page
            }}
          >
            <SelectTrigger id="sort-order" className="h-8 w-32">
              <SelectValue placeholder={t('reviews.descending')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="desc">{t('reviews.descending')}</SelectItem>
              <SelectItem value="asc">{t('reviews.ascending')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {reviews.length > 0 ? (
        <div className="space-y-6">
          {/* Liste des avis */}
          <div className="space-y-4">
            {reviews.map((review) => (
              <ReviewItem
                key={review.id}
                review={review}
                onVoteChange={handleVoteChange}
              />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2 py-4">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
                className="h-8 w-8"
              >
                <ChevronsLeft className="h-4 w-4" />
                <span className="sr-only">{t('reviews.first_page')}</span>
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
                className="h-8 w-8"
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="sr-only">{t('reviews.previous_page')}</span>
              </Button>

              {/* Numéros de page avec points de suspension */}
              {Array.from({ length: totalPages }).map((_, index) => {
                const pageNumber = index + 1;

                // Afficher toujours la première page, la dernière page, la page courante
                // et une page avant et après la page courante
                if (
                  pageNumber === 1 ||
                  pageNumber === totalPages ||
                  (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                ) {
                  return (
                    <Button
                      key={pageNumber}
                      variant={pageNumber === currentPage ? "default" : "outline"}
                      size="icon"
                      onClick={() => setCurrentPage(pageNumber)}
                      className="h-8 w-8"
                    >
                      {pageNumber}
                    </Button>
                  );
                }

                // Afficher des points de suspension pour les pages intermédiaires
                // mais seulement une fois entre les groupes de pages
                if (
                  (pageNumber === 2 && currentPage > 3) ||
                  (pageNumber === totalPages - 1 && currentPage < totalPages - 2)
                ) {
                  return <span key={pageNumber} className="px-2">...</span>;
                }

                return null;
              })}

              <Button
                variant="outline"
                size="icon"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="h-8 w-8"
              >
                <ChevronRight className="h-4 w-4" />
                <span className="sr-only">{t('reviews.next_page')}</span>
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
                className="h-8 w-8"
              >
                <ChevronsRight className="h-4 w-4" />
                <span className="sr-only">{t('reviews.last_page')}</span>
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="rounded-lg border border-dashed p-6 text-center">
          <p className="text-muted-foreground">
            {t('reviews.no_reviews')}
          </p>
        </div>
      )}
    </div>
  );
}
