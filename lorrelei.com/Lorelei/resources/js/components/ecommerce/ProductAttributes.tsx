import React from 'react';
import { ProductAttribute } from '@/models/ProductVariant';
import { cn } from '@/lib/utils';

interface ProductAttributesProps {
  attributes: ProductAttribute[];
  className?: string;
}

/**
 * Composant pour afficher les attributs d'un produit
 */
export default function ProductAttributes({ attributes, className }: ProductAttributesProps) {
  // Regrouper les attributs par type
  const colorAttributes = attributes.filter(attr => attr.type === 'couleur');
  const sizeAttributes = attributes.filter(attr => attr.type === 'taille');
  const materialAttributes = attributes.filter(attr => attr.type === 'matiere');
  const otherAttributes = attributes.filter(attr => attr.type === 'autre');

  if (attributes.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Attributs de couleur */}
      {colorAttributes.length > 0 && (
        <div>
          <h3 className="mb-2 text-sm font-medium">Couleurs</h3>
          <div className="flex flex-wrap gap-2">
            {colorAttributes.map((attr, index) => (
              'nom' in attr && 'code' in attr && (
                <div 
                  key={index} 
                  className="flex items-center gap-2"
                  title={attr.nom}
                >
                  <div 
                    className="h-6 w-6 rounded-full border shadow" 
                    style={{ backgroundColor: attr.code }}
                  />
                  <span className="text-sm">{attr.nom}</span>
                </div>
              )
            ))}
          </div>
        </div>
      )}

      {/* Attributs de taille */}
      {sizeAttributes.length > 0 && (
        <div>
          <h3 className="mb-2 text-sm font-medium">Tailles</h3>
          <div className="flex flex-wrap gap-2">
            {sizeAttributes.map((attr, index) => (
              'valeur' in attr && (
                <div 
                  key={index} 
                  className="rounded-md border px-3 py-1 text-sm"
                >
                  {attr.valeur}
                </div>
              )
            ))}
          </div>
        </div>
      )}

      {/* Attributs de matière */}
      {materialAttributes.length > 0 && (
        <div>
          <h3 className="mb-2 text-sm font-medium">Matières</h3>
          <div className="flex flex-wrap gap-2">
            {materialAttributes.map((attr, index) => (
              'valeur' in attr && (
                <div 
                  key={index} 
                  className="rounded-md border px-3 py-1 text-sm"
                >
                  {attr.valeur}
                </div>
              )
            ))}
          </div>
        </div>
      )}

      {/* Autres attributs */}
      {otherAttributes.length > 0 && (
        <div>
          <h3 className="mb-2 text-sm font-medium">Autres caractéristiques</h3>
          <div className="space-y-1">
            {otherAttributes.map((attr, index) => (
              'valeur' in attr && (
                <div key={index} className="text-sm">
                  {attr.valeur}
                </div>
              )
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
