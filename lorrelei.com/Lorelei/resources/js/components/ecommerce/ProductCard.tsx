import { Link } from '@inertiajs/react';
import { Product } from '@/models/Product';
import { ShoppingCart, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCart } from '@/contexts/CartContext';

interface ProductCardProps {
  product: Product;
  className?: string;
}

export default function ProductCard({ product, className = '' }: ProductCardProps) {
  const { addToCart } = useCart();
  
  const hasDiscount = product.discount_price && product.discount_price < product.price;
  const discountPercentage = hasDiscount 
    ? Math.round(((product.price - product.discount_price!) / product.price) * 100) 
    : 0;

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(product);
  };

  return (
    <Link
      href={route('product', { productSlug: product.slug })}
      className={`group relative flex flex-col overflow-hidden rounded-lg border bg-card transition-all hover:shadow-md ${className}`}
    >
      {/* Image du produit */}
      <div className="relative aspect-square overflow-hidden bg-muted">
        {product.image_urls && product.image_urls.length > 0 ? (
          <img
            src={product.image_urls[0]}
            alt={product.name}
            className="h-full w-full object-cover transition-transform group-hover:scale-105"
            loading="lazy"
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-muted">
            <span className="text-sm text-muted-foreground">Pas d'image</span>
          </div>
        )}
        
        {/* Badge de réduction */}
        {hasDiscount && (
          <Badge className="absolute left-2 top-2 bg-destructive">
            -{discountPercentage}%
          </Badge>
        )}
        
        {/* Bouton favoris */}
        <Button
          variant="outline"
          size="icon"
          className="absolute right-2 top-2 h-8 w-8 rounded-full bg-background/80 opacity-0 transition-opacity group-hover:opacity-100"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            // Ajouter aux favoris
          }}
        >
          <Heart className="h-4 w-4" />
        </Button>
      </div>
      
      {/* Informations du produit */}
      <div className="flex flex-1 flex-col p-4">
        <h3 className="mb-1 line-clamp-2 text-sm font-medium">{product.name}</h3>
        
        <div className="mt-auto">
          {/* Prix */}
          <div className="mb-2 flex items-center gap-2">
            {hasDiscount ? (
              <>
                <span className="font-bold">{product.discount_price?.toFixed(2)}€</span>
                <span className="text-sm text-muted-foreground line-through">{product.price.toFixed(2)}€</span>
              </>
            ) : (
              <span className="font-bold">{product.price.toFixed(2)}€</span>
            )}
          </div>
          
          {/* Bouton ajouter au panier */}
          <Button
            variant="default"
            size="sm"
            className="w-full"
            onClick={handleAddToCart}
            disabled={product.stock <= 0}
          >
            {product.stock > 0 ? (
              <>
                <ShoppingCart className="mr-2 h-4 w-4" />
                Ajouter au panier
              </>
            ) : (
              'Rupture de stock'
            )}
          </Button>
        </div>
      </div>
    </Link>
  );
}
