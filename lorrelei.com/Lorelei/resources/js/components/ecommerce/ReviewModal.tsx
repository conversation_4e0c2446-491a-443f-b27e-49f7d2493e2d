import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Star, Upload, X, Loader2 } from 'lucide-react';
import { ReviewService } from '@/services/ReviewService';
import { toast } from '@/hooks/use-toast';
import { useTranslation } from '@/hooks/use-translation';

interface ReviewModalProps {
  productId: string;
  productName: string;
  onReviewAdded: () => void;
}

export default function ReviewModal({ productId, productName, onReviewAdded }: ReviewModalProps) {
  const [open, setOpen] = useState(false);
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [images, setImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hoverRating, setHoverRating] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const reviewService = new ReviewService();
  const { t } = useTranslation();

  const handleRatingClick = (value: number) => {
    setRating(value);
  };

  const handleRatingHover = (value: number) => {
    setHoverRating(value);
  };

  const handleRatingLeave = () => {
    setHoverRating(0);
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newImages = Array.from(e.target.files);

      // Limiter à 5 images maximum
      const totalImages = [...images, ...newImages];
      if (totalImages.length > 5) {
        toast({
          title: t('reviews.image_limit_reached'),
          description: t('reviews.image_limit_message'),
          variant: "destructive",
        });
        return;
      }

      setImages([...images, ...newImages]);

      // Créer des URLs pour les aperçus
      const newImageUrls = newImages.map(file => URL.createObjectURL(file));
      setImagePreviewUrls([...imagePreviewUrls, ...newImageUrls]);
    }
  };

  const removeImage = (index: number) => {
    // Libérer l'URL de l'aperçu
    URL.revokeObjectURL(imagePreviewUrls[index]);

    // Supprimer l'image et son aperçu
    setImages(images.filter((_, i) => i !== index));
    setImagePreviewUrls(imagePreviewUrls.filter((_, i) => i !== index));
  };

  const resetForm = () => {
    setName('');
    setEmail('');
    setRating(0);
    setComment('');

    // Libérer les URLs des aperçus
    imagePreviewUrls.forEach(url => URL.revokeObjectURL(url));

    setImages([]);
    setImagePreviewUrls([]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (rating === 0) {
      toast({
        title: t('reviews.rating_required'),
        description: t('reviews.select_rating'),
        variant: "destructive",
      });
      return;
    }

    if (comment.length < 10) {
      toast({
        title: t('reviews.comment_too_short'),
        description: t('reviews.comment_min_length'),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      await reviewService.addReview(productId, {
        name,
        email,
        rating,
        comment,
        images,
      });

      toast({
        title: t('reviews.review_added'),
        description: t('reviews.review_added_success'),
        variant: "success",
      });

      resetForm();
      setOpen(false);
      onReviewAdded();
    } catch (error) {
      console.error('Erreur lors de l\'ajout de l\'avis:', error);
      toast({
        title: t('toast.error'),
        description: error instanceof Error ? error.message : t('reviews.review_error'),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          {t('reviews.write_review')}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>{t('reviews.write_review')} pour {productName}</DialogTitle>
            <DialogDescription>
              {t('reviews.write_review_description')}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">{t('reviews.your_name')}</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">{t('reviews.your_email')}</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label>{t('reviews.your_rating')}</Label>
              <div className="flex items-center gap-1">
                {[1, 2, 3, 4, 5].map((value) => (
                  <button
                    key={value}
                    type="button"
                    className="focus:outline-none"
                    onClick={() => handleRatingClick(value)}
                    onMouseEnter={() => handleRatingHover(value)}
                    onMouseLeave={handleRatingLeave}
                  >
                    <Star
                      className={`h-6 w-6 ${
                        value <= (hoverRating || rating)
                          ? 'fill-amber-500 text-amber-500'
                          : 'text-muted-foreground'
                      }`}
                    />
                  </button>
                ))}
                <span className="ml-2 text-sm text-muted-foreground">
                  {rating > 0 ? `${rating}/5` : t('reviews.select_rating')}
                </span>
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="comment">{t('reviews.your_comment')}</Label>
              <Textarea
                id="comment"
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                rows={4}
                required
                minLength={10}
              />
            </div>
            <div className="grid gap-2">
              <Label>{t('reviews.images')}</Label>
              <div className="flex flex-wrap gap-2">
                {imagePreviewUrls.map((url, index) => (
                  <div key={index} className="relative h-20 w-20">
                    <img
                      src={url}
                      alt={`Aperçu ${index + 1}`}
                      className="h-full w-full rounded-md object-cover"
                    />
                    <button
                      type="button"
                      className="absolute -right-2 -top-2 rounded-full bg-destructive p-1 text-destructive-foreground"
                      onClick={() => removeImage(index)}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
                {images.length < 5 && (
                  <button
                    type="button"
                    className="flex h-20 w-20 items-center justify-center rounded-md border border-dashed border-muted-foreground/50 hover:border-muted-foreground/80"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="h-6 w-6 text-muted-foreground" />
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      multiple
                      className="hidden"
                      onChange={handleImageChange}
                    />
                  </button>
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                {t('reviews.images_limit')}
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              {t('reviews.cancel')}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('reviews.sending')}
                </>
              ) : (
                t('reviews.submit')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
