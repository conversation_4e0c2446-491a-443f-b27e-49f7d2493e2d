import { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react';
import { Category } from '@/models/Category';
import { Product } from '@/models/Product';
import { CategoryService } from '@/services/CategoryService';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbTrailProps {
    category?: Category;
    product?: Product;
    className?: string;
}

export default function BreadcrumbTrail({ category, product, className = '' }: BreadcrumbTrailProps) {
    const [parentCategories, setParentCategories] = useState<Category[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const categoryService = new CategoryService();

    useEffect(() => {
        const fetchParentCategories = async () => {
            if (!category) return;
            setIsLoading(true);
            try {
                const parents = await categoryService.getCategoryParents(category.id);
                setParentCategories(parents);
            } catch (error) {
                console.error('Erreur lors de la récupération des catégories parentes:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchParentCategories();
    }, [category]);


    if (isLoading) {
        return (
            <nav className={`flex items-center space-x-2 text-sm text-muted-foreground ${className}`}>
                <div className="h-4 w-16 animate-pulse rounded bg-muted"></div>
                <ChevronRight className="h-4 w-4" />
                <div className="h-4 w-24 animate-pulse rounded bg-muted"></div>
            </nav>
        );
    }

    return (
        <nav className={`flex flex-wrap items-center space-x-2 text-sm text-muted-foreground ${className}`}>
            <Link href={route('home')} className="flex items-center hover:text-foreground">
                <Home className="mr-1 h-4 w-4" />
                Accueil
            </Link>

            <ChevronRight className="h-4 w-4" />

            <Link href={route('categories')} className="hover:text-foreground">
                Catégories
            </Link>

            {parentCategories.length > 0 && (
                <>
                    {parentCategories.map((parent, index) => (
                        <div key={parent.id} className="flex items-center">
                            <ChevronRight className="h-4 w-4" />
                            <Link
                                href={route('category', { categorySlug: parent.slug })}
                                className="hover:text-foreground"
                            >
                                {parent.name}
                            </Link>
                        </div>
                    ))}
                </>
            )}

            {category && (
                <>
                    <ChevronRight className="h-4 w-4" />
                    {product ? (
                        <Link
                            href={route('category', { categorySlug: category.slug })}
                            className="hover:text-foreground"
                        >
                            {category.name}
                        </Link>
                    ) : (
                        <span className="font-medium text-foreground">{category.name}</span>
                    )}
                </>
            )}

            {product && (
                <>
                    <ChevronRight className="h-4 w-4" />
                    <span className="font-medium text-foreground">{product.name}</span>
                </>
            )}
        </nav>
    );
}
