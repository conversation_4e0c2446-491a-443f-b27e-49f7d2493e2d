import { Category } from '@/models/Category';
import { Link } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import CategoriesSidebar from './CategoriesSidebar';

/**
 * Props pour le composant ListeCategories
 */
interface ListeCategoriesProps {
  categories: Category[];
  className?: string;
  variant?: 'grid' | 'list';
}

/**
 * Composant affichant une liste de catégories avec liens
 *
 * @param categories - Les catégories à afficher
 * @param className - Classes CSS additionnelles
 * @param variant - Variante d'affichage (grille ou liste)
 */
export default function ListeCategories({
  categories,
  className = '',
  variant = 'grid'
}: ListeCategoriesProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loadingImages, setLoadingImages] = useState<Record<string, boolean>>({});

  // Initialiser l'état de chargement pour chaque catégorie
  useEffect(() => {
    const initialLoadingState: Record<string, boolean> = {};
    categories.forEach(category => {
      if (category.imageUrl) {
        initialLoadingState[category.id] = true;
      }
    });
    setLoadingImages(initialLoadingState);
  }, [categories]);

  // Fonction pour marquer une image comme chargée
  const handleImageLoaded = (categoryId: string) => {
    setLoadingImages(prev => ({
      ...prev,
      [categoryId]: false
    }));
  };
  if (variant === 'grid') {
    return (
      <div className={`grid grid-cols-4 gap-4 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 ${className}`}>
        {categories.map((category) => (
          <Link
            key={category.id}
            href={route('category', { categorySlug: category.slug })}
            className="group overflow-hidden rounded-lg border transition-all hover:shadow-md"
          >
            <div className={`relative aspect-square overflow-hidden ${loadingImages[category.id] ? 'animate-pulse' : ''}`}>
              {category.imageUrl ? (
                <img
                  src={category.imageUrl}
                  alt={category.getTranslatedName()}
                  className={`h-full w-full object-cover transition-transform duration-300 group-hover:scale-105 ${loadingImages[category.id] ? 'opacity-70' : 'opacity-100'}`}
                  onLoad={() => handleImageLoaded(category.id)}
                  onError={() => handleImageLoaded(category.id)}
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center bg-muted">
                  <span className="text-2xl font-semibold text-muted-foreground">
                    {category.getTranslatedName().charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>
            <div className="p-3 text-center">
              <h3 className="font-medium text-[8px] sm:text-xs md:text-sm">{category.getTranslatedName()}</h3>
            </div>
          </Link>
        ))}
      </div>
    );
  }

  return (
    <>
      <CategoriesSidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className={`flex items-center ${className}`}>
        <div className="flex-1 overflow-x-auto whitespace-nowrap py-2 scrollbar-hide">
          <div className="flex space-x-4 px-1">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={route('category', { categorySlug: category.slug })}
                className="flex flex-col items-center rounded-md px-3 py-2 text-sm transition-colors hover:bg-muted"
              >
                {category.imageUrl ? (
                  <div className={`mb-1 h-10 w-10 rounded-full overflow-hidden ${loadingImages[category.id] ? 'animate-pulse' : ''}`}>
                    <img
                      src={category.imageUrl}
                      alt={category.getTranslatedName()}
                      className={`h-full w-full object-cover ${loadingImages[category.id] ? 'opacity-70' : 'opacity-100'}`}
                      onLoad={() => handleImageLoaded(category.id)}
                      onError={() => handleImageLoaded(category.id)}
                    />
                  </div>
                ) : (
                  <div className="mb-1 flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                    <span className="text-sm font-semibold text-muted-foreground">
                      {category.getTranslatedName().charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                <span>{category.getTranslatedName()}</span>
              </Link>
            ))}
          </div>
        </div>

        <button
          onClick={() => setSidebarOpen(true)}
          className="ml-2 flex h-10 w-10 items-center justify-center rounded-full hover:bg-gray-100"
          aria-label="Voir toutes les catégories"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </>
  );
}
