import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import { SizeGuide, SizeGuideService } from '@/services/SizeGuideService';
import { useTranslation } from '@/hooks/use-translation';

interface SizeGuideModalProps {
  productId?: string | number; // Rendu optionnel
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Composant modal pour afficher le guide des tailles
 */
export default function SizeGuideModal({ productId, isOpen, onClose }: SizeGuideModalProps) {
  const { translate, currentLocale } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sizeGuides, setSizeGuides] = useState<SizeGuide[]>([]);
  const [selectedGuide, setSelectedGuide] = useState<SizeGuide | null>(null);
  const [selectedSystem, setSelectedSystem] = useState<string>('standard');

  // Charger les guides des tailles au montage du composant
  useEffect(() => {
    if (isOpen) {
      loadSizeGuides();
    }
  }, [isOpen]);

  // Charger les guides des tailles depuis l'API
  const loadSizeGuides = async () => {
    try {
      setLoading(true);
      setError(null);

      // Utiliser getAllGuides au lieu de getForProduct
      const response = await SizeGuideService.getAllGuides();

      if (response && response.size_guides && Array.isArray(response.size_guides)) {
        setSizeGuides(response.size_guides);

        // Sélectionner le premier guide par défaut
        if (response.size_guides.length > 0) {
          const firstGuide = response.size_guides[0];
          setSelectedGuide(firstGuide);

          // Sélectionner le système standard par défaut, ou le premier disponible
          if (firstGuide.size_systems && Array.isArray(firstGuide.size_systems)) {
            const hasStandard = firstGuide.size_systems.some(system => system.code === 'standard');
            if (hasStandard) {
              setSelectedSystem('standard');
            } else if (firstGuide.size_systems.length > 0 && firstGuide.size_systems[0]?.code) {
              setSelectedSystem(firstGuide.size_systems[0].code);
            } else {
              setSelectedSystem('');
            }
          } else {
            setSelectedSystem('');
          }
        }
      } else {
        setSizeGuides([]);
        setSelectedGuide(null);
        setSelectedSystem('');
      }
    } catch (err) {
      console.error('Erreur lors du chargement des guides des tailles:', err);
      setError(translate('size_guide.error_loading'));
      setSizeGuides([]);
      setSelectedGuide(null);
      setSelectedSystem('');
    } finally {
      setLoading(false);
    }
  };
  // Gérer le changement de guide
  const handleGuideChange = (guideId: string) => {
    const guide = sizeGuides.find(g => g.id?.toString() === guideId);
    if (guide) {
      setSelectedGuide(guide);

      // Vérifier si le système actuellement sélectionné existe dans ce guide
      if (guide.size_systems && Array.isArray(guide.size_systems)) {
        const systemExists = guide.size_systems.some(system => system.code === selectedSystem);
        if (!systemExists && guide.size_systems.length > 0) {
          setSelectedSystem(guide.size_systems[0].code);
        }
      }
    }
  };

  // Si le modal n'est pas ouvert, ne rien afficher
  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{translate('size_guide.title')}</DialogTitle>
          <DialogDescription>
            {translate('size_guide.description')}
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">{translate('common.loading')}...</span>
          </div>
        ) : error ? (
          <div className="py-8 text-center text-red-500">
            <p>{error}</p>
            <Button
              variant="outline"
              onClick={loadSizeGuides}
              className="mt-4"
            >
              {translate('common.retry')}
            </Button>
          </div>
        ) : sizeGuides.length === 0 ? (
          <div className="py-8 text-center text-muted-foreground">
            <p>{translate('size_guide.no_guides_available')}</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Sélecteur de guide si plusieurs guides sont disponibles */}
            {sizeGuides.length > 1 && (
              <div className="space-y-2">
                <Label htmlFor="guide-selector">{translate('size_guide.select_guide')}</Label>
                <Select
                  value={selectedGuide?.id?.toString() || ''}
                  onValueChange={handleGuideChange}
                >
                  <SelectTrigger id="guide-selector">
                    <SelectValue placeholder={translate('size_guide.select_guide')} />
                  </SelectTrigger>
                  <SelectContent>
                    {sizeGuides.map(guide => (
                      <SelectItem key={guide.id} value={guide.id?.toString() || ''}>
                        {(currentLocale === 'fr' ? guide.name_fr : guide.name_en )}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {selectedGuide && (
              <>
                {/* Instructions de mesure */}
                <div className="rounded-lg border p-4">
                  <h3 className="mb-2 font-medium">{translate('size_guide.how_to_measure')}</h3>
                  <div
                    className="prose prose-sm max-w-none dark:prose-invert"
                    dangerouslySetInnerHTML={{ __html: (currentLocale === 'fr' ? selectedGuide.instructions_fr : selectedGuide.instructions_fr) || selectedGuide.instructions }}
                  />

                  {/* Image d'illustration */}
                  {selectedGuide.image && (
                    <div className="mt-4 flex justify-center">
                      <img
                        src={selectedGuide.image}
                        alt={translate('size_guide.measurement_illustration')}
                        className="max-h-60 object-contain"
                      />
                    </div>
                  )}
                </div>

                {/* Sélecteur de système de tailles */}
                {selectedGuide.size_systems && selectedGuide.size_systems.length > 0 ? (
                  <div className="space-y-2">
                    <Label htmlFor="system-selector">{translate('size_guide.size_system')}</Label>
                    <Select
                      value={selectedSystem}
                      onValueChange={setSelectedSystem}
                    >
                      <SelectTrigger id="system-selector">
                        <SelectValue placeholder={translate('size_guide.select_system')} />
                      </SelectTrigger>
                      <SelectContent>
                        {selectedGuide.size_systems.map(system => (
                          <SelectItem key={system.code} value={system.code}>
                            {currentLocale === 'fr' ? system.name_fr : system.name_en}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                ) : null}

                {/* Tableau des tailles */}
                <div className="overflow-x-auto">
                  {selectedGuide.size_chart && selectedGuide.size_chart.length > 0 ? (
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-muted">
                          <th className="border p-2 text-left">{translate('size_guide.size')}</th>
                          {selectedGuide.measurement_types && selectedGuide.measurement_types.map(type => (
                            <th key={type.code} className="border p-2 text-left">
                              {currentLocale === 'fr' ? type.name_fr : type.name_en} ({type.unit || 'cm'})
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {selectedGuide.size_chart.map(size => (
                          <tr key={size.size_code || 'unknown'} className="hover:bg-muted/50">
                            <td className="border p-2 font-medium">
                              {SizeGuideService.getSystemValue(size, selectedSystem) || size.size_code || size[selectedSystem]}
                            </td>
                            {selectedGuide.measurement_types && selectedGuide.measurement_types.map(type => (
                              <td key={type.code} className="border p-2">
                                {SizeGuideService.getMeasurementValue(size, type.code) ?? size.measurements[type.code]}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : (
                    <div className="py-4 text-center text-muted-foreground">
                      <p>{translate('size_guide.no_size_chart_available')}</p>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        )}

        <DialogFooter>
          <Button onClick={onClose}>{translate('common.close')}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
