import React, { useState, useEffect, useMemo } from 'react';
import { ProductVariant, ProductAttribute, ColorAttribute } from '@/models/ProductVariant';
import { Product } from '@/models/Product';
import { cn } from '@/lib/utils';
import { Check, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/hooks/use-translation';

interface ProductVariantSelectorProps {
    variants: ProductVariant[];
    basePrice: number;
    currency: string;
    onVariantChange?: (variant: ProductVariant | null) => void;
    className?: string;
    product?: Product; // Ajout du produit pour accéder à ses attributs
    onImageChange?: (imageUrl: string) => void; // Callback pour changer l'image affichée
    onAttributesSelected?: (selectedAttributes: {
        color: string | null;
        size: string | null;
        material: string | null;
    }) => void; // Callback pour informer des attributs sélectionnés
}

/**
 * Composant pour sélectionner une variante de produit
 */
export default function ProductVariantSelector({
    variants,
    basePrice,
    currency,
    onVariantChange,
    className,
    product,
    onImageChange,
    onAttributesSelected
}: ProductVariantSelectorProps) {
    const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
    const [showingVariantImages, setShowingVariantImages] = useState<boolean>(false);
    const [selectedColorHasImage, setSelectedColorHasImage] = useState<boolean>(false);
    const { translate } = useTranslation();

    // Utiliser useMemo pour optimiser les calculs coûteux qui ne changent que lorsque les dépendances changent

    // Regrouper les variantes par type d'attribut
    const colorVariants = useMemo(() =>
        variants.filter(v => v.attributes.some(a => a.type === 'couleur')),
        [variants]
    );

    const sizeVariants = useMemo(() =>
        variants.filter(v => v.attributes.some(a => a.type === 'taille')),
        [variants]
    );

    const materialVariants = useMemo(() =>
        variants.filter(v => v.attributes.some(a => a.type === 'matiere')),
        [variants]
    );

    // Extraire les attributs de couleur du produit principal
    const productColorAttributes = useMemo(() =>
        product?.attributes.filter(
            (attr): attr is ColorAttribute => attr.type === 'couleur'
        ) || [],
        [product]
    );

    // Extraire toutes les valeurs uniques pour chaque type d'attribut
    // Combiner les couleurs du produit principal et des variantes
    const variantColors = useMemo(() => Array.from(
        new Set(
            colorVariants.flatMap(v =>
                v.attributes
                    .filter((a): a is ColorAttribute => a.type === 'couleur')
                    .map(a => ({ nom: a.nom, code: a.code, fromVariant: true, variantId: v.id }))
            )
        )
    ), [colorVariants]);

    const productColors = useMemo(() => productColorAttributes.map(a => ({
        nom: a.nom,
        code: a.code,
        fromVariant: false,
        variantId: null
    })), [productColorAttributes]);

    const uniqueColors = useMemo(() =>
        [...productColors, ...variantColors],
        [productColors, variantColors]
    );

    // Extraire les tailles du produit principal
    const productSizeAttributes = useMemo(() =>
        product?.attributes.filter(
            attr => attr.type === 'taille' && 'valeur' in attr
        ) || [],
        [product]
    );

    const productSizes = useMemo(() =>
        productSizeAttributes.map(a => 'valeur' in a ? a.valeur : '').filter(Boolean),
        [productSizeAttributes]
    );

    const variantSizes = useMemo(() => Array.from(
        new Set(
            sizeVariants.flatMap(v =>
                v.attributes
                    .filter(a => a.type === 'taille')
                    .map(a => 'valeur' in a ? a.valeur : '')
            )
        )
    ).filter(Boolean), [sizeVariants]);

    const uniqueSizes = useMemo(() =>
        [...new Set([...productSizes, ...variantSizes])],
        [productSizes, variantSizes]
    );



    // Extraire les matières du produit principal
    const productMaterialAttributes = useMemo(() =>
        product?.attributes.filter(
            attr => attr.type === 'matiere' && 'valeur' in attr
        ) || [],
        [product]
    );

    const productMaterials = useMemo(() =>
        productMaterialAttributes.map(a => 'valeur' in a ? a.valeur : '').filter(Boolean),
        [productMaterialAttributes]
    );

    const variantMaterials = useMemo(() => Array.from(
        new Set(
            materialVariants.flatMap(v =>
                v.attributes
                    .filter(a => a.type === 'matiere')
                    .map(a => 'valeur' in a ? a.valeur : '')
            )
        )
    ).filter(Boolean), [materialVariants]);

    const uniqueMaterials = useMemo(() =>
        [...new Set([...productMaterials, ...variantMaterials])],
        [productMaterials, variantMaterials]
    );

    // État pour stocker les sélections de l'utilisateur
    const [selectedColor, setSelectedColor] = useState<string | null>(null);
    const [selectedSize, setSelectedSize] = useState<string | null>(null);
    const [selectedMaterial, setSelectedMaterial] = useState<string | null>(null);
    const [sizeRequired, setSizeRequired] = useState<boolean>(false);
    // Filtrer les tailles disponibles en fonction de la couleur sélectionnée
    const availableSizes = useMemo(() => {
        console.log("unique sizes = ", uniqueSizes)
        if (!selectedColor) return uniqueSizes;

        // Filtrer les variantes qui ont la couleur sélectionnée
        const variantsWithSelectedColor = variants.filter(v =>
            v.attributes.some(a => a.type === 'couleur' && 'nom' in a && a.nom === selectedColor)
        );

        // Extraire les tailles de ces variantes
        const sizesForSelectedColor = Array.from(
            new Set(
                variantsWithSelectedColor.flatMap(v =>
                    v.attributes
                        .filter(a => a.type === 'taille')
                        .map(a => 'valeur' in a ? a.valeur : '')
                )
            )
        ).filter(Boolean);

        // Si la couleur sélectionnée est une couleur du produit principal, inclure aussi les tailles du produit
        const isProductColor = productColors.some(c => c.nom === selectedColor);
        if (isProductColor) {
            return [...new Set([...productSizes, ...sizesForSelectedColor])];
        }

        return sizesForSelectedColor;
    }, [selectedColor, variants, productSizes, productColors, uniqueSizes]);

    // Sélectionner automatiquement la première couleur du produit principal au chargement
    useEffect(() => {
        if (productColors.length > 0 && !selectedColor) {
            setSelectedColor(productColors[0].nom);
        } else if (uniqueColors.length > 0 && !selectedColor) {
            // Si pas de couleur dans le produit principal, sélectionner la première couleur disponible
            setSelectedColor(uniqueColors[0].nom);
        }
    }, [productColors, uniqueColors, selectedColor]);

    // Informer le parent des attributs sélectionnés lorsqu'ils changent
    useEffect(() => {
        if (onAttributesSelected) {
            onAttributesSelected({
                color: selectedColor,
                size: selectedSize,
                material: selectedMaterial
            });
        }
    }, [selectedColor, selectedSize, selectedMaterial, onAttributesSelected]);

    // Fonction pour revenir aux images du produit principal
    const resetToProductImages = () => {
        if (product && product.mainImageUrls.length > 0 && onImageChange) {
            onImageChange(product.mainImageUrls[0]);
            setShowingVariantImages(false);
        }
    };

    // Mettre à jour la variante sélectionnée lorsque les sélections changent
    useEffect(() => {
        // Si aucune variante n'est disponible, réinitialiser la sélection
        if (!variants.length) {
            setSelectedVariant(null);
            if (onVariantChange) onVariantChange(null);
            return;
        }

        // Filtrer les variantes qui correspondent aux sélections
        let filteredVariants = [...variants];

        if (selectedColor) {
            filteredVariants = filteredVariants.filter(v =>
                v.attributes.some(a =>
                    a.type === 'couleur' && 'nom' in a && a.nom === selectedColor
                )
            );

            // Si une couleur est sélectionnée, vérifier si elle appartient à une variante
            const selectedColorInfo = uniqueColors.find(c => c.nom === selectedColor);

            if (selectedColorInfo) {
                // Vérifier si la couleur a une image
                const colorVariant = selectedColorInfo.fromVariant && selectedColorInfo.variantId
                    ? variants.find(v => v.id === selectedColorInfo.variantId)
                    : null;

                const colorAttribute = colorVariant
                    ? colorVariant.attributes.find((a): a is ColorAttribute => a.type === 'couleur' && a.nom === selectedColor)
                    : product?.attributes.find((a): a is ColorAttribute => a.type === 'couleur' && a.nom === selectedColor);

                const hasImage = colorAttribute && 'with_image' in colorAttribute && colorAttribute.with_image;
                setSelectedColorHasImage(!!hasImage);

                // Par défaut, on reste sur les images du produit principal
                // On ne change pour les images de la variante que si l'utilisateur clique sur une variante
                // ou si le produit principal n'a pas d'images
                if (!showingVariantImages) {
                    if (product && product.mainImageUrls.length > 0 && onImageChange) {
                        onImageChange(product.mainImageUrls[0]);
                    } else if (colorVariant && colorVariant.imageUrls.length > 0 && onImageChange) {
                        // Si le produit n'a pas d'images, utiliser celles de la variante
                        onImageChange(colorVariant.imageUrls[0]);
                        setShowingVariantImages(true);
                    }
                }
            }
        }

        if (selectedSize) {
            filteredVariants = filteredVariants.filter(v =>
                v.attributes.some(a =>
                    a.type === 'taille' && 'valeur' in a && a.valeur === selectedSize
                )
            );
            // Si une taille est sélectionnée, désactiver l'indicateur de taille requise
            setSizeRequired(false);
        } else {
            // Si aucune taille n'est sélectionnée mais qu'il y a des tailles disponibles,
            // activer l'indicateur de taille requise
            setSizeRequired(availableSizes.length > 0);
        }

        if (selectedMaterial) {
            filteredVariants = filteredVariants.filter(v =>
                v.attributes.some(a =>
                    a.type === 'matiere' && 'valeur' in a && a.valeur === selectedMaterial
                )
            );
        }

        // Sélectionner la première variante qui correspond à tous les critères
        // Mais seulement si une taille est sélectionnée (si des tailles sont disponibles)
        let newSelectedVariant = null;

        // Si nous avons des variantes filtrées et soit une taille est sélectionnée, soit aucune taille n'est disponible
        if (filteredVariants.length > 0 && (selectedSize || availableSizes.length === 0)) {
            newSelectedVariant = filteredVariants[0];
        }
        // Si aucune variante ne correspond mais que des attributs sont sélectionnés et appartiennent au produit principal
        else if (selectedSize && productSizes.includes(selectedSize)) {
            // Pas de variante sélectionnée, mais on a sélectionné un attribut du produit principal
            newSelectedVariant = null;
        }

        setSelectedVariant(newSelectedVariant);

        if (onVariantChange) {
            onVariantChange(newSelectedVariant);
        }
    }, [selectedColor, selectedSize, selectedMaterial, variants, onVariantChange, onImageChange, uniqueColors, product, showingVariantImages, availableSizes, productSizes]);

    if (variants.length === 0 && availableSizes.length === 0) {
        return null;
    }

    return (
        <div className={cn("space-y-4", className)}>
            {/* Sélecteur de couleur unifié (produit principal + variantes) */}
            {uniqueColors.length > 0 && (
                <div>
                    <div className="flex items-center justify-between">
                        <h3 className="mb-2 text-sm font-medium">{translate('common.color')}</h3>
                        {/* Bouton pour revenir aux images du produit principal si on affiche les images d'une variante */}
                        {showingVariantImages && product && product.mainImageUrls.length > 0 && (
                            <Button
                                variant="ghost"
                                size="sm"
                                className="mb-2 flex items-center gap-1 text-xs text-primary"
                                onClick={resetToProductImages}
                            >
                                <RefreshCw className="h-3 w-3" />
                                {translate('common.view_product_images')}
                            </Button>
                        )}
                    </div>
                    <div className="flex flex-wrap gap-2">
                        {uniqueColors.map((color, index) => {
                            const isSelected = selectedColor === color.nom;
                            // Ajouter une bordure spéciale pour les couleurs du produit principal
                            const isProductColor = !color.fromVariant;

                            // Trouver l'attribut de couleur correspondant
                            const colorVariant = color.fromVariant && color.variantId
                                ? variants.find(v => v.id === color.variantId)
                                : null;

                            const colorAttribute = colorVariant
                                ? colorVariant.attributes.find((a): a is ColorAttribute => a.type === 'couleur' && a.nom === color.nom)
                                : product?.attributes.find((a): a is ColorAttribute => a.type === 'couleur' && a.nom === color.nom);

                            const hasImage = colorAttribute && 'with_image' in colorAttribute && colorAttribute.with_image;
                            const colorImage = colorAttribute && 'color_image' in colorAttribute ? colorAttribute.color_image : null;

                            // Si la couleur a une image et que l'image existe, l'afficher
                            // Sinon, afficher la couleur
                            const handleColorClick = () => {
                                // Si la couleur est déjà sélectionnée, ne rien faire
                                if (isSelected) return;

                                // Sélectionner la couleur
                                setSelectedColor(color.nom);

                                // Si on clique sur une couleur de variante, afficher les images de la variante immédiatement
                                if (color.fromVariant && color.variantId) {
                                    const variant = variants.find(v => v.id === color.variantId);
                                    if (variant && variant.imageUrls.length > 0 && onImageChange) {
                                        onImageChange(variant.imageUrls[0]);
                                        setShowingVariantImages(true);
                                    }
                                }
                                // Si on clique sur une couleur du produit principal, afficher les images du produit
                                else if (!color.fromVariant) {
                                    resetToProductImages();
                                }
                            };

                            return (
                                <button
                                    key={index}
                                    type="button"
                                    className={cn(
                                        "relative flex h-10 w-10 items-center justify-center rounded-full border p-1",
                                        isSelected && "ring-2 ring-primary ring-offset-2",
                                        isProductColor && "border-2 border-amber-400"
                                    )}
                                    style={hasImage && colorImage ? {} : { backgroundColor: color.code }}
                                    onClick={handleColorClick}
                                    title={`${color.nom}${isProductColor ? ' (Couleur principale)' : ''}`}
                                >
                                    {hasImage && colorImage && (
                                        <img
                                            src={colorImage}
                                            alt={color.nom}
                                            className="h-full w-full rounded-full object-cover"
                                        />
                                    )}
                                    {isSelected && (
                                        <Check className="absolute h-5 w-5 text-white drop-shadow-md" />
                                    )}
                                    {isProductColor && (
                                        <span className="absolute -bottom-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-amber-400 text-[8px] font-bold text-amber-950">P</span>
                                    )}
                                </button>
                            );
                        })}
                    </div>
                </div>
            )}

            {/* Sélecteur de taille unifié (produit principal + variantes) */}
            {availableSizes.length > 0 && (
                <div>
                    <h3 className="mb-2 text-sm font-medium">
                        {translate('common.size')} <span className="text-red-500">*</span>
                        {sizeRequired && !selectedSize && <span className="ml-2 text-xs text-red-500">({translate('common.size_required_description')})</span>}
                    </h3>
                    <div className="flex flex-wrap gap-2">
                        {availableSizes.map((size, index) => {
                            const isSelected = selectedSize === size;
                            // Vérifier si cette taille appartient au produit principal
                            const isProductSize = productSizes.includes(size);
                            return (
                                <button
                                    key={index}
                                    type="button"
                                    className={cn(
                                        "relative rounded-md border px-3 py-1 text-sm",
                                        isSelected
                                            ? "border-primary bg-primary text-primary-foreground"
                                            : "border-input bg-background hover:bg-muted",
                                        sizeRequired && !selectedSize && "animate-pulse border-red-500",
                                        isProductSize && "border-amber-400"
                                    )}
                                    onClick={() => setSelectedSize(isSelected ? null : size)}
                                    title={`${size}${isProductSize ? ' (Taille principale)' : ''}`}
                                >
                                    {size}
                                    {isProductSize && (
                                        <span className="ml-1 inline-flex h-3 w-3 items-center justify-center rounded-full bg-amber-400 text-[6px] font-bold text-amber-950">P</span>
                                    )}
                                </button>
                            );
                        })}
                    </div>
                </div>
            )}

            {/* Sélecteur de matière unifié (produit principal + variantes) */}
            {uniqueMaterials.length > 0 && (
                <div>
                    <h3 className="mb-2 text-sm font-medium">{translate('common.material')}</h3>
                    <div className="flex flex-wrap gap-2">
                        {uniqueMaterials.map((material, index) => {
                            const isSelected = selectedMaterial === material;
                            // Vérifier si cette matière appartient au produit principal
                            const isProductMaterial = productMaterials.includes(material);
                            return (
                                <button
                                    key={index}
                                    type="button"
                                    className={cn(
                                        "relative rounded-md border px-3 py-1 text-sm",
                                        isSelected
                                            ? "border-primary bg-primary text-primary-foreground"
                                            : "border-input bg-background hover:bg-muted",
                                        isProductMaterial && "border-amber-400"
                                    )}
                                    onClick={() => setSelectedMaterial(isSelected ? null : material)}
                                    title={`${material}${isProductMaterial ? ' (Matière principale)' : ''}`}
                                >
                                    {material}
                                    {isProductMaterial && (
                                        <span className="ml-1 inline-flex h-3 w-3 items-center justify-center rounded-full bg-amber-400 text-[6px] font-bold text-amber-950">P</span>
                                    )}
                                </button>
                            );
                        })}
                    </div>
                </div>
            )}

            {/* Affichage de la variante sélectionnée */}
            {selectedVariant && (selectedSize || availableSizes.length === 0) && (
                <div className="mt-4 rounded-md bg-muted p-3">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium">{translate('common.selected_variant')}</p>
                            <p className="text-xs text-muted-foreground">
                                {/* Afficher uniquement les attributs sélectionnés par l'utilisateur */}
                                {selectedColor && `${translate('common.color')}: ${selectedColor}`}
                                {selectedSize && `, ${translate('common.size')}: ${selectedSize}`}
                                {selectedMaterial && `, ${translate('common.material')}: ${selectedMaterial}`}
                            </p>

                            {/* Bouton pour afficher les images de la variante */}
                            {selectedVariant.imageUrls.length > 0 && !showingVariantImages && (
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="mt-2 flex items-center gap-1 text-xs text-primary"
                                    onClick={() => {
                                        if (selectedVariant.imageUrls.length > 0 && onImageChange) {
                                            onImageChange(selectedVariant.imageUrls[0]);
                                            setShowingVariantImages(true);
                                        }
                                    }}
                                >
                                    <RefreshCw className="h-3 w-3" />
                                    {translate('common.view_variant_images')}
                                </Button>
                            )}
                        </div>
                        <div className="text-right">
                            <p className="text-sm font-medium">
                                {selectedVariant.formattedTotalPrice(basePrice, currency)}
                            </p>
                            <p className="text-xs text-muted-foreground">
                                {selectedVariant.inStock
                                    ? `${translate('common.in_stock')}: ${selectedVariant.stock}`
                                    : translate('common.out_of_stock')}
                            </p>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
