import React, { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface AddressFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  defaultType?: string;
  disableTypeSelection?: boolean;
  className?: string;
}

export default function AddressForm({
  onSuccess,
  onCancel,
  defaultType = 'Livraison',
  disableTypeSelection = false,
  className = ''
}: AddressFormProps) {
  const { data, setData, post, processing, errors, reset } = useForm({
    rue: '',
    ville: '',
    etat: '',
    pays: 'Cameroun',
    codePostal: '',
    type: defaultType,
  });

  const [formError, setFormError] = useState<string | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    post(route('my-account.addresses.add'), {
      onSuccess: () => {
        reset();
        if (onSuccess) onSuccess();
      },
      onError: (errors) => {
        if (Object.keys(errors).length === 0) {
          setFormError('Une erreur est survenue lors de l\'ajout de l\'adresse.');
        }
      },
    });
  };

  return (
    <form onSubmit={handleSubmit} className={`space-y-4 ${className}`}>
      {formError && (
        <Alert variant="destructive">
          <AlertDescription>{formError}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-2">
        <Label htmlFor="rue">Rue</Label>
        <Input
          id="rue"
          value={data.rue}
          onChange={(e) => setData('rue', e.target.value)}
          placeholder="123 Rue Exemple"
          required
        />
        {errors.rue && <p className="text-sm text-destructive">{errors.rue}</p>}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="ville">Ville</Label>
          <Input
            id="ville"
            value={data.ville}
            onChange={(e) => setData('ville', e.target.value)}
            placeholder="Yaoundé"
            required
          />
          {errors.ville && <p className="text-sm text-destructive">{errors.ville}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="codePostal">Code postal</Label>
          <Input
            id="codePostal"
            value={data.codePostal}
            onChange={(e) => setData('codePostal', e.target.value)}
            placeholder="2370001"
            required
          />
          {errors.codePostal && <p className="text-sm text-destructive">{errors.codePostal}</p>}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="etat">État / Région</Label>
        <Input
          id="etat"
          value={data.etat}
          onChange={(e) => setData('etat', e.target.value)}
          placeholder="Centre"
          required
        />
        {errors.etat && <p className="text-sm text-destructive">{errors.etat}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="pays">Pays</Label>
        <Input
          id="pays"
          value={data.pays}
          onChange={(e) => setData('pays', e.target.value)}
          placeholder="Cameroun"
          required
        />
        {errors.pays && <p className="text-sm text-destructive">{errors.pays}</p>}
      </div>


        <div className="space-y-2">
          <Label htmlFor="type">Type d'adresse</Label>
          <Select
            value={data.type}
            onValueChange={(value) => setData('type', value)}
            disabled={disableTypeSelection}
          >
            <SelectTrigger id="type">
              <SelectValue placeholder="Sélectionnez un type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Livraison">Livraison</SelectItem>
              <SelectItem value="Facturation">Facturation</SelectItem>
              <SelectItem value="Entreprise">Entreprise</SelectItem>
            </SelectContent>
          </Select>
          {errors.type && <p className="text-sm text-destructive">{errors.type}</p>}
        </div>
      

      <div className="flex justify-end gap-2 pt-2">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Annuler
          </Button>
        )}
        <Button type="submit" disabled={processing}>
          {processing ? 'Enregistrement...' : 'Enregistrer l\'adresse'}
        </Button>
      </div>
    </form>
  );
}
