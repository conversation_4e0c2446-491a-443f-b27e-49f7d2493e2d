import { useState, useEffect, useRef } from 'react';
import { Link } from '@inertiajs/react';
import { Category } from '@/models/Category';
import { CategoryService } from '@/services/CategoryService';
import { ChevronDown } from 'lucide-react';

interface CategoryDropdownProps {
  category: Category;
}

export default function CategoryDropdown({ category }: CategoryDropdownProps) {
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hoveredSubcategory, setHoveredSubcategory] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Gérer la fermeture du dropdown lorsque l'utilisateur clique en dehors
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Récupérer les sous-catégories lorsque le composant est monté
  useEffect(() => {
    const fetchSubcategories = async () => {
      setIsLoading(true);
      try {
        const categoryService = new CategoryService();
        const subCategories = await categoryService.getSubcategories(category.id);
        setSubcategories(subCategories);
      } catch (error) {
        console.error(`Erreur lors de la récupération des sous-catégories pour ${category.getTranslatedName()}:`, error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubcategories();
  }, [category.id]);

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Lien de la catégorie principale avec indicateur de dropdown */}
      <button
        className="flex items-center gap-1 py-3 text-sm font-medium hover:text-primary"
        onClick={() => setIsOpen(!isOpen)}
        onMouseEnter={() => setIsOpen(true)}
      >
        {category.getTranslatedName()}
        <ChevronDown className="h-4 w-4 transition-transform duration-200"
          style={{ transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)' }} />
      </button>

      {/* Dropdown des sous-catégories */}
      <div
        className={`absolute left-0 top-full z-50 w-screen max-w-screen-lg rounded-b-lg border bg-background p-4 shadow-lg transition-all duration-200 ${isOpen ? 'visible opacity-100' : 'invisible opacity-0'}`}
        onMouseLeave={() => setIsOpen(false)}
      >
        <div className="grid grid-cols-4 gap-6">
          {/* Colonne de gauche avec la liste des sous-catégories */}
          <div className="col-span-1 border-r pr-4">
            <h3 className="mb-3 font-semibold">{category.getTranslatedName()}</h3>
            <ul className="space-y-2">
              {isLoading ? (
                <li className="text-sm text-muted-foreground">Chargement...</li>
              ) : subcategories.length > 0 ? (
                <>
                  {subcategories.map((subcategory) => (
                    <li key={subcategory.id}>
                      <Link
                        href={route('category', { categorySlug: subcategory.slug })}
                        className={`block rounded-md px-3 py-2 text-sm transition-colors ${
                          hoveredSubcategory === subcategory.id
                            ? 'bg-primary/10 text-primary'
                            : 'hover:bg-muted'
                        }`}
                        onMouseEnter={() => setHoveredSubcategory(subcategory.id)}
                      >
                        {subcategory.getTranslatedName()}
                      </Link>
                    </li>
                  ))}
                  <li className="mt-4 border-t pt-2">
                    <Link
                      href={route('category', { categorySlug: category.slug })}
                      className="block rounded-md px-3 py-2 text-sm font-medium text-primary hover:bg-primary/10"
                    >
                      Voir toutes les sous-catégories
                    </Link>
                  </li>
                </>
              ) : (
                <li className="text-sm text-muted-foreground">Aucune sous-catégorie</li>
              )}
            </ul>
          </div>

          {/* Colonne de droite avec les images des sous-catégories */}
          <div className="col-span-3 grid grid-cols-3 gap-6">
            {isLoading ? (
              // Afficher des placeholders pendant le chargement
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div className="mb-2 h-32 w-32 animate-pulse rounded-full bg-muted"></div>
                  <div className="h-4 w-24 animate-pulse rounded bg-muted"></div>
                </div>
              ))
            ) : hoveredSubcategory ? (
              // Afficher la sous-catégorie survolée avec une mise en évidence
              subcategories
                .filter((subcategory) => subcategory.id === hoveredSubcategory)
                .map((subcategory) => (
                  <Link
                    key={subcategory.id}
                    href={route('category', { categorySlug: subcategory.slug })}
                    className="group flex flex-col items-center text-center"
                  >
                    <div className="mb-2 overflow-hidden rounded-full border-2 border-primary p-1 shadow-md transition-all">
                      {subcategory.imageUrl ? (
                        <img
                          src={subcategory.imageUrl}
                          alt={subcategory.getTranslatedName()}
                          className="h-40 w-40 rounded-full object-cover transition-transform group-hover:scale-105"
                          loading="lazy"
                        />
                      ) : (
                        <div className="flex h-40 w-40 items-center justify-center rounded-full bg-muted">
                          <span className="text-sm text-muted-foreground">Pas d'image</span>
                        </div>
                      )}
                    </div>
                    <span className="text-base font-medium text-primary">{subcategory.getTranslatedName()}</span>
                    <span className="mt-1 text-xs text-muted-foreground">Cliquez pour explorer</span>
                  </Link>
                ))
            ) : (
              // Afficher toutes les sous-catégories si aucune n'est survolée
              subcategories.slice(0, 6).map((subcategory) => (
                <Link
                  key={subcategory.id}
                  href={route('category', { categorySlug: subcategory.slug })}
                  className="group flex flex-col items-center text-center"
                  onMouseEnter={() => setHoveredSubcategory(subcategory.id)}
                >
                  <div className="mb-2 overflow-hidden rounded-full border p-1 transition-all group-hover:border-primary">
                    {subcategory.imageUrl ? (
                      <img
                        src={subcategory.imageUrl}
                        alt={subcategory.getTranslatedName()}
                        className="h-32 w-32 rounded-full object-cover transition-transform group-hover:scale-105"
                        loading="lazy"
                      />
                    ) : (
                      <div className="flex h-32 w-32 items-center justify-center rounded-full bg-muted">
                        <span className="text-sm text-muted-foreground">Pas d'image</span>
                      </div>
                    )}
                  </div>
                  <span className="text-sm font-medium">{subcategory.getTranslatedName()}</span>
                </Link>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
