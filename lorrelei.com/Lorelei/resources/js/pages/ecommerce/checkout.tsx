import { useState, useEffect } from 'react';
import { Head, usePage } from '@inertiajs/react';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import { useCart } from '@/contexts/CartContext';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { ArrowLeft, CreditCard, Truck, Home, Check, PlusCircle, Shield, Lock, MapPin, Smartphone, CreditCard as CreditCardIcon } from 'lucide-react';
import AddressForm from '@/components/ecommerce/AddressForm';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { useTranslation } from '@/hooks/use-translation';
import ZoneLivraisonSelector from '@/components/ecommerce/ZoneLivraisonSelector';
import DeliveryInfo from '@/components/ecommerce/DeliveryInfo';
import { useDelivery } from '@/contexts/DeliveryContext';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

interface CheckoutProps {
    adresses: Array<{
        id: string;
        rue: string;
        ville: string;
        etat: string;
        pays: string;
        codePostal: string;
        type: string;
    }>;
    user: {
        id: string;
        name: string;
        email: string;
    };
}

/**
 * Page de paiement (checkout)
 */
export default function Checkout() {
    const { items, formattedTotal, itemCount,
        formattedSubtotal,
        formattedGrandTotal,
        estimatedDeliveryTime
    } = useCart();
    const { selectedZone } = useDelivery();

    const [isLoading, setIsLoading] = useState(false);
    const { adresses, user } = usePage<CheckoutProps>().props;
    const [selectedAddress, setSelectedAddress] = useState<string | null>(null);
    const { translate } = useTranslation();
    const { toast } = useToast();
    const [loading, setLoading] = useState(true);

    // État pour la méthode de paiement sélectionnée
    const [paymentMethod, setPaymentMethod] = useState<'card' | 'paypal' | 'orange' | 'mtn'>('card');

    // État pour contrôler l'affichage du modal d'ajout d'adresse
    const [showAddressModal, setShowAddressModal] = useState(false);
    /**
     * Vérifie si tous les produits du panier proviennent du même marchand
     *
     * @returns {boolean} true si tous les produits ont le même marchand, false sinon
     */
    const allProductsFromSameMerchant = (): boolean => {
        if (items.length <= 1) return true;

        const firstSeller = items[0].product.seller;
        return items.every(item => item.product.seller === firstSeller);
    };

    /**
     * Calcule les frais de livraison totaux par marchand
     *
     * @returns {Object} Un objet avec les frais de livraison par marchand
     */
    const getDeliveryFeesByMerchant = () => {
        const feesByMerchant: Record<string, {
            fees: number,
            merchantName: string,
            zoneName: string | undefined
        }> = {};

        items.forEach(item => {
            if (item.deliveryInfo) {
                const sellerId = item.product.seller || 'unknown';

                if (!feesByMerchant[sellerId]) {
                    feesByMerchant[sellerId] = {
                        fees: 0,
                        merchantName: item.product.seller || translate('common.unknown_merchant'),
                        zoneName: item.deliveryInfo.zone_nom
                    };
                }

                feesByMerchant[sellerId].fees += item.getDeliveryFees();
            }
        });

        return feesByMerchant;
    };

    // Vérifier si tous les produits viennent du même marchand
    const sameMerchant = allProductsFromSameMerchant();

    // Obtenir les frais de livraison par marchand
    const deliveryFeesByMerchant = getDeliveryFeesByMerchant();


    // Sélectionner la première adresse de livraison par défaut
    useEffect(() => {
        if (adresses.length > 0 && !selectedAddress) {
            const shippingAddress = adresses.find(addr => addr.type === 'Livraison');
            if (shippingAddress) {
                setSelectedAddress(shippingAddress.id);
            } else {
                setSelectedAddress(adresses[0].id);
            }
        }
    }, [adresses, selectedAddress]);

    // Vérifier les conditions pour accéder à la page de checkout
    useEffect(() => {
        // Rediriger vers la page d'accueil si le panier est vide
        if (items.length === 0) {
            toast({
                title: 'Information',
                description: 'Votre panier est vide.',
            });
            window.location.href = route('home');
            return;
        }

        // Afficher le modal d'ajout d'adresse si l'utilisateur n'a pas d'adresse
        if (!loading && adresses.length === 0) {
            toast({
                title: 'Information',
                description: 'Veuillez ajouter une adresse de livraison pour continuer.',
            });
            setShowAddressModal(true);
        }
    }, [items, adresses, loading, toast]);

    /**
     * Gère la soumission du formulaire de paiement
     */
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);

        // Simuler un traitement de paiement
        setTimeout(() => {
            setIsLoading(false);

            // Message différent selon la méthode de paiement
            let message = '';
            switch (paymentMethod) {
                case 'card':
                    message = 'Le paiement par carte bancaire n\'est pas encore implémenté.';
                    break;
                case 'paypal':
                    message = 'La redirection vers PayPal n\'est pas encore implémentée.';
                    break;
                case 'orange':
                    message = 'Le paiement par Orange Money n\'est pas encore implémenté.';
                    break;
                case 'mtn':
                    message = 'Le paiement par MTN Mobile Money n\'est pas encore implémenté.';
                    break;
                default:
                    message = 'Cette fonctionnalité n\'est pas encore implémentée.';
            }

            toast({
                title: "Information",
                description: message,
            });
        }, 1500);
    };

    return (
        <EcommerceLayout>
            <Head title="Paiement" />

            <div className="container mx-auto px-4 py-8">
                <div className="mb-6">
                    <Link href={route('cart')} className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 hover:dark:text-gray-200">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Retour au panier
                    </Link>
                    <h1 className="mt-2 text-2xl font-bold md:text-3xl">Finaliser votre commande</h1>
                </div>

                <div className="grid gap-8 md:grid-cols-3">
                    {/* Formulaire de paiement */}
                    <div className="md:col-span-2">
                        <div className="rounded-lg border bg-card p-6 shadow-sm">
                            <form onSubmit={handleSubmit}>
                                {/* Étapes du checkout */}
                                <div className="mb-8">
                                    <div className="mb-6">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white ">
                                                    <Home className="h-4 w-4 dark:text-black dark:bg-white" />
                                                </div>
                                                <h2 className="ml-3 text-lg font-medium">Adresse de livraison</h2>
                                            </div>

                                            <Dialog>
                                                <DialogTrigger asChild>
                                                    <Button variant="outline" size="sm" className="flex items-center gap-1">
                                                        <PlusCircle className="h-4 w-4" />
                                                        <span>Ajouter</span>
                                                    </Button>
                                                </DialogTrigger>
                                                <DialogContent>
                                                    <DialogHeader>
                                                        <DialogTitle>Ajouter une adresse de livraison</DialogTitle>
                                                    </DialogHeader>
                                                    <AddressForm
                                                        defaultType="Livraison"
                                                        disableTypeSelection={true}
                                                        onSuccess={() => window.location.reload()}
                                                    />
                                                </DialogContent>
                                            </Dialog>
                                        </div>

                                        {adresses.length > 0 ? (
                                            <div className="mt-4 space-y-3">
                                                {adresses.map((address) => (
                                                    <div
                                                        key={address.id}
                                                        className={`rounded-md border p-4 ${selectedAddress === address.id ? 'border-primary bg-primary/5' : ''}`}
                                                        onClick={() => setSelectedAddress(address.id)}
                                                    >
                                                        <div className="flex items-center justify-between">
                                                            <div>
                                                                <p className="font-medium">{user.name}</p>
                                                                <p className="text-sm text-gray-600">{address.rue}</p>
                                                                <p className="text-sm text-gray-600">{address.codePostal} {address.ville}, {address.pays}</p>
                                                                <p className="mt-1 text-xs text-muted-foreground">Type: {address.type}</p>
                                                            </div>
                                                            {selectedAddress === address.id && (
                                                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-white">
                                                                    <Check className="h-3 w-3" />
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="mt-4 flex flex-col items-center justify-center rounded-md border border-dashed p-6 text-center">
                                                <Home className="mb-2 h-8 w-8 text-muted-foreground dark:text-black dark:bg-white" />
                                                <p className="mb-2 text-sm font-medium">Aucune adresse de livraison</p>
                                                <p className="mb-4 text-xs text-muted-foreground">
                                                    Veuillez ajouter une adresse de livraison pour continuer.
                                                </p>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="flex items-center gap-1"
                                                    onClick={() => setShowAddressModal(true)}
                                                >
                                                    <PlusCircle className="h-4 w-4" />
                                                    <span>Ajouter une adresse</span>
                                                </Button>
                                            </div>
                                        )}
                                    </div>

                                    <div className="mb-6">
                                        <div className="flex items-center">
                                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">
                                                <Truck className="h-4 w-4 dark:text-black dark:bg-white" />
                                            </div>
                                            <h2 className="ml-3 text-lg font-medium">Mode de livraison</h2>
                                        </div>
                                        <div className="mt-4 space-y-3">
                                            <div className="flex items-center rounded-md border p-3">
                                                <input
                                                    type="radio"
                                                    id="standard"
                                                    name="shipping"
                                                    className="h-4 w-4 text-primary"
                                                    defaultChecked
                                                />
                                                <label htmlFor="standard" className="ml-3 flex-1">
                                                    <span className="font-medium">Livraison standard</span>
                                                    <p className="text-sm text-gray-600">3-5 jours ouvrés</p>
                                                </label>
                                                <span className="font-medium">Gratuit</span>
                                            </div>
                                            <div className="flex items-center rounded-md border p-3">
                                                <input
                                                    type="radio"
                                                    id="express"
                                                    name="shipping"
                                                    className="h-4 w-4 text-primary"
                                                />
                                                <label htmlFor="express" className="ml-3 flex-1">
                                                    <span className="font-medium">Livraison express</span>
                                                    <p className="text-sm text-gray-600">1-2 jours ouvrés</p>
                                                </label>
                                                <span className="font-medium">9,99 €</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="mb-6">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">
                                                    <Truck className="h-4 w-4 dark:text-black dark:bg-white" />
                                                </div>
                                                <h2 className="ml-3 text-lg font-medium">Zones de livraison</h2>
                                            </div>
                                        </div>

                                        <div className="mt-4 space-y-3">
                                            <div className="rounded-md border p-4">
                                                {/* Afficher les zones de livraison par marchand */}
                                                {Object.keys(deliveryFeesByMerchant).length > 0 ? (
                                                    <div className="space-y-4">
                                                        <h3 className="text-sm font-medium">Vos produits seront livrés dans les zones suivantes :</h3>

                                                        {Object.entries(deliveryFeesByMerchant).map(([merchantId, info]) => (
                                                            <div key={merchantId} className="space-y-2 rounded-md border p-3">
                                                                <div className="flex items-center justify-between">
                                                                    <span className="text-sm font-medium">
                                                                        {sameMerchant
                                                                            ? 'Tous vos produits'
                                                                            : `Produits de ${info.merchantName}`}
                                                                    </span>
                                                                </div>

                                                                {info.zoneName && (
                                                                    <div className="flex items-center justify-between">
                                                                        <span className="text-sm">Zone de livraison :</span>
                                                                        <span className="text-sm font-medium">{info.zoneName}</span>
                                                                    </div>
                                                                )}

                                                                <div className="flex items-center justify-between">
                                                                    <span className="text-sm">Frais de livraison :</span>
                                                                    <span className="text-sm font-medium">
                                                                        {info.fees.toLocaleString('fr-FR', { minimumFractionDigits: 0, maximumFractionDigits: 0 })} FCFA
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <div className="rounded-md bg-yellow-50 p-3 text-sm text-yellow-800">
                                                        <p>Aucune information de livraison disponible pour vos produits.</p>
                                                        <p>Veuillez retourner au panier et sélectionner des zones de livraison pour vos produits.</p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <div className="flex items-center">
                                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">
                                                <CreditCard className="h-4 w-4 dark:text-black dark:bg-white" />
                                            </div>
                                            <h2 className="ml-3 text-lg font-medium">Paiement</h2>
                                        </div>
                                        <div className="mt-4 rounded-md border p-4">
                                            <RadioGroup
                                                value={paymentMethod}
                                                onValueChange={(value) => setPaymentMethod(value as 'card' | 'paypal' | 'orange' | 'mtn')}
                                                className="space-y-3"
                                            >
                                                {/* Option Carte Bancaire */}
                                                <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-gray-50 hover:dark:text-black cursor-pointer">
                                                    <RadioGroupItem value="card" id="card"/>
                                                    <Label htmlFor="card" className="flex flex-1 items-center cursor-pointer">
                                                        <div className="flex items-center space-x-2">
                                                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-50 text-blue-600">
                                                                <CreditCard className="h-4 w-4" />
                                                            </div>
                                                            <div>
                                                                <span className="font-medium">Carte bancaire</span>
                                                                <p className="text-xs text-gray-500">Visa, Mastercard, etc.</p>
                                                            </div>
                                                        </div>
                                                    </Label>
                                                    <div className="flex space-x-1">
                                                        <img src="/images/payment/visa.png" alt="Visa" className="h-6" />
                                                        <img src="/images/payment/mastercard.png" alt="Mastercard" className="h-6" />
                                                    </div>
                                                </div>

                                                {/* Option PayPal */}
                                                <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-gray-50 hover:dark:text-black cursor-pointer">
                                                    <RadioGroupItem value="paypal" id="paypal" />
                                                    <Label htmlFor="paypal" className="flex flex-1 items-center cursor-pointer">
                                                        <div className="flex items-center space-x-2">
                                                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-50 text-blue-600">
                                                                <img src="/images/payment/paypal.png" alt="PayPal" className="h-5" />
                                                            </div>
                                                            <div>
                                                                <span className="font-medium">PayPal</span>
                                                                <p className="text-xs text-gray-500">Paiement sécurisé en ligne</p>
                                                            </div>
                                                        </div>
                                                    </Label>
                                                </div>

                                                {/* Option Orange Money */}
                                                <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-gray-50 hover:dark:text-black cursor-pointer">
                                                    <RadioGroupItem value="orange" id="orange" />
                                                    <Label htmlFor="orange" className="flex flex-1 items-center cursor-pointer">
                                                        <div className="flex items-center space-x-2">
                                                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange-50 text-orange-600">
                                                                <img src="/images/payment/orange-money.png" alt="Orange Money" className="h-5" />
                                                            </div>
                                                            <div>
                                                                <span className="font-medium">Orange Money</span>
                                                                <p className="text-xs text-gray-500">Paiement mobile</p>
                                                            </div>
                                                        </div>
                                                    </Label>
                                                </div>

                                                {/* Option MTN Mobile Money */}
                                                <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-gray-50 hover:dark:text-black cursor-pointer">
                                                    <RadioGroupItem value="mtn" id="mtn" />
                                                    <Label htmlFor="mtn" className="flex flex-1 items-center cursor-pointer">
                                                        <div className="flex items-center space-x-2">
                                                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-yellow-50 text-yellow-600">
                                                                <img src="/images/payment/mobile-money.png" alt="MTN Money" className="h-5" />
                                                            </div>
                                                            <div>
                                                                <span className="font-medium">MTN Mobile Money</span>
                                                                <p className="text-xs text-gray-500">Paiement mobile</p>
                                                            </div>
                                                        </div>
                                                    </Label>
                                                </div>
                                            </RadioGroup>

                                            {/* Formulaire spécifique selon la méthode de paiement sélectionnée */}
                                            <div className="mt-4 border-t pt-4">
                                                {paymentMethod === 'card' && (
                                                    <div className="space-y-4">
                                                        <div>
                                                            <label htmlFor="cardNumber" className="mb-1 block text-sm font-medium">
                                                                Numéro de carte
                                                            </label>
                                                            <input
                                                                type="text"
                                                                id="cardNumber"
                                                                placeholder="1234 5678 9012 3456"
                                                                className="w-full rounded-md border border-gray-300 p-2 text-sm"
                                                            />
                                                        </div>
                                                        <div className="grid grid-cols-2 gap-4">
                                                            <div>
                                                                <label htmlFor="expiry" className="mb-1 block text-sm font-medium">
                                                                    Date d'expiration
                                                                </label>
                                                                <input
                                                                    type="text"
                                                                    id="expiry"
                                                                    placeholder="MM/AA"
                                                                    className="w-full rounded-md border border-gray-300 p-2 text-sm"
                                                                />
                                                            </div>
                                                            <div>
                                                                <label htmlFor="cvc" className="mb-1 block text-sm font-medium">
                                                                    CVC
                                                                </label>
                                                                <input
                                                                    type="text"
                                                                    id="cvc"
                                                                    placeholder="123"
                                                                    className="w-full rounded-md border border-gray-300 p-2 text-sm"
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}

                                                {paymentMethod === 'paypal' && (
                                                    <div className="text-center p-4">
                                                        <p className="text-sm mb-2">Vous serez redirigé vers PayPal pour finaliser votre paiement.</p>
                                                        <img src="/images/payment/paypal.png" alt="PayPal" className="h-10 mx-auto" />
                                                    </div>
                                                )}

                                                {paymentMethod === 'orange' && (
                                                    <div className="space-y-4">
                                                        <div>
                                                            <label htmlFor="orangeNumber" className="mb-1 block text-sm font-medium">
                                                                Numéro Orange Money
                                                            </label>
                                                            <input
                                                                type="text"
                                                                id="orangeNumber"
                                                                placeholder="07X XX XX XX"
                                                                className="w-full rounded-md border border-gray-300 p-2 text-sm"
                                                            />
                                                        </div>
                                                        <p className="text-xs text-gray-500">
                                                            Vous recevrez un code de confirmation par SMS pour valider votre paiement.
                                                        </p>
                                                    </div>
                                                )}

                                                {paymentMethod === 'mtn' && (
                                                    <div className="space-y-4">
                                                        <div>
                                                            <label htmlFor="mtnNumber" className="mb-1 block text-sm font-medium">
                                                                Numéro MTN Mobile Money
                                                            </label>
                                                            <input
                                                                type="text"
                                                                id="mtnNumber"
                                                                placeholder="05X XX XX XX"
                                                                className="w-full rounded-md border border-gray-300 p-2 text-sm"
                                                            />
                                                        </div>
                                                        <p className="text-xs text-gray-500">
                                                            Vous recevrez un code de confirmation par SMS pour valider votre paiement.
                                                        </p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <Button
                                    type="submit"
                                    className="w-full cursor-pointer"
                                    disabled={isLoading || adresses.length === 0 || !selectedAddress}
                                >
                                    {isLoading
                                        ? 'Traitement en cours...'
                                        : adresses.length === 0
                                            ? 'Ajoutez une adresse pour continuer'
                                            : Object.keys(deliveryFeesByMerchant).length === 0
                                                ? 'Retournez au panier pour sélectionner des zones de livraison'
                                                : 'Confirmer la commande'
                                    }
                                </Button>
                            </form>
                        </div>
                    </div>

                    {/* Récapitulatif de la commande */}
                    <div>
                        <div className="rounded-lg border bg-card p-6 shadow-sm">
                            <h2 className="mb-4 text-lg font-medium">Récapitulatif de la commande</h2>

                            <div className="mb-4 max-h-60 overflow-auto">
                                {items.map((item) => (
                                    <div key={item.product.id} className="mb-3 flex items-start">
                                        <div className="h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border">
                                            <img
                                                src={item.product.imageUrl}
                                                alt={item.product.name}
                                                className="h-full w-full object-cover"
                                            />
                                        </div>
                                        <div className="ml-3 flex-1">
                                            <p className="text-sm font-medium">{item.product.name}</p>
                                            <p className="text-xs text-gray-500">Quantité: {item.quantity}</p>
                                            <p className="text-sm font-medium">{item.formattedSubtotal()}</p>

                                            {/* Afficher la zone de livraison si disponible */}
                                            {item.deliveryInfo && item.deliveryInfo.zone_nom && (
                                                <div className="mt-1 flex items-center">
                                                    <MapPin className="h-3 w-3 mr-1 text-muted-foreground" />
                                                    <p className="text-xs text-muted-foreground">
                                                        Livraison: {item.deliveryInfo.zone_nom}
                                                        ({item.deliveryInfo.frais_livraison.toLocaleString('fr-FR', { minimumFractionDigits: 0, maximumFractionDigits: 0 })} FCFA)
                                                    </p>
                                                </div>
                                            )}

                                            {/* Afficher le marchand si disponible */}
                                            {item.product.seller && (
                                                <p className="text-xs text-muted-foreground">
                                                    Vendeur: {item.product.seller}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>

                            <div className="space-y-2 border-t pt-4">
                                <div className="flex justify-between text-sm">
                                    <span>Sous-total ({itemCount} articles)</span>
                                    <span>{formattedSubtotal}</span>
                                </div>
                                {/* Frais de livraison par marchand */}
                                {Object.keys(deliveryFeesByMerchant).length > 0 ? (
                                    <>
                                        {Object.entries(deliveryFeesByMerchant).map(([merchantId, info]) => (
                                            <div key={merchantId} className="flex justify-between text-sm">
                                                <span>
                                                    {sameMerchant
                                                        ? 'Livraison'
                                                        : `Livraison (${info.merchantName || 'Vendeur'})`}
                                                </span>
                                                <span>
                                                    {info.fees.toLocaleString('fr-FR', { minimumFractionDigits: 0, maximumFractionDigits: 0 })} FCFA
                                                </span>
                                            </div>
                                        ))}
                                    </>
                                ) : (
                                    <div className="flex justify-between text-sm">
                                        <span>Livraison</span>
                                        <span className="text-muted-foreground">Non disponible</span>
                                    </div>
                                )}
                                {selectedZone && estimatedDeliveryTime && (
                                    <div className="flex justify-between text-sm">
                                        <span>Délai de livraison</span>
                                        <span>
                                            {estimatedDeliveryTime.min === estimatedDeliveryTime.max
                                                ? `${estimatedDeliveryTime.min} jours`
                                                : `${estimatedDeliveryTime.min}-${estimatedDeliveryTime.max} jours`}
                                        </span>
                                    </div>
                                )}
                                <div className="flex justify-between font-medium">
                                    <span>Total</span>
                                    <span>{formattedGrandTotal}</span>
                                </div>
                            </div>

                            {/* Section Sécurité des paiements */}
                            <div className="mt-6 space-y-4">
                                <div className="border-t pt-4">
                                    <h3 className="mb-2 flex items-center text-sm font-medium">
                                        <Shield className="mr-2 h-4 w-4 text-primary" />
                                        Sécurité des paiements
                                    </h3>
                                    <p className="text-xs text-muted-foreground">
                                        Lorrelei s'engage à protéger vos informations de paiement et ne partage vos données de carte bancaire qu'avec nos prestataires de services de paiement qui se sont engagés à sauvegarder vos informations.
                                    </p>
                                </div>

                                {/* Logos des méthodes de paiement */}
                                <div className="flex flex-wrap items-center justify-center gap-3 py-2">
                                    <div className="flex h-8 w-12 items-center justify-center rounded bg-white p-1 shadow-sm">
                                        <img src="/images/payment/visa.png" alt="Visa" className="h-6" />
                                    </div>
                                    <div className="flex h-8 w-12 items-center justify-center rounded bg-white p-1 shadow-sm">
                                        <img src="/images/payment/mastercard.png" alt="Mastercard" className="h-6" />
                                    </div>
                                    <div className="flex h-8 w-12 items-center justify-center rounded bg-white p-1 shadow-sm">
                                        <img src="/images/payment/paypal.png" alt="PayPal" className="h-6" />
                                    </div>
                                    <div className="flex h-8 w-12 items-center justify-center rounded bg-white p-1 shadow-sm">
                                        <img src="/images/payment/orange-money.png" alt="Orange Money" className="h-6" />
                                    </div>
                                    <div className="flex h-8 w-12 items-center justify-center rounded bg-white p-1 shadow-sm">
                                        <img src="/images/payment/mobile-money.png" alt="MTN Mobile Money" className="h-6" />
                                    </div>
                                </div>

                                {/* Section Sécurité et confidentialité */}
                                <div className="border-t pt-4">
                                    <h3 className="mb-2 flex items-center text-sm font-medium">
                                        <Lock className="mr-2 h-4 w-4 text-primary" />
                                        Sécurité & Confidentialité
                                    </h3>
                                    <p className="text-xs text-muted-foreground">
                                        Vos données personnelles sont protégées par un cryptage SSL. Nous ne stockons pas vos informations de paiement et respectons strictement les normes de sécurité PCI DSS. Consultez notre politique de confidentialité pour plus d'informations sur la façon dont nous protégeons vos données.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Modal d'ajout d'adresse */}
            <Dialog open={showAddressModal} onOpenChange={setShowAddressModal}>
                <DialogContent className="sm:max-w-[500px]">
                    <DialogHeader>
                        <DialogTitle>Ajouter une adresse de livraison</DialogTitle>
                    </DialogHeader>
                    <AddressForm
                        defaultType="Livraison"
                        disableTypeSelection={true}
                        onSuccess={() => {
                            setShowAddressModal(false);
                            window.location.reload();
                        }}
                        onCancel={() => setShowAddressModal(false)}
                    />
                </DialogContent>
            </Dialog>
        </EcommerceLayout>
    );
}
