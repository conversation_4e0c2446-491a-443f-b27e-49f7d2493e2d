import { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import { Category } from '@/models/Category';
import { CategoryService } from '@/services/CategoryService';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import { Separator } from '@/components/ui/separator';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from '@/components/ui/breadcrumb';
import SubcategoryFilter from '@/components/ecommerce/SubcategoryFilter';

interface CategoryShowProps {
  categoryId: string;
  selectedSubcategories?: string[];
  initialCategory?: Category;
}

export default function CategoryShow({
  categoryId,
  selectedSubcategories = [],
  initialCategory,
}: CategoryShowProps) {
  const [category, setCategory] = useState<Category | null>(initialCategory || null);
  const [isLoading, setIsLoading] = useState(!initialCategory);
  const categoryService = new CategoryService();

  // Récupérer la catégorie si elle n'est pas fournie
  useEffect(() => {
    if (!initialCategory) {
      const fetchCategory = async () => {
        setIsLoading(true);
        try {
          const cat = await categoryService.getCategoryById(categoryId);
          setCategory(cat);
        } catch (error) {
          console.error(`Erreur lors de la récupération de la catégorie ${categoryId}:`, error);
        } finally {
          setIsLoading(false);
        }
      };

      fetchCategory();
    }
  }, [categoryId, initialCategory]);

  return (
    <EcommerceLayout>
      <Head title={category ? category.getTranslatedName() : 'Catégorie'} />

      <div className="container mx-auto px-4 py-8">
        {/* Fil d'Ariane */}
        <Breadcrumb className="mb-6">
          <BreadcrumbItem>
            <BreadcrumbLink href={route('home')}>Accueil</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href={route('categories')}>Catégories</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink>{isLoading ? 'Chargement...' : (category ? category.getTranslatedName() : '')}</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>

        {isLoading ? (
          <div className="mb-6 flex items-center gap-3">
            <div className="h-8 w-48 animate-pulse rounded bg-muted"></div>
          </div>
        ) : (
          <div className="mb-6">
            <h1 className="text-2xl font-bold">{category?.getTranslatedName()}</h1>
            {category?.description && (
              <p className="mt-2 text-muted-foreground">{category.getTranslatedDescription()}</p>
            )}
          </div>
        )}

        <Separator className="mb-8" />

        <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
          {/* Sidebar avec filtres */}
          <div className="md:col-span-1">
            <SubcategoryFilter
              categoryId={categoryId}
              selectedSubcategories={selectedSubcategories}
              className="sticky top-24"
            />
          </div>

          {/* Contenu principal */}
          <div className="md:col-span-3">
            {isLoading ? (
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {Array.from({ length: 6 }).map((_, index) => (
                  <div key={index} className="flex flex-col">
                    <div className="mb-3 h-48 animate-pulse rounded-lg bg-muted"></div>
                    <div className="mb-2 h-5 w-32 animate-pulse rounded bg-muted"></div>
                    <div className="h-4 w-24 animate-pulse rounded bg-muted"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-lg">
                  Contenu de la catégorie {category ? category.getTranslatedName() : ''}
                  {selectedSubcategories.length > 0 && (
                    <span className="ml-2 text-muted-foreground">
                      (Filtré par {selectedSubcategories.length} sous-catégorie
                      {selectedSubcategories.length > 1 ? 's' : ''})
                    </span>
                  )}
                </p>
                <p className="text-muted-foreground">
                  Ici s'afficheraient les produits de cette catégorie, filtrés par les sous-catégories
                  sélectionnées le cas échéant.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </EcommerceLayout>
  );
}
