import { Head, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import { useTranslation } from '@/hooks/use-translation';
import { FileQuestion } from 'lucide-react';

/**
 * Page 404 - Not Found
 *
 * Affichée lorsqu'une route demandée n'existe pas
 */
export default function NotFound() {
  const { translate } = useTranslation();

  return (
    <EcommerceLayout>
      <Head title={translate('page_not_found.title')} />
      <div className="container mx-auto flex min-h-[50vh] flex-col items-center justify-center px-4 py-16 text-center">
        <div className="mb-6 flex h-24 w-24 items-center justify-center rounded-full bg-muted">
          <FileQuestion className="h-12 w-12 text-muted-foreground" />
        </div>

        <h1 className="mb-2 text-4xl font-bold tracking-tight">
          {translate('page_not_found.heading')}
        </h1>

        <p className="mb-8 max-w-md text-muted-foreground">
          {translate('page_not_found.description')}
        </p>

        <div className="flex flex-col gap-4 sm:flex-row">
          <Button asChild size="lg">
            <Link href={route('home')}>
              {translate('page_not_found.go_home')}
            </Link>
          </Button>

          <Button variant="outline" size="lg" asChild>
            <Link href={route('products')}>
              {translate('page_not_found.browse_products')}
            </Link>
          </Button>
        </div>
      </div>
    </EcommerceLayout>
  );
}
