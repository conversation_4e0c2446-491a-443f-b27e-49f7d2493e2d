/* Styles pour le mega menu */

.mega-menu-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 100%;
  max-width: 1200px;
  background-color: white;
  border-radius: 0 0 0.5rem 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 50;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.2s ease;
  transform: translateY(-10px);
}

.mega-menu-container.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.mega-menu-category {
  position: relative;
  cursor: pointer;
}

.mega-menu-category:hover {
  color: var(--primary);
}

.mega-menu-category::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary);
  transition: width 0.2s ease;
}

.mega-menu-category:hover::after,
.mega-menu-category.active::after {
  width: 100%;
}

.mega-menu-subcategory {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.2s ease;
}

.mega-menu-subcategory:hover {
  transform: translateY(-5px);
}

.mega-menu-subcategory-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #e2e8f0;
  padding: 4px;
  transition: border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

.mega-menu-subcategory:hover .mega-menu-subcategory-image {
  border-color: var(--primary);
  transform: scale(1.05);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.mega-menu-subcategory-name {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.2s ease;
}

.mega-menu-subcategory:hover .mega-menu-subcategory-name {
  color: var(--primary);
}

/* Styles pour le mode sombre */
.dark .mega-menu-container {
  background-color: hsl(240 10% 3.9%);
  border-color: hsl(240 3.7% 15.9%);
}

.dark .mega-menu-subcategory-image {
  border-color: hsl(240 3.7% 15.9%);
}

/* Animation d'entrée pour le mega menu */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mega-menu-container.open {
  animation: fadeInUp 0.2s ease forwards;
}

/* Styles pour la barre de défilement personnalisée */
.scrollbar-styled {
  scrollbar-width: thin; /* Pour Firefox */
  scrollbar-color: var(--muted-foreground) transparent; /* Pour Firefox */
}

.scrollbar-styled::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-styled::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-styled::-webkit-scrollbar-thumb {
  background-color: var(--muted-foreground);
  border-radius: 20px;
  opacity: 0.5;
}

.scrollbar-styled::-webkit-scrollbar-thumb:hover {
  background-color: var(--primary);
}

/* Styles pour le mode sombre */
.dark .scrollbar-styled::-webkit-scrollbar-thumb {
  background-color: var(--muted-foreground);
}

.dark .scrollbar-styled::-webkit-scrollbar-thumb:hover {
  background-color: var(--primary);
}
