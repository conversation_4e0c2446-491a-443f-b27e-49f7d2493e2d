<x-filament::page>
    {{ $this->form }}

    <div class="flex flex-col gap-6 mt-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Mod<PERSON><PERSON> de fichiers -->
            <x-filament::section>
                <x-slot name="heading">Mod<PERSON><PERSON> de <PERSON>er</x-slot>
                <x-slot name="description">Téléchargez un modèle de fichier pour l'importation</x-slot>

                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-2">Modèle pour les produits</h3>
                    <a href="{{ route('filament.marchand.resources.importations.download-products-template') }}" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        Télécharger le modèle CSV
                    </a>
                </div>
            </x-filament::section>

            <!-- Actions d'importation -->
            <x-filament::section>
                <x-slot name="heading">Action d'importation</x-slot>
                <x-slot name="description">Lancez l'importation des produits</x-slot>

                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-2">Importer les produits</h3>
                    <button type="button" wire:click="importProducts" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        Lancer l'importation
                    </button>
                </div>
            </x-filament::section>
        </div>

        <!-- Instructions -->
        <x-filament::section>
            <x-slot name="heading">Instructions</x-slot>
            <x-slot name="description">Comment utiliser l'importation de produits</x-slot>

            <div>
                <h3 class="text-sm font-medium text-gray-700 mb-2">Importation de produits</h3>
                <ul class="list-disc ml-5 text-sm text-gray-600">
                    <li>Le fichier doit contenir les colonnes suivantes : nom_fr, nom_en, description_fr, description_en, categorie, prix, currency, stock, prix_remise, date_debut_remise, date_fin_remise, poids, longueur, largeur, hauteur, product_code, marque</li>
                    <li>Les colonnes nom_fr, categorie et prix sont obligatoires</li>
                    <li>Les noms et descriptions sont automatiquement gérés comme des champs traduisibles</li>
                    <li>La catégorie peut être spécifiée par son nom français ou anglais</li>
                    <li>La colonne currency permet de spécifier la devise (FCFA, EUR, USD, GBP, XAF, XOF). Si non spécifiée, FCFA sera utilisée par défaut</li>
                    <li>Les codes de devise sont automatiquement convertis en majuscules lors de l'importation</li>
                    <li>Les dates doivent être au format YYYY-MM-DD</li>
                    <li>Les produits existants sont mis à jour si le nom français correspond</li>
                    <li>Les prix peuvent utiliser le point ou la virgule comme séparateur décimal</li>
                    <li>Les images doivent être téléchargées séparément dans le dossier public correspondant</li>
                </ul>
            </div>
        </x-filament::section>
    </div>
</x-filament::page>
