# Guide de Séparation du Projet Lorrelei_Ecom

Ce guide détaille les étapes pour séparer le projet monolithique Lorrelei_Ecom (Laravel + Inertia + React) en deux projets distincts : un backend API (Laravel) et un frontend SPA (React).

## Préparation

1. Créez la structure de dossiers suivante dans le dossier parent Lorrelei_Ecom :
   ```
   Lorrelei_Ecom/
   ├── back_end/
   ├── front_end/
   └── Lorrelei/  (projet existant)
   ```

## Étape 1 : Préparation du Backend (API Laravel)

### 1.1 Initialiser le projet backend

1. Créez un nouveau projet Laravel dans le dossier back_end :
   ```bash
   cd Lorrelei_Ecom
   composer create-project laravel/laravel back_end
   ```

2. Configurez la base de données dans le fichier `.env` du backend :
   ```
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=Lorrelei_ecom
   DB_USERNAME=Lorrelei_user
   DB_PASSWORD=votre_mot_de_passe
   ```

### 1.2 Migrer les modèles

1. Copiez tous les modèles du dossier `Lorrelei/app/Models` vers `back_end/app/Models`

2. Assurez-vous que les espaces de noms sont correctement mis à jour dans chaque fichier

### 1.3 Migrer les migrations

1. Copiez toutes les migrations du dossier `Lorrelei/database/migrations` vers `back_end/database/migrations`

2. Exécutez les migrations :
   ```bash
   cd back_end
   php artisan migrate
   ```

### 1.4 Configurer l'authentification API

1. Installez Laravel Sanctum :
   ```bash
   cd back_end
   composer require laravel/sanctum
   php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
   php artisan migrate
   ```

2. Configurez Sanctum dans `app/Http/Kernel.php` :
   ```php
   'api' => [
       \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
       \Illuminate\Routing\Middleware\ThrottleRequests::class.':api',
       \Illuminate\Routing\Middleware\SubstituteBindings::class,
   ],
   ```

3. Configurez CORS dans `config/cors.php` :
   ```php
   return [
       'paths' => ['api/*', 'sanctum/csrf-cookie'],
       'allowed_methods' => ['*'],
       'allowed_origins' => ['http://localhost:3000'], // URL du frontend
       'allowed_origins_patterns' => [],
       'allowed_headers' => ['*'],
       'exposed_headers' => [],
       'max_age' => 0,
       'supports_credentials' => true,
   ];
   ```

### 1.5 Créer les ressources API

1. Créez des ressources API pour vos modèles principaux :
   ```bash
   cd back_end
   php artisan make:resource ProductResource
   php artisan make:resource CategoryResource
   # etc. pour chaque modèle important
   ```

2. Implémentez chaque ressource pour formater correctement les données

### 1.6 Créer les contrôleurs API

1. Créez des contrôleurs API pour vos fonctionnalités principales :
   ```bash
   cd back_end
   php artisan make:controller Api/ProductController --api
   php artisan make:controller Api/CategoryController --api
   php artisan make:controller Api/AuthController
   # etc. pour chaque fonctionnalité
   ```

2. Implémentez les méthodes CRUD dans chaque contrôleur

### 1.7 Définir les routes API

1. Configurez les routes API dans `routes/api.php` :
   ```php
   // Routes publiques
   Route::get('/products', [ProductController::class, 'index']);
   Route::get('/products/{product}', [ProductController::class, 'show']);
   Route::get('/categories', [CategoryController::class, 'index']);

   // Routes d'authentification
   Route::post('/login', [AuthController::class, 'login']);
   Route::post('/register', [AuthController::class, 'register']);

   // Routes protégées
   Route::middleware('auth:sanctum')->group(function () {
       Route::post('/logout', [AuthController::class, 'logout']);
       Route::apiResource('/user/addresses', AddressController::class);
       // Autres routes protégées
   });
   ```

### 1.8 Migrer les services et les helpers

1. Copiez les services, helpers et autres classes utilitaires du projet existant vers le backend

## Étape 2 : Préparation du Frontend (Laravel React)

### 2.1 Initialiser le projet frontend avec Laravel et React

1. Créez un nouveau projet Laravel dans le dossier front_end :
   ```bash
   cd Lorrelei_Ecom
   composer create-project laravel/laravel front_end
   cd front_end
   ```

2. Installez les dépendances React et Inertia :
   ```bash
   composer require inertiajs/inertia-laravel
   composer require laravel/breeze --dev
   php artisan breeze:install react
   npm install
   ```

3. Configurez Inertia dans le fichier `app/Http/Middleware/HandleInertiaRequests.php` :
   ```php
   public function share(Request $request): array
   {
       return array_merge(parent::share($request), [
           'auth' => [
               'user' => $request->user(),
           ],
           'locale' => app()->getLocale(),
           'translations' => $this->getTranslations(),
           'flash' => [
               'message' => fn () => $request->session()->get('message'),
               'status' => fn () => $request->session()->get('status'),
           ],
       ]);
   }

   private function getTranslations()
   {
       $locale = app()->getLocale();
       $path = resource_path("lang/{$locale}.json");

       return file_exists($path) ? json_decode(file_get_contents($path), true) : [];
   }
   ```

### 2.2 Migrer la structure des fichiers React

1. Copiez les fichiers React depuis le projet existant vers la nouvelle structure :
   ```
   # Structure source (projet existant)
   Lorrelei/resources/js/

   # Structure cible (nouveau frontend)
   front_end/resources/js/
   ```

2. Créez la structure de dossiers suivante dans le nouveau projet :
   ```
   front_end/resources/js/
   ├── Components/
   │   ├── Common/          # Composants réutilisables (boutons, inputs, etc.)
   │   ├── Layout/          # Composants de mise en page (Header, Footer, etc.)
   │   ├── Ecommerce/       # Composants spécifiques à l'e-commerce
   │   │   ├── Cart/        # Composants liés au panier
   │   │   ├── Product/     # Composants liés aux produits
   │   │   ├── Category/    # Composants liés aux catégories
   │   │   ├── Checkout/    # Composants liés au processus d'achat
   │   │   └── User/        # Composants liés à l'utilisateur
   │   └── UI/              # Composants d'interface utilisateur
   ├── Contexts/            # Contextes React pour la gestion d'état
   ├── Hooks/               # Hooks personnalisés
   ├── Pages/               # Pages principales de l'application
   │   ├── Home.jsx
   │   ├── Category.jsx
   │   ├── Product.jsx
   │   ├── Cart.jsx
   │   ├── Checkout.jsx
   │   ├── Account.jsx
   │   └── ...
   ├── Services/            # Services pour les appels API
   ├── Utils/               # Fonctions utilitaires
   └── app.jsx              # Point d'entrée de l'application
   ```

3. Copiez les fichiers statiques (images, polices, etc.) :
   ```
   # Copiez les assets
   Lorrelei/public/ → front_end/public/
   Lorrelei/resources/css/ → front_end/resources/css/
   ```

### 2.3 Migrer les composants React en détail

1. **Composants de mise en page** :
   - Copiez `Lorrelei/resources/js/Layouts/` vers `front_end/resources/js/Components/Layout/`
   - Adaptez le composant `EcommerceHeader.jsx` pour inclure :
     - Le menu de navigation
     - Le sélecteur de langue/devise
     - Le bouton de panier avec modal
     - Le menu utilisateur avec dropdown
   - Assurez-vous que le header est configuré comme "sticky"
   - Adaptez les styles pour les scrollbars personnalisés

2. **Composants de page** :
   - Copiez les pages depuis `Lorrelei/resources/js/Pages/` vers `front_end/resources/js/Pages/`
   - Pour chaque page, mettez à jour les imports et les références aux composants
   - Assurez-vous que les pages utilisent le bon layout

3. **Composants communs** :
   - Copiez les composants communs depuis `Lorrelei/resources/js/Components/` vers `front_end/resources/js/Components/Common/`
   - Incluez les composants de formulaire, boutons, modals, etc.
   - Assurez-vous que les composants sont adaptés pour le thème clair/sombre

4. **Composants spécifiques à l'e-commerce** :
   - Copiez et adaptez les composants suivants :
     - `CardProduit.jsx` pour l'affichage des produits
     - Composants de filtrage avec support pour les filtres automatiques
     - Composants de pagination avec sélecteur d'items par page
     - Composants de navigation d'images avec animations
     - Composants de panier avec modal

### 2.4 Migrer les contextes et la gestion d'état

1. Copiez et adaptez les contextes depuis `Lorrelei/resources/js/Contexts/` vers `front_end/resources/js/Contexts/` :
   ```jsx
   // Contexte d'authentification
   export const AuthContext = createContext();

   export const AuthProvider = ({ children }) => {
     const [user, setUser] = useState(null);
     const [loading, setLoading] = useState(true);

     // Fonctions d'authentification
     const login = async (credentials) => { /* ... */ };
     const logout = async () => { /* ... */ };
     const register = async (userData) => { /* ... */ };

     return (
       <AuthContext.Provider value={{ user, login, logout, register, loading }}>
         {children}
       </AuthContext.Provider>
     );
   };

   // Contexte de thème
   export const ThemeContext = createContext();

   export const ThemeProvider = ({ children }) => {
     const [theme, setTheme] = useState('system');

     // Logique de gestion du thème

     return (
       <ThemeContext.Provider value={{ theme, setTheme }}>
         {children}
       </ThemeContext.Provider>
     );
   };

   // Contexte de panier
   export const CartContext = createContext();

   export const CartProvider = ({ children }) => {
     const [cart, setCart] = useState([]);
     const [isOpen, setIsOpen] = useState(false);

     // Fonctions de gestion du panier

     return (
       <CartContext.Provider value={{ cart, addToCart, removeFromCart, isOpen, setIsOpen }}>
         {children}
       </CartContext.Provider>
     );
   };
   ```

2. Créez des hooks personnalisés pour utiliser ces contextes :
   ```jsx
   // hooks/useAuth.js
   export const useAuth = () => useContext(AuthContext);

   // hooks/useTheme.js
   export const useTheme = () => useContext(ThemeContext);

   // hooks/useCart.js
   export const useCart = () => useContext(CartContext);
   ```

### 2.5 Migrer les services API

1. Créez un service Axios pour communiquer avec le backend :
   ```jsx
   // Services/api.js
   import axios from 'axios';

   const api = axios.create({
     baseURL: process.env.MIX_API_URL || 'http://localhost:8000/api',
     withCredentials: true,
     headers: {
       'Content-Type': 'application/json',
       'Accept': 'application/json',
       'X-Requested-With': 'XMLHttpRequest',
     }
   });

   // Intercepteur pour gérer les erreurs et les tokens
   api.interceptors.response.use(
     response => response,
     error => {
       if (error.response && error.response.status === 401) {
         // Rediriger vers la page de connexion ou rafraîchir le token
       }
       return Promise.reject(error);
     }
   );

   export default api;
   ```

2. Créez des services spécifiques pour chaque entité :
   ```jsx
   // Services/productService.js
   import api from './api';

   export const getProducts = async (params) => {
     const response = await api.get('/products', { params });
     return response.data;
   };

   export const getProduct = async (id) => {
     const response = await api.get(`/products/${id}`);
     return response.data;
   };

   // Services/categoryService.js
   import api from './api';

   export const getCategories = async (params) => {
     const response = await api.get('/categories', { params });
     return response.data;
   };

   export const getCategory = async (id) => {
     const response = await api.get(`/categories/${id}`);
     return response.data;
   };

   // Services/authService.js
   import api from './api';

   export const login = async (credentials) => {
     await api.get('/sanctum/csrf-cookie');
     const response = await api.post('/login', credentials);
     return response.data;
   };

   export const register = async (userData) => {
     await api.get('/sanctum/csrf-cookie');
     const response = await api.post('/register', userData);
     return response.data;
   };

   export const logout = async () => {
     const response = await api.post('/logout');
     return response.data;
   };

   export const getUser = async () => {
     const response = await api.get('/user');
     return response.data;
   };

   // Services/cartService.js
   import api from './api';

   export const getCart = async () => {
     const response = await api.get('/cart');
     return response.data;
   };

   export const addToCart = async (productId, quantity, attributes) => {
     const response = await api.post('/cart/add', { product_id: productId, quantity, attributes });
     return response.data;
   };
   ```

### 2.6 Configurer le système de traduction

1. Copiez les fichiers de traduction depuis `Lorrelei/lang/ecommerce/` vers `front_end/resources/lang/` :
   ```
   Lorrelei/lang/ecommerce/en.json → front_end/resources/lang/en.json
   Lorrelei/lang/ecommerce/fr.json → front_end/resources/lang/fr.json
   ```

2. Configurez le système de traduction dans `resources/js/app.jsx` :
   ```jsx
   import { createInertiaApp } from '@inertiajs/react';
   import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
   import { I18nextProvider } from 'react-i18next';
   import i18n from './i18n';

   // Autres imports et providers

   createInertiaApp({
     resolve: (name) => resolvePageComponent(`./Pages/${name}.jsx`, import.meta.glob('./Pages/**/*.jsx')),
     setup({ el, App, props }) {
       return render(
         <I18nextProvider i18n={i18n}>
           <ThemeProvider>
             <AuthProvider>
               <CartProvider>
                 <App {...props} />
               </CartProvider>
             </AuthProvider>
           </ThemeProvider>
         </I18nextProvider>,
         el
       );
     },
   });
   ```

3. Créez le fichier de configuration i18n :
   ```jsx
   // resources/js/i18n.js
   import i18n from 'i18next';
   import { initReactI18next } from 'react-i18next';
   import Backend from 'i18next-http-backend';
   import LanguageDetector from 'i18next-browser-languagedetector';

   i18n
     .use(Backend)
     .use(LanguageDetector)
     .use(initReactI18next)
     .init({
       fallbackLng: 'fr',
       debug: process.env.NODE_ENV === 'development',
       interpolation: {
         escapeValue: false,
       },
       backend: {
         loadPath: '/lang/{{lng}}.json',
       },
     });

   export default i18n;
   ```

### 2.7 Configurer les routes et les contrôleurs

1. Configurez les routes dans `routes/web.php` :
   ```php
   <?php

   use App\Http\Controllers\ProfileController;
   use App\Http\Controllers\HomeController;
   use App\Http\Controllers\ProductController;
   use App\Http\Controllers\CategoryController;
   use App\Http\Controllers\CartController;
   use App\Http\Controllers\CheckoutController;
   use Illuminate\Support\Facades\Route;
   use Inertia\Inertia;

   // Routes publiques
   Route::get('/', [HomeController::class, 'index'])->name('home');
   Route::get('/products', [ProductController::class, 'index'])->name('products.index');
   Route::get('/products/{product}', [ProductController::class, 'show'])->name('products.show');
   Route::get('/categories/{category}', [CategoryController::class, 'show'])->name('categories.show');

   // Routes d'authentification
   Route::middleware('auth')->group(function () {
       Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
       Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
       Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

       Route::get('/checkout', [CheckoutController::class, 'index'])->name('checkout');
       Route::post('/checkout', [CheckoutController::class, 'store'])->name('checkout.store');
   });

   require __DIR__.'/auth.php';
   ```

2. Créez les contrôleurs correspondants qui retournent des vues Inertia :
   ```php
   // app/Http/Controllers/HomeController.php
   public function index()
   {
       $featuredProducts = Product::featured()->take(8)->get();
       $categories = Category::main()->with('subcategories')->get();
       $banners = Banner::active()->get();

       return Inertia::render('Home', [
           'featuredProducts' => ProductResource::collection($featuredProducts),
           'categories' => CategoryResource::collection($categories),
           'banners' => BannerResource::collection($banners),
       ]);
   }

   // app/Http/Controllers/ProductController.php
   public function show(Product $product)
   {
       $product->load(['category', 'variants', 'images']);

       return Inertia::render('Product', [
           'product' => new ProductResource($product),
           'relatedProducts' => ProductResource::collection(
               Product::where('category_id', $product->category_id)
                   ->where('id', '!=', $product->id)
                   ->take(4)
                   ->get()
           ),
       ]);
   }
   ```

### 2.8 Configurer les styles et le thème

1. Copiez les fichiers CSS et SCSS depuis `Lorrelei/resources/css/` vers `front_end/resources/css/`

2. Configurez Tailwind CSS pour le thème clair/sombre :
   ```js
   // tailwind.config.js
   module.exports = {
     darkMode: 'class',
     theme: {
       extend: {
         colors: {
           primary: { /* ... */ },
           secondary: { /* ... */ },
           // Autres couleurs personnalisées
         },
       },
     },
     plugins: [
       require('@tailwindcss/forms'),
       // Autres plugins
     ],
   };
   ```

3. Créez un hook pour gérer le thème :
   ```jsx
   // Hooks/useThemeMode.js
   import { useEffect } from 'react';
   import { useTheme } from './useTheme';

   export function useThemeMode() {
     const { theme, setTheme } = useTheme();

     useEffect(() => {
       const root = window.document.documentElement;

       if (theme === 'system') {
         const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
         root.classList.toggle('dark', systemTheme === 'dark');
       } else {
         root.classList.toggle('dark', theme === 'dark');
       }
     }, [theme]);

     return { theme, setTheme };
   }
   ```

### 2.9 Configurer les fonctionnalités spécifiques

1. **Panier avec modal** :
   - Créez un composant Modal pour le panier
   - Utilisez le contexte CartContext pour gérer l'état du panier
   - Implémentez les fonctions d'ajout/suppression d'articles

2. **Filtres de produits** :
   - Implémentez les filtres qui s'appliquent automatiquement
   - Ajoutez les paramètres d'URL pour les filtres
   - Créez des composants pour les filtres de prix, couleur, taille, etc.

3. **Lazy loading et infinite scrolling** :
   - Utilisez Intersection Observer API pour le lazy loading des images
   - Implémentez l'infinite scrolling pour les listes de produits

4. **Animations et transitions** :
   - Utilisez Framer Motion ou React Spring pour les animations
   - Ajoutez des transitions pour la navigation d'images
   - Implémentez des animations pour les modals et dropdowns

5. **Responsive design** :
   - Assurez-vous que tous les composants sont adaptés pour mobile
   - Placez les sélecteurs de langue/devise et le toggle d'apparence en bas sur mobile
   - Adaptez le menu pour mobile avec des catégories horizontales

## Étape 3 : Migration Progressive

### 3.1 Commencer par une fonctionnalité simple

1. Choisissez une fonctionnalité non critique (comme la consultation des catégories)
2. Implémentez-la complètement dans le backend et le frontend
3. Testez-la pour valider votre architecture

### 3.2 Migrer les fonctionnalités une par une

1. Produits et catégories
2. Authentification et gestion des utilisateurs
3. Panier d'achat
4. Commandes et paiements
5. Fonctionnalités administratives

### 3.3 Tests et validation

1. Testez chaque fonctionnalité après sa migration
2. Comparez les résultats avec l'application existante

## Étape 4 : Finalisation

### 4.1 Documentation

1. Documentez l'API avec Swagger/OpenAPI
2. Créez un guide pour les développeurs

### 4.2 Déploiement

1. Configurez les environnements de développement, staging et production
2. Mettez en place des pipelines CI/CD

## Conseils pour la migration

- **Gardez le projet existant fonctionnel** pendant la migration
- **Utilisez le versioning de l'API** pour faciliter les évolutions futures
- **Migrez les tests** en même temps que le code
- **Communiquez régulièrement** avec l'équipe sur l'avancement

## Ressources utiles

- [Documentation Laravel API](https://laravel.com/docs/10.x/eloquent-resources)
- [Documentation Laravel Sanctum](https://laravel.com/docs/10.x/sanctum)
- [Documentation React](https://react.dev/learn)
- [Documentation React Router](https://reactrouter.com/en/main)
- [Documentation i18next](https://www.i18next.com/)
