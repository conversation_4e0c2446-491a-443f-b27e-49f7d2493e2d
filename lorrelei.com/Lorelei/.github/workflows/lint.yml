name: linter

on:
  push:
    branches:
      - develop
      - main
  pull_request:
    branches:
      - develop
      - main

permissions:
  contents: write

jobs:
  quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'

      - name: Install Dependencies
        run: |
          composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
          npm install

      - name: Run Pint
        run: vendor/bin/pint

      - name: Format Frontend
        run: npm run format

      - name: Lint Frontend
        run: npm run lint

      # - name: Commit Changes
      #   uses: stefanzweifel/git-auto-commit-action@v5
      #   with:
      #     commit_message: fix code style
      #     commit_options: '--no-verify'
