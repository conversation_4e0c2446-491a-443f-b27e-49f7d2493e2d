{
<<<<<<< HEAD
    "name": "<PERSON><PERSON><PERSON>",
=======
    "name": "<PERSON><PERSON><PERSON>",
>>>>>>> origin/develop
    "lockfileVersion": 3,
    "requires": true,
    "packages": {
        "": {
            "dependencies": {
                "@headlessui/react": "^2.2.0",
                "@inertiajs/react": "^2.0.0",
                "@paypal/react-paypal-js": "^8.8.3",
                "@radix-ui/react-accordion": "^1.2.11",
                "@radix-ui/react-avatar": "^1.1.3",
                "@radix-ui/react-checkbox": "^1.1.4",
                "@radix-ui/react-collapsible": "^1.1.3",
                "@radix-ui/react-dialog": "^1.1.6",
                "@radix-ui/react-dropdown-menu": "^2.1.6",
                "@radix-ui/react-label": "^2.1.2",
                "@radix-ui/react-navigation-menu": "^1.2.5",
                "@radix-ui/react-radio-group": "^1.3.7",
                "@radix-ui/react-scroll-area": "^1.2.6",
                "@radix-ui/react-select": "^2.1.6",
                "@radix-ui/react-separator": "^1.1.2",
                "@radix-ui/react-slider": "^1.2.4",
                "@radix-ui/react-slot": "^1.1.2",
                "@radix-ui/react-tabs": "^1.1.4",
                "@radix-ui/react-toast": "^1.2.7",
                "@radix-ui/react-toggle": "^1.1.2",
                "@radix-ui/react-toggle-group": "^1.1.2",
                "@radix-ui/react-tooltip": "^1.1.8",
                "@tailwindcss/vite": "^4.0.6",
                "@types/react": "^19.0.3",
                "@types/react-dom": "^19.0.2",
                "@vitejs/plugin-react": "^4.3.4",
                "class-variance-authority": "^0.7.1",
                "clsx": "^2.1.1",
                "concurrently": "^9.0.1",
                "date-fns": "^4.1.0",
                "framer-motion": "^12.11.0",
                "globals": "^15.14.0",
                "laravel-vite-plugin": "^1.0",
                "lucide-react": "^0.475.0",
                "react": "^19.0.0",
                "react-dom": "^19.0.0",
                "tailwind-merge": "^3.2.0",
                "tailwindcss": "^4.0.0",
                "tailwindcss-animate": "^1.0.7",
                "typescript": "^5.7.2",
                "vite": "^6.0"
            },
            "devDependencies": {
                "@eslint/js": "^9.19.0",
                "@types/node": "^22.13.5",
                "eslint": "^9.17.0",
                "eslint-config-prettier": "^10.0.1",
                "eslint-plugin-react": "^7.37.3",
                "eslint-plugin-react-hooks": "^5.1.0",
                "prettier": "^3.4.2",
                "prettier-plugin-organize-imports": "^4.1.0",
                "prettier-plugin-tailwindcss": "^0.6.11",
                "typescript-eslint": "^8.23.0"
            },
            "optionalDependencies": {
                "@rollup/rollup-linux-x64-gnu": "4.9.5",
                "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1",
                "lightningcss-linux-x64-gnu": "^1.29.1"
            }
        },
        "node_modules/@ampproject/remapping": {
            "version": "2.3.0",
            "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz",
            "integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==",
            "license": "Apache-2.0",
            "dependencies": {
                "@jridgewell/gen-mapping": "^0.3.5",
                "@jridgewell/trace-mapping": "^0.3.24"
            },
            "engines": {
                "node": ">=6.0.0"
            }
        },
        "node_modules/@babel/code-frame": {
            "version": "7.26.2",
            "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz",
            "integrity": "sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==",
            "license": "MIT",
            "dependencies": {
                "@babel/helper-validator-identifier": "^7.25.9",
                "js-tokens": "^4.0.0",
                "picocolors": "^1.0.0"
            },
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@babel/compat-data": {
            "version": "7.26.8",
            "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.8.tgz",
            "integrity": "sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==",
            "license": "MIT",
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@babel/core": {
            "version": "7.26.9",
            "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.26.9.tgz",
            "integrity": "sha512-lWBYIrF7qK5+GjY5Uy+/hEgp8OJWOD/rpy74GplYRhEauvbHDeFB8t5hPOZxCZ0Oxf4Cc36tK51/l3ymJysrKw==",
            "license": "MIT",
            "dependencies": {
                "@ampproject/remapping": "^2.2.0",
                "@babel/code-frame": "^7.26.2",
                "@babel/generator": "^7.26.9",
                "@babel/helper-compilation-targets": "^7.26.5",
                "@babel/helper-module-transforms": "^7.26.0",
                "@babel/helpers": "^7.26.9",
                "@babel/parser": "^7.26.9",
                "@babel/template": "^7.26.9",
                "@babel/traverse": "^7.26.9",
                "@babel/types": "^7.26.9",
                "convert-source-map": "^2.0.0",
                "debug": "^4.1.0",
                "gensync": "^1.0.0-beta.2",
                "json5": "^2.2.3",
                "semver": "^6.3.1"
            },
            "engines": {
                "node": ">=6.9.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/babel"
            }
        },
        "node_modules/@babel/generator": {
            "version": "7.26.9",
            "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.26.9.tgz",
            "integrity": "sha512-kEWdzjOAUMW4hAyrzJ0ZaTOu9OmpyDIQicIh0zg0EEcEkYXZb2TjtBhnHi2ViX7PKwZqF4xwqfAm299/QMP3lg==",
            "license": "MIT",
            "dependencies": {
                "@babel/parser": "^7.26.9",
                "@babel/types": "^7.26.9",
                "@jridgewell/gen-mapping": "^0.3.5",
                "@jridgewell/trace-mapping": "^0.3.25",
                "jsesc": "^3.0.2"
            },
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@babel/helper-compilation-targets": {
            "version": "7.26.5",
            "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.26.5.tgz",
            "integrity": "sha512-IXuyn5EkouFJscIDuFF5EsiSolseme1s0CZB+QxVugqJLYmKdxI1VfIBOst0SUu4rnk2Z7kqTwmoO1lp3HIfnA==",
            "license": "MIT",
            "dependencies": {
                "@babel/compat-data": "^7.26.5",
                "@babel/helper-validator-option": "^7.25.9",
                "browserslist": "^4.24.0",
                "lru-cache": "^5.1.1",
                "semver": "^6.3.1"
            },
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@babel/helper-module-imports": {
            "version": "7.25.9",
            "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz",
            "integrity": "sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==",
            "license": "MIT",
            "dependencies": {
                "@babel/traverse": "^7.25.9",
                "@babel/types": "^7.25.9"
            },
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@babel/helper-module-transforms": {
            "version": "7.26.0",
            "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz",
            "integrity": "sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==",
            "license": "MIT",
            "dependencies": {
                "@babel/helper-module-imports": "^7.25.9",
                "@babel/helper-validator-identifier": "^7.25.9",
                "@babel/traverse": "^7.25.9"
            },
            "engines": {
                "node": ">=6.9.0"
            },
            "peerDependencies": {
                "@babel/core": "^7.0.0"
            }
        },
        "node_modules/@babel/helper-plugin-utils": {
            "version": "7.26.5",
            "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz",
            "integrity": "sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==",
            "license": "MIT",
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@babel/helper-string-parser": {
            "version": "7.25.9",
            "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz",
            "integrity": "sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==",
            "license": "MIT",
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@babel/helper-validator-identifier": {
            "version": "7.25.9",
            "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz",
            "integrity": "sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==",
            "license": "MIT",
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@babel/helper-validator-option": {
            "version": "7.25.9",
            "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz",
            "integrity": "sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==",
            "license": "MIT",
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@babel/helpers": {
            "version": "7.27.0",
            "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.0.tgz",
            "integrity": "sha512-U5eyP/CTFPuNE3qk+WZMxFkp/4zUzdceQlfzf7DdGdhp+Fezd7HD+i8Y24ZuTMKX3wQBld449jijbGq6OdGNQg==",
            "license": "MIT",
            "dependencies": {
                "@babel/template": "^7.27.0",
                "@babel/types": "^7.27.0"
            },
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@babel/parser": {
            "version": "7.27.0",
            "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.0.tgz",
            "integrity": "sha512-iaepho73/2Pz7w2eMS0Q5f83+0RKI7i4xmiYeBmDzfRVbQtTOG7Ts0S4HzJVsTMGI9keU8rNfuZr8DKfSt7Yyg==",
            "license": "MIT",
            "dependencies": {
                "@babel/types": "^7.27.0"
            },
            "bin": {
                "parser": "bin/babel-parser.js"
            },
            "engines": {
                "node": ">=6.0.0"
            }
        },
        "node_modules/@babel/plugin-transform-react-jsx-self": {
            "version": "7.25.9",
            "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.25.9.tgz",
            "integrity": "sha512-y8quW6p0WHkEhmErnfe58r7x0A70uKphQm8Sp8cV7tjNQwK56sNVK0M73LK3WuYmsuyrftut4xAkjjgU0twaMg==",
            "license": "MIT",
            "dependencies": {
                "@babel/helper-plugin-utils": "^7.25.9"
            },
            "engines": {
                "node": ">=6.9.0"
            },
            "peerDependencies": {
                "@babel/core": "^7.0.0-0"
            }
        },
        "node_modules/@babel/plugin-transform-react-jsx-source": {
            "version": "7.25.9",
            "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.25.9.tgz",
            "integrity": "sha512-+iqjT8xmXhhYv4/uiYd8FNQsraMFZIfxVSqxxVSZP0WbbSAWvBXAul0m/zu+7Vv4O/3WtApy9pmaTMiumEZgfg==",
            "license": "MIT",
            "dependencies": {
                "@babel/helper-plugin-utils": "^7.25.9"
            },
            "engines": {
                "node": ">=6.9.0"
            },
            "peerDependencies": {
                "@babel/core": "^7.0.0-0"
            }
        },
        "node_modules/@babel/template": {
            "version": "7.27.0",
            "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.27.0.tgz",
            "integrity": "sha512-2ncevenBqXI6qRMukPlXwHKHchC7RyMuu4xv5JBXRfOGVcTy1mXCD12qrp7Jsoxll1EV3+9sE4GugBVRjT2jFA==",
            "license": "MIT",
            "dependencies": {
                "@babel/code-frame": "^7.26.2",
                "@babel/parser": "^7.27.0",
                "@babel/types": "^7.27.0"
            },
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@babel/traverse": {
            "version": "7.26.9",
            "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.26.9.tgz",
            "integrity": "sha512-ZYW7L+pL8ahU5fXmNbPF+iZFHCv5scFak7MZ9bwaRPLUhHh7QQEMjZUg0HevihoqCM5iSYHN61EyCoZvqC+bxg==",
            "license": "MIT",
            "dependencies": {
                "@babel/code-frame": "^7.26.2",
                "@babel/generator": "^7.26.9",
                "@babel/parser": "^7.26.9",
                "@babel/template": "^7.26.9",
                "@babel/types": "^7.26.9",
                "debug": "^4.3.1",
                "globals": "^11.1.0"
            },
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@babel/traverse/node_modules/globals": {
            "version": "11.12.0",
            "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz",
            "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==",
            "license": "MIT",
            "engines": {
                "node": ">=4"
            }
        },
        "node_modules/@babel/types": {
            "version": "7.27.0",
            "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.27.0.tgz",
            "integrity": "sha512-H45s8fVLYjbhFH62dIJ3WtmJ6RSPt/3DRO0ZcT2SUiYiQyz3BLVb9ADEnLl91m74aQPS3AzzeajZHYOalWe3bg==",
            "license": "MIT",
            "dependencies": {
                "@babel/helper-string-parser": "^7.25.9",
                "@babel/helper-validator-identifier": "^7.25.9"
            },
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@esbuild/aix-ppc64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.25.0.tgz",
            "integrity": "sha512-O7vun9Sf8DFjH2UtqK8Ku3LkquL9SZL8OLY1T5NZkA34+wG3OQF7cl4Ql8vdNzM6fzBbYfLaiRLIOZ+2FOCgBQ==",
            "cpu": [
                "ppc64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "aix"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/android-arm": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.0.tgz",
            "integrity": "sha512-PTyWCYYiU0+1eJKmw21lWtC+d08JDZPQ5g+kFyxP0V+es6VPPSUhM6zk8iImp2jbV6GwjX4pap0JFbUQN65X1g==",
            "cpu": [
                "arm"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "android"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/android-arm64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.25.0.tgz",
            "integrity": "sha512-grvv8WncGjDSyUBjN9yHXNt+cq0snxXbDxy5pJtzMKGmmpPxeAmAhWxXI+01lU5rwZomDgD3kJwulEnhTRUd6g==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "android"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/android-x64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.25.0.tgz",
            "integrity": "sha512-m/ix7SfKG5buCnxasr52+LI78SQ+wgdENi9CqyCXwjVR2X4Jkz+BpC3le3AoBPYTC9NHklwngVXvbJ9/Akhrfg==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "android"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/darwin-arm64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.25.0.tgz",
            "integrity": "sha512-mVwdUb5SRkPayVadIOI78K7aAnPamoeFR2bT5nszFUZ9P8UpK4ratOdYbZZXYSqPKMHfS1wdHCJk1P1EZpRdvw==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "darwin"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/darwin-x64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.25.0.tgz",
            "integrity": "sha512-DgDaYsPWFTS4S3nWpFcMn/33ZZwAAeAFKNHNa1QN0rI4pUjgqf0f7ONmXf6d22tqTY+H9FNdgeaAa+YIFUn2Rg==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "darwin"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/freebsd-arm64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.0.tgz",
            "integrity": "sha512-VN4ocxy6dxefN1MepBx/iD1dH5K8qNtNe227I0mnTRjry8tj5MRk4zprLEdG8WPyAPb93/e4pSgi1SoHdgOa4w==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "freebsd"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/freebsd-x64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.0.tgz",
            "integrity": "sha512-mrSgt7lCh07FY+hDD1TxiTyIHyttn6vnjesnPoVDNmDfOmggTLXRv8Id5fNZey1gl/V2dyVK1VXXqVsQIiAk+A==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "freebsd"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/linux-arm": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.0.tgz",
            "integrity": "sha512-vkB3IYj2IDo3g9xX7HqhPYxVkNQe8qTK55fraQyTzTX/fxaDtXiEnavv9geOsonh2Fd2RMB+i5cbhu2zMNWJwg==",
            "cpu": [
                "arm"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/linux-arm64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.25.0.tgz",
            "integrity": "sha512-9QAQjTWNDM/Vk2bgBl17yWuZxZNQIF0OUUuPZRKoDtqF2k4EtYbpyiG5/Dk7nqeK6kIJWPYldkOcBqjXjrUlmg==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/linux-ia32": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.25.0.tgz",
            "integrity": "sha512-43ET5bHbphBegyeqLb7I1eYn2P/JYGNmzzdidq/w0T8E2SsYL1U6un2NFROFRg1JZLTzdCoRomg8Rvf9M6W6Gg==",
            "cpu": [
                "ia32"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/linux-loong64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.25.0.tgz",
            "integrity": "sha512-fC95c/xyNFueMhClxJmeRIj2yrSMdDfmqJnyOY4ZqsALkDrrKJfIg5NTMSzVBr5YW1jf+l7/cndBfP3MSDpoHw==",
            "cpu": [
                "loong64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/linux-mips64el": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.0.tgz",
            "integrity": "sha512-nkAMFju7KDW73T1DdH7glcyIptm95a7Le8irTQNO/qtkoyypZAnjchQgooFUDQhNAy4iu08N79W4T4pMBwhPwQ==",
            "cpu": [
                "mips64el"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/linux-ppc64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.25.0.tgz",
            "integrity": "sha512-NhyOejdhRGS8Iwv+KKR2zTq2PpysF9XqY+Zk77vQHqNbo/PwZCzB5/h7VGuREZm1fixhs4Q/qWRSi5zmAiO4Fw==",
            "cpu": [
                "ppc64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/linux-riscv64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.25.0.tgz",
            "integrity": "sha512-5S/rbP5OY+GHLC5qXp1y/Mx//e92L1YDqkiBbO9TQOvuFXM+iDqUNG5XopAnXoRH3FjIUDkeGcY1cgNvnXp/kA==",
            "cpu": [
                "riscv64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/linux-s390x": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.0.tgz",
            "integrity": "sha512-XM2BFsEBz0Fw37V0zU4CXfcfuACMrppsMFKdYY2WuTS3yi8O1nFOhil/xhKTmE1nPmVyvQJjJivgDT+xh8pXJA==",
            "cpu": [
                "s390x"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/linux-x64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.25.0.tgz",
            "integrity": "sha512-9yl91rHw/cpwMCNytUDxwj2XjFpxML0y9HAOH9pNVQDpQrBxHy01Dx+vaMu0N1CKa/RzBD2hB4u//nfc+Sd3Cw==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/netbsd-arm64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.0.tgz",
            "integrity": "sha512-RuG4PSMPFfrkH6UwCAqBzauBWTygTvb1nxWasEJooGSJ/NwRw7b2HOwyRTQIU97Hq37l3npXoZGYMy3b3xYvPw==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "netbsd"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/netbsd-x64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.0.tgz",
            "integrity": "sha512-jl+qisSB5jk01N5f7sPCsBENCOlPiS/xptD5yxOx2oqQfyourJwIKLRA2yqWdifj3owQZCL2sn6o08dBzZGQzA==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "netbsd"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/openbsd-arm64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.0.tgz",
            "integrity": "sha512-21sUNbq2r84YE+SJDfaQRvdgznTD8Xc0oc3p3iW/a1EVWeNj/SdUCbm5U0itZPQYRuRTW20fPMWMpcrciH2EJw==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "openbsd"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/openbsd-x64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.25.0.tgz",
            "integrity": "sha512-2gwwriSMPcCFRlPlKx3zLQhfN/2WjJ2NSlg5TKLQOJdV0mSxIcYNTMhk3H3ulL/cak+Xj0lY1Ym9ysDV1igceg==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "openbsd"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/sunos-x64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.0.tgz",
            "integrity": "sha512-bxI7ThgLzPrPz484/S9jLlvUAHYMzy6I0XiU1ZMeAEOBcS0VePBFxh1JjTQt3Xiat5b6Oh4x7UC7IwKQKIJRIg==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "sunos"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/win32-arm64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.0.tgz",
            "integrity": "sha512-ZUAc2YK6JW89xTbXvftxdnYy3m4iHIkDtK3CLce8wg8M2L+YZhIvO1DKpxrd0Yr59AeNNkTiic9YLf6FTtXWMw==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "win32"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/win32-ia32": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.25.0.tgz",
            "integrity": "sha512-eSNxISBu8XweVEWG31/JzjkIGbGIJN/TrRoiSVZwZ6pkC6VX4Im/WV2cz559/TXLcYbcrDN8JtKgd9DJVIo8GA==",
            "cpu": [
                "ia32"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "win32"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@esbuild/win32-x64": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.0.tgz",
            "integrity": "sha512-ZENoHJBxA20C2zFzh6AI4fT6RraMzjYw4xKWemRTRmRVtN9c5DcH9r/f2ihEkMjOW5eGgrwCslG/+Y/3bL+DHQ==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "win32"
            ],
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/@eslint-community/eslint-utils": {
            "version": "4.4.1",
            "resolved": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.4.1.tgz",
            "integrity": "sha512-s3O3waFUrMV8P/XaF/+ZTp1X9XBZW1a4B97ZnjQF2KYWaFD2A8KyFBsrsfSjEmjn3RGWAIuvlneuZm3CUK3jbA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "eslint-visitor-keys": "^3.4.3"
            },
            "engines": {
                "node": "^12.22.0 || ^14.17.0 || >=16.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/eslint"
            },
            "peerDependencies": {
                "eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"
            }
        },
        "node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys": {
            "version": "3.4.3",
            "resolved": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz",
            "integrity": "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==",
            "dev": true,
            "license": "Apache-2.0",
            "engines": {
                "node": "^12.22.0 || ^14.17.0 || >=16.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/eslint"
            }
        },
        "node_modules/@eslint-community/regexpp": {
            "version": "4.12.1",
            "resolved": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz",
            "integrity": "sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": "^12.0.0 || ^14.0.0 || >=16.0.0"
            }
        },
        "node_modules/@eslint/config-array": {
            "version": "0.19.2",
            "resolved": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.19.2.tgz",
            "integrity": "sha512-GNKqxfHG2ySmJOBSHg7LxeUx4xpuCoFjacmlCoYWEbaPXLwvfIjixRI12xCQZeULksQb23uiA8F40w5TojpV7w==",
            "dev": true,
            "license": "Apache-2.0",
            "dependencies": {
                "@eslint/object-schema": "^2.1.6",
                "debug": "^4.3.1",
                "minimatch": "^3.1.2"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            }
        },
        "node_modules/@eslint/core": {
            "version": "0.12.0",
            "resolved": "https://registry.npmjs.org/@eslint/core/-/core-0.12.0.tgz",
            "integrity": "sha512-cmrR6pytBuSMTaBweKoGMwu3EiHiEC+DoyupPmlZ0HxBJBtIxwe+j/E4XPIKNx+Q74c8lXKPwYawBf5glsTkHg==",
            "dev": true,
            "license": "Apache-2.0",
            "dependencies": {
                "@types/json-schema": "^7.0.15"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            }
        },
        "node_modules/@eslint/eslintrc": {
            "version": "3.3.0",
            "resolved": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.3.0.tgz",
            "integrity": "sha512-yaVPAiNAalnCZedKLdR21GOGILMLKPyqSLWaAjQFvYA2i/ciDi8ArYVr69Anohb6cH2Ukhqti4aFnYyPm8wdwQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "ajv": "^6.12.4",
                "debug": "^4.3.2",
                "espree": "^10.0.1",
                "globals": "^14.0.0",
                "ignore": "^5.2.0",
                "import-fresh": "^3.2.1",
                "js-yaml": "^4.1.0",
                "minimatch": "^3.1.2",
                "strip-json-comments": "^3.1.1"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "url": "https://opencollective.com/eslint"
            }
        },
        "node_modules/@eslint/eslintrc/node_modules/globals": {
            "version": "14.0.0",
            "resolved": "https://registry.npmjs.org/globals/-/globals-14.0.0.tgz",
            "integrity": "sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=18"
            },
            "funding": {
                "url": "https://github.com/sponsors/sindresorhus"
            }
        },
        "node_modules/@eslint/js": {
            "version": "9.21.0",
            "resolved": "https://registry.npmjs.org/@eslint/js/-/js-9.21.0.tgz",
            "integrity": "sha512-BqStZ3HX8Yz6LvsF5ByXYrtigrV5AXADWLAGc7PH/1SxOb7/FIYYMszZZWiUou/GB9P2lXWk2SV4d+Z8h0nknw==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            }
        },
        "node_modules/@eslint/object-schema": {
            "version": "2.1.6",
            "resolved": "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.6.tgz",
            "integrity": "sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==",
            "dev": true,
            "license": "Apache-2.0",
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            }
        },
        "node_modules/@eslint/plugin-kit": {
            "version": "0.2.7",
            "resolved": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.7.tgz",
            "integrity": "sha512-JubJ5B2pJ4k4yGxaNLdbjrnk9d/iDz6/q8wOilpIowd6PJPgaxCuHBnBszq7Ce2TyMrywm5r4PnKm6V3iiZF+g==",
            "dev": true,
            "license": "Apache-2.0",
            "dependencies": {
                "@eslint/core": "^0.12.0",
                "levn": "^0.4.1"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            }
        },
        "node_modules/@floating-ui/core": {
            "version": "1.6.9",
            "resolved": "https://registry.npmjs.org/@floating-ui/core/-/core-1.6.9.tgz",
            "integrity": "sha512-uMXCuQ3BItDUbAMhIXw7UPXRfAlOAvZzdK9BWpE60MCn+Svt3aLn9jsPTi/WNGlRUu2uI0v5S7JiIUsbsvh3fw==",
            "license": "MIT",
            "dependencies": {
                "@floating-ui/utils": "^0.2.9"
            }
        },
        "node_modules/@floating-ui/dom": {
            "version": "1.6.13",
            "resolved": "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.6.13.tgz",
            "integrity": "sha512-umqzocjDgNRGTuO7Q8CU32dkHkECqI8ZdMZ5Swb6QAM0t5rnlrN3lGo1hdpscRd3WS8T6DKYK4ephgIH9iRh3w==",
            "license": "MIT",
            "dependencies": {
                "@floating-ui/core": "^1.6.0",
                "@floating-ui/utils": "^0.2.9"
            }
        },
        "node_modules/@floating-ui/react": {
            "version": "0.26.28",
            "resolved": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.28.tgz",
            "integrity": "sha512-yORQuuAtVpiRjpMhdc0wJj06b9JFjrYF4qp96j++v2NBpbi6SEGF7donUJ3TMieerQ6qVkAv1tgr7L4r5roTqw==",
            "license": "MIT",
            "dependencies": {
                "@floating-ui/react-dom": "^2.1.2",
                "@floating-ui/utils": "^0.2.8",
                "tabbable": "^6.0.0"
            },
            "peerDependencies": {
                "react": ">=16.8.0",
                "react-dom": ">=16.8.0"
            }
        },
        "node_modules/@floating-ui/react-dom": {
            "version": "2.1.2",
            "resolved": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.2.tgz",
            "integrity": "sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A==",
            "license": "MIT",
            "dependencies": {
                "@floating-ui/dom": "^1.0.0"
            },
            "peerDependencies": {
                "react": ">=16.8.0",
                "react-dom": ">=16.8.0"
            }
        },
        "node_modules/@floating-ui/utils": {
            "version": "0.2.9",
            "resolved": "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.9.tgz",
            "integrity": "sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==",
            "license": "MIT"
        },
        "node_modules/@headlessui/react": {
            "version": "2.2.0",
            "resolved": "https://registry.npmjs.org/@headlessui/react/-/react-2.2.0.tgz",
            "integrity": "sha512-RzCEg+LXsuI7mHiSomsu/gBJSjpupm6A1qIZ5sWjd7JhARNlMiSA4kKfJpCKwU9tE+zMRterhhrP74PvfJrpXQ==",
            "license": "MIT",
            "dependencies": {
                "@floating-ui/react": "^0.26.16",
                "@react-aria/focus": "^3.17.1",
                "@react-aria/interactions": "^3.21.3",
                "@tanstack/react-virtual": "^3.8.1"
            },
            "engines": {
                "node": ">=10"
            },
            "peerDependencies": {
                "react": "^18 || ^19 || ^19.0.0-rc",
                "react-dom": "^18 || ^19 || ^19.0.0-rc"
            }
        },
        "node_modules/@humanfs/core": {
            "version": "0.19.1",
            "resolved": "https://registry.npmjs.org/@humanfs/core/-/core-0.19.1.tgz",
            "integrity": "sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==",
            "dev": true,
            "license": "Apache-2.0",
            "engines": {
                "node": ">=18.18.0"
            }
        },
        "node_modules/@humanfs/node": {
            "version": "0.16.6",
            "resolved": "https://registry.npmjs.org/@humanfs/node/-/node-0.16.6.tgz",
            "integrity": "sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==",
            "dev": true,
            "license": "Apache-2.0",
            "dependencies": {
                "@humanfs/core": "^0.19.1",
                "@humanwhocodes/retry": "^0.3.0"
            },
            "engines": {
                "node": ">=18.18.0"
            }
        },
        "node_modules/@humanfs/node/node_modules/@humanwhocodes/retry": {
            "version": "0.3.1",
            "resolved": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.3.1.tgz",
            "integrity": "sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==",
            "dev": true,
            "license": "Apache-2.0",
            "engines": {
                "node": ">=18.18"
            },
            "funding": {
                "type": "github",
                "url": "https://github.com/sponsors/nzakas"
            }
        },
        "node_modules/@humanwhocodes/module-importer": {
            "version": "1.0.1",
            "resolved": "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz",
            "integrity": "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==",
            "dev": true,
            "license": "Apache-2.0",
            "engines": {
                "node": ">=12.22"
            },
            "funding": {
                "type": "github",
                "url": "https://github.com/sponsors/nzakas"
            }
        },
        "node_modules/@humanwhocodes/retry": {
            "version": "0.4.2",
            "resolved": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.2.tgz",
            "integrity": "sha512-xeO57FpIu4p1Ri3Jq/EXq4ClRm86dVF2z/+kvFnyqVYRavTZmaFaUBbWCOuuTh0o/g7DSsk6kc2vrS4Vl5oPOQ==",
            "dev": true,
            "license": "Apache-2.0",
            "engines": {
                "node": ">=18.18"
            },
            "funding": {
                "type": "github",
                "url": "https://github.com/sponsors/nzakas"
            }
        },
        "node_modules/@inertiajs/core": {
            "version": "2.0.4",
            "resolved": "https://registry.npmjs.org/@inertiajs/core/-/core-2.0.4.tgz",
            "integrity": "sha512-gCUqpwBRYOhz0hwBDWca2lkk+Mc+36GvbRoE0rEvYFpzQAMMP0xFhH9h8hr7VWTn+vVOZRuDvakI+4cazwtvCg==",
            "license": "MIT",
            "dependencies": {
                "axios": "^1.6.0",
                "deepmerge": "^4.0.0",
                "qs": "^6.9.0"
            }
        },
        "node_modules/@inertiajs/react": {
            "version": "2.0.4",
            "resolved": "https://registry.npmjs.org/@inertiajs/react/-/react-2.0.4.tgz",
            "integrity": "sha512-syPqZNVU5v0DB3VHCm9aVQafJ9kgkxtC5lfc4WOTBxtUjZjbJYDwt5d0yLOhyfU4S7d9CR0dhlkkEt1DsedD3Q==",
            "license": "MIT",
            "dependencies": {
                "@inertiajs/core": "2.0.4",
                "lodash.isequal": "^4.5.0"
            },
            "peerDependencies": {
                "react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"
            }
        },
        "node_modules/@jridgewell/gen-mapping": {
            "version": "0.3.8",
            "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz",
            "integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==",
            "license": "MIT",
            "dependencies": {
                "@jridgewell/set-array": "^1.2.1",
                "@jridgewell/sourcemap-codec": "^1.4.10",
                "@jridgewell/trace-mapping": "^0.3.24"
            },
            "engines": {
                "node": ">=6.0.0"
            }
        },
        "node_modules/@jridgewell/resolve-uri": {
            "version": "3.1.2",
            "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz",
            "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==",
            "license": "MIT",
            "engines": {
                "node": ">=6.0.0"
            }
        },
        "node_modules/@jridgewell/set-array": {
            "version": "1.2.1",
            "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz",
            "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==",
            "license": "MIT",
            "engines": {
                "node": ">=6.0.0"
            }
        },
        "node_modules/@jridgewell/sourcemap-codec": {
            "version": "1.5.0",
            "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz",
            "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==",
            "license": "MIT"
        },
        "node_modules/@jridgewell/trace-mapping": {
            "version": "0.3.25",
            "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz",
            "integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==",
            "license": "MIT",
            "dependencies": {
                "@jridgewell/resolve-uri": "^3.1.0",
                "@jridgewell/sourcemap-codec": "^1.4.14"
            }
        },
        "node_modules/@nodelib/fs.scandir": {
            "version": "2.1.5",
            "resolved": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz",
            "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@nodelib/fs.stat": "2.0.5",
                "run-parallel": "^1.1.9"
            },
            "engines": {
                "node": ">= 8"
            }
        },
        "node_modules/@nodelib/fs.stat": {
            "version": "2.0.5",
            "resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz",
            "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 8"
            }
        },
        "node_modules/@nodelib/fs.walk": {
            "version": "1.2.8",
            "resolved": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz",
            "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@nodelib/fs.scandir": "2.1.5",
                "fastq": "^1.6.0"
            },
            "engines": {
                "node": ">= 8"
            }
        },
        "node_modules/@paypal/paypal-js": {
            "version": "8.2.0",
            "resolved": "https://registry.npmjs.org/@paypal/paypal-js/-/paypal-js-8.2.0.tgz",
            "integrity": "sha512-hLg5wNORW3WiyMiRNJOm6cN2IqjPlClpxd971bEdm0LNpbbejQZYtesb0/0arTnySSbGcxg7MxjkZ/N5Z5qBNQ==",
            "license": "Apache-2.0",
            "dependencies": {
                "promise-polyfill": "^8.3.0"
            }
        },
        "node_modules/@paypal/react-paypal-js": {
            "version": "8.8.3",
            "resolved": "https://registry.npmjs.org/@paypal/react-paypal-js/-/react-paypal-js-8.8.3.tgz",
            "integrity": "sha512-H5s3EU7S+RFaLad3BmV9nSAmD3iaJI14mCtbngpqMm4ruMNGHjOTaSTX3jAAk/ghmzGAda2GMfyiYondO4F21Q==",
            "license": "Apache-2.0",
            "dependencies": {
                "@paypal/paypal-js": "^8.1.2",
                "@paypal/sdk-constants": "^1.0.122"
            },
            "peerDependencies": {
                "react": "^16.8.0 || ^17 || ^18 || ^19",
                "react-dom": "^16.8.0 || ^17 || ^18 || ^19"
            }
        },
        "node_modules/@paypal/sdk-constants": {
            "version": "1.0.153",
            "resolved": "https://registry.npmjs.org/@paypal/sdk-constants/-/sdk-constants-1.0.153.tgz",
            "integrity": "sha512-wrPXK9ckwYBOOLyHfsGGtYjf9vOQgPSOh08DEeGRr/KBamQgvHYEG2soSMPlJuLfker32n1x5wvnWCcYP+ea9w==",
            "license": "Apache-2.0",
            "dependencies": {
                "hi-base32": "^0.5.0"
            }
        },
        "node_modules/@radix-ui/number": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/number/-/number-1.1.0.tgz",
            "integrity": "sha512-V3gRzhVNU1ldS5XhAPTom1fOIo4ccrjjJgmE+LI2h/WaFpHmx0MQApT+KZHnx8abG6Avtfcz4WoEciMnpFT3HQ==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/primitive": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.1.tgz",
            "integrity": "sha512-SJ31y+Q/zAyShtXJc8x83i9TYdbAfHZ++tUZnvjJJqFjzsdUnKsxPL6IEtBlxKkU7yzer//GQtZSV4GbldL3YA==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/react-accordion": {
            "version": "1.2.11",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.11.tgz",
            "integrity": "sha512-l3W5D54emV2ues7jjeG1xcyN7S3jnK3zE2zHqgn0CmMsy9lNJwmgcrmaxS+7ipw15FAivzKNzH3d5EcGoFKw0A==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.2",
                "@radix-ui/react-collapsible": "1.1.11",
                "@radix-ui/react-collection": "1.1.7",
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-direction": "1.1.1",
                "@radix-ui/react-id": "1.1.1",
                "@radix-ui/react-primitive": "2.1.3",
                "@radix-ui/react-use-controllable-state": "1.2.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-accordion/node_modules/@radix-ui/primitive": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz",
            "integrity": "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/react-accordion/node_modules/@radix-ui/react-collection": {
            "version": "1.1.7",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.7.tgz",
            "integrity": "sha512-Fh9rGN0MoI4ZFUNyfFVNU4y9LUz93u9/0K+yLgA2bwRojxM8JU1DyvvMBabnZPBgMWREAJvU2jjVzq+LrFUglw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-primitive": "2.1.3",
                "@radix-ui/react-slot": "1.2.3"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-accordion/node_modules/@radix-ui/react-compose-refs": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz",
            "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-accordion/node_modules/@radix-ui/react-context": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz",
            "integrity": "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-accordion/node_modules/@radix-ui/react-direction": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.1.tgz",
            "integrity": "sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-accordion/node_modules/@radix-ui/react-id": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.1.tgz",
            "integrity": "sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-accordion/node_modules/@radix-ui/react-primitive": {
            "version": "2.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz",
            "integrity": "sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-slot": "1.2.3"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-accordion/node_modules/@radix-ui/react-slot": {
            "version": "1.2.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.3.tgz",
            "integrity": "sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-accordion/node_modules/@radix-ui/react-use-controllable-state": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz",
            "integrity": "sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-effect-event": "0.0.2",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-accordion/node_modules/@radix-ui/react-use-layout-effect": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz",
            "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-arrow": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.2.tgz",
            "integrity": "sha512-G+KcpzXHq24iH0uGG/pF8LyzpFJYGD4RfLjCIBfGdSLXvjLHST31RUiRVrupIBMvIppMgSzQ6l66iAxl03tdlg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-avatar": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.3.tgz",
            "integrity": "sha512-Paen00T4P8L8gd9bNsRMw7Cbaz85oxiv+hzomsRZgFm2byltPFDtfcoqlWJ8GyZlIBWgLssJlzLCnKU0G0302g==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-layout-effect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-checkbox": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.4.tgz",
            "integrity": "sha512-wP0CPAHq+P5I4INKe3hJrIa1WoNqqrejzW+zoU0rOvo1b9gDEJJFl2rYfO1PYJUQCc2H1WZxIJmyv9BS8i5fLw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-use-previous": "1.1.0",
                "@radix-ui/react-use-size": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-collapsible": {
            "version": "1.1.11",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.11.tgz",
            "integrity": "sha512-2qrRsVGSCYasSz1RFOorXwl0H7g7J1frQtgpQgYrt+MOidtPAINHn9CPovQXb83r8ahapdx3Tu0fa/pdFFSdPg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.2",
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-id": "1.1.1",
                "@radix-ui/react-presence": "1.1.4",
                "@radix-ui/react-primitive": "2.1.3",
                "@radix-ui/react-use-controllable-state": "1.2.2",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/primitive": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz",
            "integrity": "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/react-compose-refs": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz",
            "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/react-context": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz",
            "integrity": "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/react-id": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.1.tgz",
            "integrity": "sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/react-presence": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.4.tgz",
            "integrity": "sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/react-primitive": {
            "version": "2.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz",
            "integrity": "sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-slot": "1.2.3"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/react-slot": {
            "version": "1.2.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.3.tgz",
            "integrity": "sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/react-use-controllable-state": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz",
            "integrity": "sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-effect-event": "0.0.2",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/react-use-layout-effect": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz",
            "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-collection": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.2.tgz",
            "integrity": "sha512-9z54IEKRxIa9VityapoEYMuByaG42iSy1ZXlY2KcuLSEtq8x4987/N6m15ppoMffgZX72gER2uHe1D9Y6Unlcw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-slot": "1.1.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-compose-refs": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.1.tgz",
            "integrity": "sha512-Y9VzoRDSJtgFMUCoiZBDVo084VQ5hfpXxVE+NgkdNsjiDBByiImMZKKhxMwCbdHvhlENG6a833CbFkOQvTricw==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-context": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1.tgz",
            "integrity": "sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-dialog": {
            "version": "1.1.6",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.6.tgz",
            "integrity": "sha512-/IVhJV5AceX620DUJ4uYVMymzsipdKBzo3edo+omeskCKGm9FRHM0ebIdbPnlQVJqyuHbuBltQUOG2mOTq2IYw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-dismissable-layer": "1.1.5",
                "@radix-ui/react-focus-guards": "1.1.1",
                "@radix-ui/react-focus-scope": "1.1.2",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-portal": "1.1.4",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-slot": "1.1.2",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "aria-hidden": "^1.2.4",
                "react-remove-scroll": "^2.6.3"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-direction": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.0.tgz",
            "integrity": "sha512-BUuBvgThEiAXh2DWu93XsT+a3aWrGqolGlqqw5VU1kG7p/ZH2cuDlM1sRLNnY3QcBS69UIz2mcKhMxDsdewhjg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-dismissable-layer": {
            "version": "1.1.5",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.5.tgz",
            "integrity": "sha512-E4TywXY6UsXNRhFrECa5HAvE5/4BFcGyfTyK36gP+pAW1ed7UTK4vKwdr53gAJYwqbfCWC6ATvJa3J3R/9+Qrg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-escape-keydown": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-dropdown-menu": {
            "version": "2.1.6",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-dropdown-menu/-/react-dropdown-menu-2.1.6.tgz",
            "integrity": "sha512-no3X7V5fD487wab/ZYSHXq3H37u4NVeLDKI/Ks724X/eEFSSEFYZxWgsIlr1UBeEyDaM29HM5x9p1Nv8DuTYPA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-menu": "2.1.6",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-focus-guards": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.1.tgz",
            "integrity": "sha512-pSIwfrT1a6sIoDASCSpFwOasEwKTZWDw/iBdtnqKO7v6FeOzYJ7U53cPzYFVR3geGGXgVHaH+CdngrrAzqUGxg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-focus-scope": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.2.tgz",
            "integrity": "sha512-zxwE80FCU7lcXUGWkdt6XpTTCKPitG1XKOwViTxHVKIJhZl9MvIl2dVHeZENCWD9+EdWv05wlaEkRXUykU27RA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-use-callback-ref": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-id": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.0.tgz",
            "integrity": "sha512-EJUrI8yYh7WOjNOqpoJaf1jlFIH2LvtgAl+YcFqNCa+4hj64ZXmPkAKOFs/ukjz3byN6bdb/AVUqHkI8/uWWMA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-label": {
            "version": "2.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.2.tgz",
            "integrity": "sha512-zo1uGMTaNlHehDyFQcDZXRJhUPDuukcnHz0/jnrup0JA6qL+AFpAnty+7VKa9esuU5xTblAZzTGYJKSKaBxBhw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-menu": {
            "version": "2.1.6",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-menu/-/react-menu-2.1.6.tgz",
            "integrity": "sha512-tBBb5CXDJW3t2mo9WlO7r6GTmWV0F0uzHZVFmlRmYpiSK1CDU5IKojP1pm7oknpBOrFZx/YgBRW9oorPO2S/Lg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-collection": "1.1.2",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-dismissable-layer": "1.1.5",
                "@radix-ui/react-focus-guards": "1.1.1",
                "@radix-ui/react-focus-scope": "1.1.2",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-popper": "1.2.2",
                "@radix-ui/react-portal": "1.1.4",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-roving-focus": "1.1.2",
                "@radix-ui/react-slot": "1.1.2",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "aria-hidden": "^1.2.4",
                "react-remove-scroll": "^2.6.3"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-navigation-menu": {
            "version": "1.2.5",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.5.tgz",
            "integrity": "sha512-myMHHQUZ3ZLTi8W381/Vu43Ia0NqakkQZ2vzynMmTUtQQ9kNkjzhOwkZC9TAM5R07OZUVIQyHC06f/9JZJpvvA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-collection": "1.1.2",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-dismissable-layer": "1.1.5",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-use-layout-effect": "1.1.0",
                "@radix-ui/react-use-previous": "1.1.0",
                "@radix-ui/react-visually-hidden": "1.1.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-popper": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.2.tgz",
            "integrity": "sha512-Rvqc3nOpwseCyj/rgjlJDYAgyfw7OC1tTkKn2ivhaMGcYt8FSBlahHOZak2i3QwkRXUXgGgzeEe2RuqeEHuHgA==",
            "license": "MIT",
            "dependencies": {
                "@floating-ui/react-dom": "^2.0.0",
                "@radix-ui/react-arrow": "1.1.2",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-layout-effect": "1.1.0",
                "@radix-ui/react-use-rect": "1.1.0",
                "@radix-ui/react-use-size": "1.1.0",
                "@radix-ui/rect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-portal": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.4.tgz",
            "integrity": "sha512-sn2O9k1rPFYVyKd5LAJfo96JlSGVFpa1fS6UuBJfrZadudiw5tAmru+n1x7aMRQ84qDM71Zh1+SzK5QwU0tJfA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-use-layout-effect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-presence": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.2.tgz",
            "integrity": "sha512-18TFr80t5EVgL9x1SwF/YGtfG+l0BS0PRAlCWBDoBEiDQjeKgnNZRVJp/oVBl24sr3Gbfwc/Qpj4OcWTQMsAEg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-use-layout-effect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-primitive": {
            "version": "2.0.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.2.tgz",
            "integrity": "sha512-Ec/0d38EIuvDF+GZjcMU/Ze6MxntVJYO/fRlCPhCaVUyPY9WTalHJw54tp9sXeJo3tlShWpy41vQRgLRGOuz+w==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-slot": "1.1.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group": {
            "version": "1.3.7",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-radio-group/-/react-radio-group-1.3.7.tgz",
            "integrity": "sha512-9w5XhD0KPOrm92OTTE0SysH3sYzHsSTHNvZgUBo/VZ80VdYyB5RneDbc0dKpURS24IxkoFRu/hI0i4XyfFwY6g==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.2",
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-direction": "1.1.1",
                "@radix-ui/react-presence": "1.1.4",
                "@radix-ui/react-primitive": "2.1.3",
                "@radix-ui/react-roving-focus": "1.1.10",
                "@radix-ui/react-use-controllable-state": "1.2.2",
                "@radix-ui/react-use-previous": "1.1.1",
                "@radix-ui/react-use-size": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/primitive": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz",
            "integrity": "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-collection": {
            "version": "1.1.7",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.7.tgz",
            "integrity": "sha512-Fh9rGN0MoI4ZFUNyfFVNU4y9LUz93u9/0K+yLgA2bwRojxM8JU1DyvvMBabnZPBgMWREAJvU2jjVzq+LrFUglw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-primitive": "2.1.3",
                "@radix-ui/react-slot": "1.2.3"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-compose-refs": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz",
            "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-context": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz",
            "integrity": "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-direction": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.1.tgz",
            "integrity": "sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-id": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.1.tgz",
            "integrity": "sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-presence": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.4.tgz",
            "integrity": "sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-primitive": {
            "version": "2.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz",
            "integrity": "sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-slot": "1.2.3"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-roving-focus": {
            "version": "1.1.10",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.10.tgz",
            "integrity": "sha512-dT9aOXUen9JSsxnMPv/0VqySQf5eDQ6LCk5Sw28kamz8wSOW2bJdlX2Bg5VUIIcV+6XlHpWTIuTPCf/UNIyq8Q==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.2",
                "@radix-ui/react-collection": "1.1.7",
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-direction": "1.1.1",
                "@radix-ui/react-id": "1.1.1",
                "@radix-ui/react-primitive": "2.1.3",
                "@radix-ui/react-use-callback-ref": "1.1.1",
                "@radix-ui/react-use-controllable-state": "1.2.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-slot": {
            "version": "1.2.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.3.tgz",
            "integrity": "sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-use-callback-ref": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz",
            "integrity": "sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-use-controllable-state": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz",
            "integrity": "sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-effect-event": "0.0.2",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-use-layout-effect": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz",
            "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-use-previous": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1.tgz",
            "integrity": "sha512-2dHfToCj/pzca2Ck724OZ5L0EVrr3eHRNsG/b3xQJLA2hZpVCS99bLAX+hm1IHXDEnzU6by5z/5MIY794/a8NQ==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group/node_modules/@radix-ui/react-use-size": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.1.tgz",
            "integrity": "sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-roving-focus": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.2.tgz",
            "integrity": "sha512-zgMQWkNO169GtGqRvYrzb0Zf8NhMHS2DuEB/TiEmVnpr5OqPU3i8lfbxaAmC2J/KYuIQxyoQQ6DxepyXp61/xw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-collection": "1.1.2",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-scroll-area": {
            "version": "1.2.6",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.6.tgz",
            "integrity": "sha512-lj8OMlpPERXrQIHlEQdlXHJoRT52AMpBrgyPYylOhXYq5e/glsEdtOc/kCQlsTdtgN5U0iDbrrolDadvektJGQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/number": "1.1.1",
                "@radix-ui/primitive": "1.1.2",
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-direction": "1.1.1",
                "@radix-ui/react-presence": "1.1.4",
                "@radix-ui/react-primitive": "2.1.0",
                "@radix-ui/react-use-callback-ref": "1.1.1",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-scroll-area/node_modules/@radix-ui/number": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/number/-/number-1.1.1.tgz",
            "integrity": "sha512-MkKCwxlXTgz6CFoJx3pCwn07GKp36+aZyu/u2Ln2VrA5DcdyCZkASEDBTd8x5whTQQL5CiYf4prXKLcgQdv29g==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/react-scroll-area/node_modules/@radix-ui/primitive": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz",
            "integrity": "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/react-scroll-area/node_modules/@radix-ui/react-compose-refs": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz",
            "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-scroll-area/node_modules/@radix-ui/react-context": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz",
            "integrity": "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-scroll-area/node_modules/@radix-ui/react-direction": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.1.tgz",
            "integrity": "sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-scroll-area/node_modules/@radix-ui/react-presence": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.4.tgz",
            "integrity": "sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-scroll-area/node_modules/@radix-ui/react-primitive": {
            "version": "2.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0.tgz",
            "integrity": "sha512-/J/FhLdK0zVcILOwt5g+dH4KnkonCtkVJsa2G6JmvbbtZfBEI1gMsO3QMjseL4F/SwfAMt1Vc/0XKYKq+xJ1sw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-slot": "1.2.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-scroll-area/node_modules/@radix-ui/react-slot": {
            "version": "1.2.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.0.tgz",
            "integrity": "sha512-ujc+V6r0HNDviYqIK3rW4ffgYiZ8g5DEHrGJVk4x7kTlLXRDILnKX9vAUYeIsLOoDpDJ0ujpqMkjH4w2ofuo6w==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-scroll-area/node_modules/@radix-ui/react-use-callback-ref": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz",
            "integrity": "sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-scroll-area/node_modules/@radix-ui/react-use-layout-effect": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz",
            "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-select": {
            "version": "2.1.6",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-select/-/react-select-2.1.6.tgz",
            "integrity": "sha512-T6ajELxRvTuAMWH0YmRJ1qez+x4/7Nq7QIx7zJ0VK3qaEWdnWpNbEDnmWldG1zBDwqrLy5aLMUWcoGirVj5kMg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/number": "1.1.0",
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-collection": "1.1.2",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-dismissable-layer": "1.1.5",
                "@radix-ui/react-focus-guards": "1.1.1",
                "@radix-ui/react-focus-scope": "1.1.2",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-popper": "1.2.2",
                "@radix-ui/react-portal": "1.1.4",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-slot": "1.1.2",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-use-layout-effect": "1.1.0",
                "@radix-ui/react-use-previous": "1.1.0",
                "@radix-ui/react-visually-hidden": "1.1.2",
                "aria-hidden": "^1.2.4",
                "react-remove-scroll": "^2.6.3"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-separator": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-separator/-/react-separator-1.1.2.tgz",
            "integrity": "sha512-oZfHcaAp2Y6KFBX6I5P1u7CQoy4lheCGiYj+pGFrHy8E/VNRb5E39TkTr3JrV520csPBTZjkuKFdEsjS5EUNKQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider": {
            "version": "1.2.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.4.tgz",
            "integrity": "sha512-Vr/OgNejNJPAghIhjS7Mf/2F/EXGDT0qgtiHf2BHz71+KqgN+jndFLKq5xAB9JOGejGzejfJLIvT04Do+yzhcg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/number": "1.1.1",
                "@radix-ui/primitive": "1.1.2",
                "@radix-ui/react-collection": "1.1.3",
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-direction": "1.1.1",
                "@radix-ui/react-primitive": "2.0.3",
                "@radix-ui/react-use-controllable-state": "1.1.1",
                "@radix-ui/react-use-layout-effect": "1.1.1",
                "@radix-ui/react-use-previous": "1.1.1",
                "@radix-ui/react-use-size": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/number": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/number/-/number-1.1.1.tgz",
            "integrity": "sha512-MkKCwxlXTgz6CFoJx3pCwn07GKp36+aZyu/u2Ln2VrA5DcdyCZkASEDBTd8x5whTQQL5CiYf4prXKLcgQdv29g==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/primitive": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz",
            "integrity": "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/react-collection": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3.tgz",
            "integrity": "sha512-mM2pxoQw5HJ49rkzwOs7Y6J4oYH22wS8BfK2/bBxROlI4xuR0c4jEenQP63LlTlDkO6Buj2Vt+QYAYcOgqtrXA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-primitive": "2.0.3",
                "@radix-ui/react-slot": "1.2.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/react-compose-refs": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz",
            "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/react-context": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz",
            "integrity": "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/react-direction": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.1.tgz",
            "integrity": "sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/react-primitive": {
            "version": "2.0.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3.tgz",
            "integrity": "sha512-Pf/t/GkndH7CQ8wE2hbkXA+WyZ83fhQQn5DDmwDiDo6AwN/fhaH8oqZ0jRjMrO2iaMhDi6P1HRx6AZwyMinY1g==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-slot": "1.2.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/react-slot": {
            "version": "1.2.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.0.tgz",
            "integrity": "sha512-ujc+V6r0HNDviYqIK3rW4ffgYiZ8g5DEHrGJVk4x7kTlLXRDILnKX9vAUYeIsLOoDpDJ0ujpqMkjH4w2ofuo6w==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/react-use-callback-ref": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz",
            "integrity": "sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/react-use-controllable-state": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.1.1.tgz",
            "integrity": "sha512-YnEXIy8/ga01Y1PN0VfaNH//MhA91JlEGVBDxDzROqwrAtG5Yr2QGEPz8A/rJA3C7ZAHryOYGaUv8fLSW2H/mg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-callback-ref": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/react-use-layout-effect": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz",
            "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/react-use-previous": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1.tgz",
            "integrity": "sha512-2dHfToCj/pzca2Ck724OZ5L0EVrr3eHRNsG/b3xQJLA2hZpVCS99bLAX+hm1IHXDEnzU6by5z/5MIY794/a8NQ==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider/node_modules/@radix-ui/react-use-size": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.1.tgz",
            "integrity": "sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slot": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.2.tgz",
            "integrity": "sha512-YAKxaiGsSQJ38VzKH86/BPRC4rh+b1Jpa+JneA5LRE7skmLPNAyeG8kPJj/oo4STLvlrs8vkf/iYyc3A5stYCQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4.tgz",
            "integrity": "sha512-fuHMHWSf5SRhXke+DbHXj2wVMo+ghVH30vhX3XVacdXqDl+J4XWafMIGOOER861QpBx1jxgwKXL2dQnfrsd8MQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-direction": "1.1.1",
                "@radix-ui/react-id": "1.1.1",
                "@radix-ui/react-presence": "1.1.3",
                "@radix-ui/react-primitive": "2.0.3",
                "@radix-ui/react-roving-focus": "1.1.3",
                "@radix-ui/react-use-controllable-state": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/primitive": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz",
            "integrity": "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-collection": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3.tgz",
            "integrity": "sha512-mM2pxoQw5HJ49rkzwOs7Y6J4oYH22wS8BfK2/bBxROlI4xuR0c4jEenQP63LlTlDkO6Buj2Vt+QYAYcOgqtrXA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-primitive": "2.0.3",
                "@radix-ui/react-slot": "1.2.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-compose-refs": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz",
            "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-context": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz",
            "integrity": "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-direction": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.1.tgz",
            "integrity": "sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-id": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.1.tgz",
            "integrity": "sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-presence": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3.tgz",
            "integrity": "sha512-IrVLIhskYhH3nLvtcBLQFZr61tBG7wx7O3kEmdzcYwRGAEBmBicGGL7ATzNgruYJ3xBTbuzEEq9OXJM3PAX3tA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-primitive": {
            "version": "2.0.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3.tgz",
            "integrity": "sha512-Pf/t/GkndH7CQ8wE2hbkXA+WyZ83fhQQn5DDmwDiDo6AwN/fhaH8oqZ0jRjMrO2iaMhDi6P1HRx6AZwyMinY1g==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-slot": "1.2.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-roving-focus": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.3.tgz",
            "integrity": "sha512-ufbpLUjZiOg4iYgb2hQrWXEPYX6jOLBbR27bDyAff5GYMRrCzcze8lukjuXVUQvJ6HZe8+oL+hhswDcjmcgVyg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.2",
                "@radix-ui/react-collection": "1.1.3",
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-direction": "1.1.1",
                "@radix-ui/react-id": "1.1.1",
                "@radix-ui/react-primitive": "2.0.3",
                "@radix-ui/react-use-callback-ref": "1.1.1",
                "@radix-ui/react-use-controllable-state": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-slot": {
            "version": "1.2.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.0.tgz",
            "integrity": "sha512-ujc+V6r0HNDviYqIK3rW4ffgYiZ8g5DEHrGJVk4x7kTlLXRDILnKX9vAUYeIsLOoDpDJ0ujpqMkjH4w2ofuo6w==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-use-callback-ref": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz",
            "integrity": "sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-use-controllable-state": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.1.1.tgz",
            "integrity": "sha512-YnEXIy8/ga01Y1PN0VfaNH//MhA91JlEGVBDxDzROqwrAtG5Yr2QGEPz8A/rJA3C7ZAHryOYGaUv8fLSW2H/mg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-callback-ref": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-use-layout-effect": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz",
            "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast": {
            "version": "1.2.7",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-toast/-/react-toast-1.2.7.tgz",
            "integrity": "sha512-0IWTbAUKvzdpOaWDMZisXZvScXzF0phaQjWspK8RUMEUxjLbli+886mB/kXTIC3F+t5vQ0n0vYn+dsX8s+WdfA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.2",
                "@radix-ui/react-collection": "1.1.3",
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-dismissable-layer": "1.1.6",
                "@radix-ui/react-portal": "1.1.5",
                "@radix-ui/react-presence": "1.1.3",
                "@radix-ui/react-primitive": "2.0.3",
                "@radix-ui/react-use-callback-ref": "1.1.1",
                "@radix-ui/react-use-controllable-state": "1.1.1",
                "@radix-ui/react-use-layout-effect": "1.1.1",
                "@radix-ui/react-visually-hidden": "1.1.3"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/primitive": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz",
            "integrity": "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-collection": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3.tgz",
            "integrity": "sha512-mM2pxoQw5HJ49rkzwOs7Y6J4oYH22wS8BfK2/bBxROlI4xuR0c4jEenQP63LlTlDkO6Buj2Vt+QYAYcOgqtrXA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-primitive": "2.0.3",
                "@radix-ui/react-slot": "1.2.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-compose-refs": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz",
            "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-context": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz",
            "integrity": "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-dismissable-layer": {
            "version": "1.1.6",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.6.tgz",
            "integrity": "sha512-7gpgMT2gyKym9Jz2ZhlRXSg2y6cNQIK8d/cqBZ0RBCaps8pFryCWXiUKI+uHGFrhMrbGUP7U6PWgiXzIxoyF3Q==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.2",
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-primitive": "2.0.3",
                "@radix-ui/react-use-callback-ref": "1.1.1",
                "@radix-ui/react-use-escape-keydown": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-portal": {
            "version": "1.1.5",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.5.tgz",
            "integrity": "sha512-ps/67ZqsFm+Mb6lSPJpfhRLrVL2i2fntgCmGMqqth4eaGUf+knAuuRtWVJrNjUhExgmdRqftSgzpf0DF0n6yXA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.3",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-presence": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3.tgz",
            "integrity": "sha512-IrVLIhskYhH3nLvtcBLQFZr61tBG7wx7O3kEmdzcYwRGAEBmBicGGL7ATzNgruYJ3xBTbuzEEq9OXJM3PAX3tA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-primitive": {
            "version": "2.0.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3.tgz",
            "integrity": "sha512-Pf/t/GkndH7CQ8wE2hbkXA+WyZ83fhQQn5DDmwDiDo6AwN/fhaH8oqZ0jRjMrO2iaMhDi6P1HRx6AZwyMinY1g==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-slot": "1.2.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-slot": {
            "version": "1.2.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.0.tgz",
            "integrity": "sha512-ujc+V6r0HNDviYqIK3rW4ffgYiZ8g5DEHrGJVk4x7kTlLXRDILnKX9vAUYeIsLOoDpDJ0ujpqMkjH4w2ofuo6w==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-use-callback-ref": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz",
            "integrity": "sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-use-controllable-state": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.1.1.tgz",
            "integrity": "sha512-YnEXIy8/ga01Y1PN0VfaNH//MhA91JlEGVBDxDzROqwrAtG5Yr2QGEPz8A/rJA3C7ZAHryOYGaUv8fLSW2H/mg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-callback-ref": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-use-escape-keydown": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1.tgz",
            "integrity": "sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-callback-ref": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-use-layout-effect": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz",
            "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-visually-hidden": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.1.3.tgz",
            "integrity": "sha512-oXSF3ZQRd5fvomd9hmUCb2EHSZbPp3ZSHAHJJU/DlF9XoFkJBBW8RHU/E8WEH+RbSfJd/QFA0sl8ClJXknBwHQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.3"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toggle": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.2.tgz",
            "integrity": "sha512-lntKchNWx3aCHuWKiDY+8WudiegQvBpDRAYL8dKLRvKEH8VOpl0XX6SSU/bUBqIRJbcTy4+MW06Wv8vgp10rzQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toggle-group": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.2.tgz",
            "integrity": "sha512-JBm6s6aVG/nwuY5eadhU2zDi/IwYS0sDM5ZWb4nymv/hn3hZdkw+gENn0LP4iY1yCd7+bgJaCwueMYJIU3vk4A==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-roving-focus": "1.1.2",
                "@radix-ui/react-toggle": "1.1.2",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tooltip": {
            "version": "1.1.8",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-tooltip/-/react-tooltip-1.1.8.tgz",
            "integrity": "sha512-YAA2cu48EkJZdAMHC0dqo9kialOcRStbtiY4nJPaht7Ptrhcvpo+eDChaM6BIs8kL6a8Z5l5poiqLnXcNduOkA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-dismissable-layer": "1.1.5",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-popper": "1.2.2",
                "@radix-ui/react-portal": "1.1.4",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.2",
                "@radix-ui/react-slot": "1.1.2",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-visually-hidden": "1.1.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-callback-ref": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0.tgz",
            "integrity": "sha512-CasTfvsy+frcFkbXtSJ2Zu9JHpN8TYKxkgJGWbjiZhFivxaeW7rMeZt7QELGVLaYVfFMsKHjb7Ak0nMEe+2Vfw==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-controllable-state": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.1.0.tgz",
            "integrity": "sha512-MtfMVJiSr2NjzS0Aa90NPTnvTSg6C/JLCV7ma0W6+OMV78vd8OyRpID+Ng9LxzsPbLeuBnWBA1Nq30AtBIDChw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-callback-ref": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-effect-event": {
            "version": "0.0.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2.tgz",
            "integrity": "sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-effect-event/node_modules/@radix-ui/react-use-layout-effect": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz",
            "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-escape-keydown": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0.tgz",
            "integrity": "sha512-L7vwWlR1kTTQ3oh7g1O0CBF3YCyyTj8NmhLR+phShpyA50HCfBFKVJTpshm9PzLiKmehsrQzTYTpX9HvmC9rhw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-callback-ref": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-layout-effect": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.0.tgz",
            "integrity": "sha512-+FPE0rOdziWSrH9athwI1R0HDVbWlEhd+FR+aSDk4uWGmSJ9Z54sdZVDQPZAinJhJXwfT+qnj969mCsT2gfm5w==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-previous": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.0.tgz",
            "integrity": "sha512-Z/e78qg2YFnnXcW88A4JmTtm4ADckLno6F7OXotmkQfeuCVaKuYzqAATPhVzl3delXE7CxIV8shofPn3jPc5Og==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-rect": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0.tgz",
            "integrity": "sha512-0Fmkebhr6PiseyZlYAOtLS+nb7jLmpqTrJyv61Pe68MKYW6OWdRE2kI70TaYY27u7H0lajqM3hSMMLFq18Z7nQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/rect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-size": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.0.tgz",
            "integrity": "sha512-XW3/vWuIXHa+2Uwcc2ABSfcCledmXhhQPlGbfcRXbiUQI5Icjcg19BGCZVKKInYbvUCut/ufbbLLPFC5cbb1hw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-visually-hidden": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.1.2.tgz",
            "integrity": "sha512-1SzA4ns2M1aRlvxErqhLHsBHoS5eI5UUcI2awAMgGUp4LoaoWOKYmvqDY2s/tltuPkh3Yk77YF/r3IRj+Amx4Q==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/rect": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/rect/-/rect-1.1.0.tgz",
            "integrity": "sha512-A9+lCBZoaMJlVKcRBz2YByCG+Cp2t6nAnMnNba+XiWxnj6r4JUFqfsgwocMBZU9LPtdxC6wB56ySYpc7LQIoJg==",
            "license": "MIT"
        },
        "node_modules/@react-aria/focus": {
            "version": "3.19.1",
            "resolved": "https://registry.npmjs.org/@react-aria/focus/-/focus-3.19.1.tgz",
            "integrity": "sha512-bix9Bu1Ue7RPcYmjwcjhB14BMu2qzfJ3tMQLqDc9pweJA66nOw8DThy3IfVr8Z7j2PHktOLf9kcbiZpydKHqzg==",
            "license": "Apache-2.0",
            "dependencies": {
                "@react-aria/interactions": "^3.23.0",
                "@react-aria/utils": "^3.27.0",
                "@react-types/shared": "^3.27.0",
                "@swc/helpers": "^0.5.0",
                "clsx": "^2.0.0"
            },
            "peerDependencies": {
                "react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1",
                "react-dom": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"
            }
        },
        "node_modules/@react-aria/interactions": {
            "version": "3.23.0",
            "resolved": "https://registry.npmjs.org/@react-aria/interactions/-/interactions-3.23.0.tgz",
            "integrity": "sha512-0qR1atBIWrb7FzQ+Tmr3s8uH5mQdyRH78n0krYaG8tng9+u1JlSi8DGRSaC9ezKyNB84m7vHT207xnHXGeJ3Fg==",
            "license": "Apache-2.0",
            "dependencies": {
                "@react-aria/ssr": "^3.9.7",
                "@react-aria/utils": "^3.27.0",
                "@react-types/shared": "^3.27.0",
                "@swc/helpers": "^0.5.0"
            },
            "peerDependencies": {
                "react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1",
                "react-dom": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"
            }
        },
        "node_modules/@react-aria/ssr": {
            "version": "3.9.7",
            "resolved": "https://registry.npmjs.org/@react-aria/ssr/-/ssr-3.9.7.tgz",
            "integrity": "sha512-GQygZaGlmYjmYM+tiNBA5C6acmiDWF52Nqd40bBp0Znk4M4hP+LTmI0lpI1BuKMw45T8RIhrAsICIfKwZvi2Gg==",
            "license": "Apache-2.0",
            "dependencies": {
                "@swc/helpers": "^0.5.0"
            },
            "engines": {
                "node": ">= 12"
            },
            "peerDependencies": {
                "react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"
            }
        },
        "node_modules/@react-aria/utils": {
            "version": "3.27.0",
            "resolved": "https://registry.npmjs.org/@react-aria/utils/-/utils-3.27.0.tgz",
            "integrity": "sha512-p681OtApnKOdbeN8ITfnnYqfdHS0z7GE+4l8EXlfLnr70Rp/9xicBO6d2rU+V/B3JujDw2gPWxYKEnEeh0CGCw==",
            "license": "Apache-2.0",
            "dependencies": {
                "@react-aria/ssr": "^3.9.7",
                "@react-stately/utils": "^3.10.5",
                "@react-types/shared": "^3.27.0",
                "@swc/helpers": "^0.5.0",
                "clsx": "^2.0.0"
            },
            "peerDependencies": {
                "react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1",
                "react-dom": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"
            }
        },
        "node_modules/@react-stately/utils": {
            "version": "3.10.5",
            "resolved": "https://registry.npmjs.org/@react-stately/utils/-/utils-3.10.5.tgz",
            "integrity": "sha512-iMQSGcpaecghDIh3mZEpZfoFH3ExBwTtuBEcvZ2XnGzCgQjeYXcMdIUwAfVQLXFTdHUHGF6Gu6/dFrYsCzySBQ==",
            "license": "Apache-2.0",
            "dependencies": {
                "@swc/helpers": "^0.5.0"
            },
            "peerDependencies": {
                "react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"
            }
        },
        "node_modules/@react-types/shared": {
            "version": "3.27.0",
            "resolved": "https://registry.npmjs.org/@react-types/shared/-/shared-3.27.0.tgz",
            "integrity": "sha512-gvznmLhi6JPEf0bsq7SwRYTHAKKq/wcmKqFez9sRdbED+SPMUmK5omfZ6w3EwUFQHbYUa4zPBYedQ7Knv70RMw==",
            "license": "Apache-2.0",
            "peerDependencies": {
                "react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"
            }
        },
        "node_modules/@rollup/rollup-android-arm-eabi": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.9.tgz",
            "integrity": "sha512-qZdlImWXur0CFakn2BJ2znJOdqYZKiedEPEVNTBrpfPjc/YuTGcaYZcdmNFTkUj3DU0ZM/AElcM8Ybww3xVLzA==",
            "cpu": [
                "arm"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "android"
            ]
        },
        "node_modules/@rollup/rollup-android-arm64": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.9.tgz",
            "integrity": "sha512-4KW7P53h6HtJf5Y608T1ISKvNIYLWRKMvfnG0c44M6In4DQVU58HZFEVhWINDZKp7FZps98G3gxwC1sb0wXUUg==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "android"
            ]
        },
        "node_modules/@rollup/rollup-darwin-arm64": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.9.tgz",
            "integrity": "sha512-0CY3/K54slrzLDjOA7TOjN1NuLKERBgk9nY5V34mhmuu673YNb+7ghaDUs6N0ujXR7fz5XaS5Aa6d2TNxZd0OQ==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "darwin"
            ]
        },
        "node_modules/@rollup/rollup-darwin-x64": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.9.tgz",
            "integrity": "sha512-eOojSEAi/acnsJVYRxnMkPFqcxSMFfrw7r2iD9Q32SGkb/Q9FpUY1UlAu1DH9T7j++gZ0lHjnm4OyH2vCI7l7Q==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "darwin"
            ]
        },
        "node_modules/@rollup/rollup-freebsd-arm64": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.9.tgz",
            "integrity": "sha512-2lzjQPJbN5UnHm7bHIUKFMulGTQwdvOkouJDpPysJS+QFBGDJqcfh+CxxtG23Ik/9tEvnebQiylYoazFMAgrYw==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "freebsd"
            ]
        },
        "node_modules/@rollup/rollup-freebsd-x64": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.9.tgz",
            "integrity": "sha512-SLl0hi2Ah2H7xQYd6Qaiu01kFPzQ+hqvdYSoOtHYg/zCIFs6t8sV95kaoqjzjFwuYQLtOI0RZre/Ke0nPaQV+g==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "freebsd"
            ]
        },
        "node_modules/@rollup/rollup-linux-arm-gnueabihf": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.9.tgz",
            "integrity": "sha512-88I+D3TeKItrw+Y/2ud4Tw0+3CxQ2kLgu3QvrogZ0OfkmX/DEppehus7L3TS2Q4lpB+hYyxhkQiYPJ6Mf5/dPg==",
            "cpu": [
                "arm"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ]
        },
        "node_modules/@rollup/rollup-linux-arm-musleabihf": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.34.9.tgz",
            "integrity": "sha512-3qyfWljSFHi9zH0KgtEPG4cBXHDFhwD8kwg6xLfHQ0IWuH9crp005GfoUUh/6w9/FWGBwEHg3lxK1iHRN1MFlA==",
            "cpu": [
                "arm"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ]
        },
        "node_modules/@rollup/rollup-linux-arm64-gnu": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.9.tgz",
            "integrity": "sha512-6TZjPHjKZUQKmVKMUowF3ewHxctrRR09eYyvT5eFv8w/fXarEra83A2mHTVJLA5xU91aCNOUnM+DWFMSbQ0Nxw==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ]
        },
        "node_modules/@rollup/rollup-linux-arm64-musl": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.9.tgz",
            "integrity": "sha512-LD2fytxZJZ6xzOKnMbIpgzFOuIKlxVOpiMAXawsAZ2mHBPEYOnLRK5TTEsID6z4eM23DuO88X0Tq1mErHMVq0A==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ]
        },
        "node_modules/@rollup/rollup-linux-loongarch64-gnu": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.9.tgz",
            "integrity": "sha512-dRAgTfDsn0TE0HI6cmo13hemKpVHOEyeciGtvlBTkpx/F65kTvShtY/EVyZEIfxFkV5JJTuQ9tP5HGBS0hfxIg==",
            "cpu": [
                "loong64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ]
        },
        "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.9.tgz",
            "integrity": "sha512-PHcNOAEhkoMSQtMf+rJofwisZqaU8iQ8EaSps58f5HYll9EAY5BSErCZ8qBDMVbq88h4UxaNPlbrKqfWP8RfJA==",
            "cpu": [
                "ppc64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ]
        },
        "node_modules/@rollup/rollup-linux-riscv64-gnu": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.9.tgz",
            "integrity": "sha512-Z2i0Uy5G96KBYKjeQFKbbsB54xFOL5/y1P5wNBsbXB8yE+At3oh0DVMjQVzCJRJSfReiB2tX8T6HUFZ2k8iaKg==",
            "cpu": [
                "riscv64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ]
        },
        "node_modules/@rollup/rollup-linux-s390x-gnu": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.9.tgz",
            "integrity": "sha512-U+5SwTMoeYXoDzJX5dhDTxRltSrIax8KWwfaaYcynuJw8mT33W7oOgz0a+AaXtGuvhzTr2tVKh5UO8GVANTxyQ==",
            "cpu": [
                "s390x"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ]
        },
        "node_modules/@rollup/rollup-linux-x64-gnu": {
            "version": "4.9.5",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.9.5.tgz",
            "integrity": "sha512-Dq1bqBdLaZ1Gb/l2e5/+o3B18+8TI9ANlA1SkejZqDgdU/jK/ThYaMPMJpVMMXy2uRHvGKbkz9vheVGdq3cJfA==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ]
        },
        "node_modules/@rollup/rollup-linux-x64-musl": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.9.tgz",
            "integrity": "sha512-cYRpV4650z2I3/s6+5/LONkjIz8MBeqrk+vPXV10ORBnshpn8S32bPqQ2Utv39jCiDcO2eJTuSlPXpnvmaIgRA==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ]
        },
        "node_modules/@rollup/rollup-win32-arm64-msvc": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.9.tgz",
            "integrity": "sha512-z4mQK9dAN6byRA/vsSgQiPeuO63wdiDxZ9yg9iyX2QTzKuQM7T4xlBoeUP/J8uiFkqxkcWndWi+W7bXdPbt27Q==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "win32"
            ]
        },
        "node_modules/@rollup/rollup-win32-ia32-msvc": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.9.tgz",
            "integrity": "sha512-KB48mPtaoHy1AwDNkAJfHXvHp24H0ryZog28spEs0V48l3H1fr4i37tiyHsgKZJnCmvxsbATdZGBpbmxTE3a9w==",
            "cpu": [
                "ia32"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "win32"
            ]
        },
        "node_modules/@rollup/rollup-win32-x64-msvc": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.9.tgz",
            "integrity": "sha512-AyleYRPU7+rgkMWbEh71fQlrzRfeP6SyMnRf9XX4fCdDPAJumdSBqYEcWPMzVQ4ScAl7E4oFfK0GUVn77xSwbw==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "win32"
            ]
        },
        "node_modules/@swc/helpers": {
            "version": "0.5.15",
            "resolved": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.15.tgz",
            "integrity": "sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==",
            "license": "Apache-2.0",
            "dependencies": {
                "tslib": "^2.8.0"
            }
        },
        "node_modules/@tailwindcss/node": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/node/-/node-4.0.10.tgz",
            "integrity": "sha512-5YuI8pXfNkg5Ng12wgMic6jrFe4K8+eVmaC1kLsbA6g7iMgrj5fyl4hoLqHjmBDGpJXKxUAjwMSuJmc4oetnrg==",
            "license": "MIT",
            "dependencies": {
                "enhanced-resolve": "^5.18.1",
                "jiti": "^2.4.2",
                "tailwindcss": "4.0.10"
            }
        },
        "node_modules/@tailwindcss/oxide": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/oxide/-/oxide-4.0.10.tgz",
            "integrity": "sha512-vAPYXF1c2yH8jmepA82on3kLpgrHZQ0B7Q2tPeASXnKxJx3GP/Fe0j1RB6PDmR5UntwA0y0Z0bZYwLcnw4/OGw==",
            "license": "MIT",
            "engines": {
                "node": ">= 10"
            },
            "optionalDependencies": {
                "@tailwindcss/oxide-android-arm64": "4.0.10",
                "@tailwindcss/oxide-darwin-arm64": "4.0.10",
                "@tailwindcss/oxide-darwin-x64": "4.0.10",
                "@tailwindcss/oxide-freebsd-x64": "4.0.10",
                "@tailwindcss/oxide-linux-arm-gnueabihf": "4.0.10",
                "@tailwindcss/oxide-linux-arm64-gnu": "4.0.10",
                "@tailwindcss/oxide-linux-arm64-musl": "4.0.10",
                "@tailwindcss/oxide-linux-x64-gnu": "4.0.10",
                "@tailwindcss/oxide-linux-x64-musl": "4.0.10",
                "@tailwindcss/oxide-win32-arm64-msvc": "4.0.10",
                "@tailwindcss/oxide-win32-x64-msvc": "4.0.10"
            }
        },
        "node_modules/@tailwindcss/oxide-android-arm64": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-android-arm64/-/oxide-android-arm64-4.0.10.tgz",
            "integrity": "sha512-HymaBJV/oB7fAMabW/EdWBrNskw9BOXoChYVnk/n3xq9LpK3eWNOcLeB4P52Bks+OpAyv8u0I/0WdrOkPRPv0A==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "android"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@tailwindcss/oxide-darwin-arm64": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-darwin-arm64/-/oxide-darwin-arm64-4.0.10.tgz",
            "integrity": "sha512-PJtNobUOQCydEpBbOmVhP+diTD8JEM7HRxgX9O72SODg+ynKDM0fNDkqKOX0CFR6+mCdOwRQdhnoulM6hM27TA==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "darwin"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@tailwindcss/oxide-darwin-x64": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-darwin-x64/-/oxide-darwin-x64-4.0.10.tgz",
            "integrity": "sha512-jUqYWjThIoLEUTX5WGwukGh0js+RGGFqjt0YhQnDyCDofBD/CBxOdbrsXX6CnYmbGw+a3BDrl0r3xbPY2fX8Mw==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "darwin"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@tailwindcss/oxide-freebsd-x64": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-freebsd-x64/-/oxide-freebsd-x64-4.0.10.tgz",
            "integrity": "sha512-m4SdTo/MkZJX2FEyiOjtQAsKG17q9d/RJXTlXDu6owVIM/U9TG0Vy3XdW/L4Yh0mHsayhHUJVIpvV0ZaWMs7nQ==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "freebsd"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@tailwindcss/oxide-linux-arm-gnueabihf": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-linux-arm-gnueabihf/-/oxide-linux-arm-gnueabihf-4.0.10.tgz",
            "integrity": "sha512-cdq+Xa4cgYOYgg2n8RdL2/COIuW0FZJRvSg+AtGuZWG0omVS9XIf/wLlL+ln7pCTMt9zGOX1Yyryfrw12tYw4Q==",
            "cpu": [
                "arm"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@tailwindcss/oxide-linux-arm64-gnu": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-linux-arm64-gnu/-/oxide-linux-arm64-gnu-4.0.10.tgz",
            "integrity": "sha512-6PMpTsv8vE0xiaPnpOptSvO99JkIqW9KrtmPYp/Khr6i9AkVmf95XGQxqcgwlU7Gdo7eb02fK5z0c5crK/pTew==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@tailwindcss/oxide-linux-arm64-musl": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-linux-arm64-musl/-/oxide-linux-arm64-musl-4.0.10.tgz",
            "integrity": "sha512-tI264V1H4yxRnYaOzYWm+5x94QtoreoBpVkX0OpQTycvnv6JPUC6wqsZkrDwpphaDitUGY+mv7rGQZ5vzB/Tlg==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@tailwindcss/oxide-linux-x64-gnu": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-linux-x64-gnu/-/oxide-linux-x64-gnu-4.0.10.tgz",
            "integrity": "sha512-Xe15DqfzcYzozbhhgTUeZNnmnr56HdnqeollvLumxKvrCicDFkeZimz299Czyw4GeRUHZgcdccwr+Do3/Y2aZA==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@tailwindcss/oxide-linux-x64-musl": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-linux-x64-musl/-/oxide-linux-x64-musl-4.0.10.tgz",
            "integrity": "sha512-L0NTk+UPpx4l/xD0G+UDBYhu6whA7xh415nErEnliFK8KV5lQlWz66icpHLmT4fTpAZTBaD+ul+GorlL1D1xCg==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@tailwindcss/oxide-win32-arm64-msvc": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.0.10.tgz",
            "integrity": "sha512-IXNvUmLBmTJNcMofOl8B0fzNvwUFPNvFE799THaEPgi16zj+WqFLVQh4N5+zuI1vgtZTaIJrZmqHhjqNPLOItg==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "win32"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@tailwindcss/oxide-win32-x64-msvc": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-win32-x64-msvc/-/oxide-win32-x64-msvc-4.0.10.tgz",
            "integrity": "sha512-K/51OZBREcq2J4JE8r9qdX2qjnVfUrm8AT4R+Pd9E27AiIyr7IkLQQjR3mj2Lpb/jUtQ8NS0KkJ1nXMoQpSlkQ==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "win32"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@tailwindcss/vite": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.0.10.tgz",
            "integrity": "sha512-SFY/FgEj68k/6o3Q0PxoZK6KzQZV9T4yMy+kwOGq17NOWXAyDJ+Fagz3tkzqhzKpWTzMMPFfIo+g5r3seyp6uQ==",
            "license": "MIT",
            "dependencies": {
                "@tailwindcss/node": "4.0.10",
                "@tailwindcss/oxide": "4.0.10",
                "lightningcss": "^1.29.1",
                "tailwindcss": "4.0.10"
            },
            "peerDependencies": {
                "vite": "^5.2.0 || ^6"
            }
        },
        "node_modules/@tanstack/react-virtual": {
            "version": "3.13.2",
            "resolved": "https://registry.npmjs.org/@tanstack/react-virtual/-/react-virtual-3.13.2.tgz",
            "integrity": "sha512-LceSUgABBKF6HSsHK2ZqHzQ37IKV/jlaWbHm+NyTa3/WNb/JZVcThDuTainf+PixltOOcFCYXwxbLpOX9sCx+g==",
            "license": "MIT",
            "dependencies": {
                "@tanstack/virtual-core": "3.13.2"
            },
            "funding": {
                "type": "github",
                "url": "https://github.com/sponsors/tannerlinsley"
            },
            "peerDependencies": {
                "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0",
                "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"
            }
        },
        "node_modules/@tanstack/virtual-core": {
            "version": "3.13.2",
            "resolved": "https://registry.npmjs.org/@tanstack/virtual-core/-/virtual-core-3.13.2.tgz",
            "integrity": "sha512-Qzz4EgzMbO5gKrmqUondCjiHcuu4B1ftHb0pjCut661lXZdGoHeze9f/M8iwsK1t5LGR6aNuNGU7mxkowaW6RQ==",
            "license": "MIT",
            "funding": {
                "type": "github",
                "url": "https://github.com/sponsors/tannerlinsley"
            }
        },
        "node_modules/@types/babel__core": {
            "version": "7.20.5",
            "resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz",
            "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==",
            "license": "MIT",
            "dependencies": {
                "@babel/parser": "^7.20.7",
                "@babel/types": "^7.20.7",
                "@types/babel__generator": "*",
                "@types/babel__template": "*",
                "@types/babel__traverse": "*"
            }
        },
        "node_modules/@types/babel__generator": {
            "version": "7.6.8",
            "resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.8.tgz",
            "integrity": "sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==",
            "license": "MIT",
            "dependencies": {
                "@babel/types": "^7.0.0"
            }
        },
        "node_modules/@types/babel__template": {
            "version": "7.4.4",
            "resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz",
            "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==",
            "license": "MIT",
            "dependencies": {
                "@babel/parser": "^7.1.0",
                "@babel/types": "^7.0.0"
            }
        },
        "node_modules/@types/babel__traverse": {
            "version": "7.20.6",
            "resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.6.tgz",
            "integrity": "sha512-r1bzfrm0tomOI8g1SzvCaQHo6Lcv6zu0EA+W2kHrt8dyrHQxGzBBL4kdkzIS+jBMV+EYcMAEAqXqYaLJq5rOZg==",
            "license": "MIT",
            "dependencies": {
                "@babel/types": "^7.20.7"
            }
        },
        "node_modules/@types/estree": {
            "version": "1.0.6",
            "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.6.tgz",
            "integrity": "sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==",
            "license": "MIT"
        },
        "node_modules/@types/json-schema": {
            "version": "7.0.15",
            "resolved": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz",
            "integrity": "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/@types/node": {
            "version": "22.13.9",
            "resolved": "https://registry.npmjs.org/@types/node/-/node-22.13.9.tgz",
            "integrity": "sha512-acBjXdRJ3A6Pb3tqnw9HZmyR3Fiol3aGxRCK1x3d+6CDAMjl7I649wpSd+yNURCjbOUGu9tqtLKnTGxmK6CyGw==",
            "devOptional": true,
            "license": "MIT",
            "dependencies": {
                "undici-types": "~6.20.0"
            }
        },
        "node_modules/@types/react": {
            "version": "19.0.10",
            "resolved": "https://registry.npmjs.org/@types/react/-/react-19.0.10.tgz",
            "integrity": "sha512-JuRQ9KXLEjaUNjTWpzuR231Z2WpIwczOkBEIvbHNCzQefFIT0L8IqE6NV6ULLyC1SI/i234JnDoMkfg+RjQj2g==",
            "license": "MIT",
            "dependencies": {
                "csstype": "^3.0.2"
            }
        },
        "node_modules/@types/react-dom": {
            "version": "19.0.4",
            "resolved": "https://registry.npmjs.org/@types/react-dom/-/react-dom-19.0.4.tgz",
            "integrity": "sha512-4fSQ8vWFkg+TGhePfUzVmat3eC14TXYSsiiDSLI0dVLsrm9gZFABjPy/Qu6TKgl1tq1Bu1yDsuQgY3A3DOjCcg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "^19.0.0"
            }
        },
        "node_modules/@typescript-eslint/eslint-plugin": {
            "version": "8.26.0",
            "resolved": "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.26.0.tgz",
            "integrity": "sha512-cLr1J6pe56zjKYajK6SSSre6nl1Gj6xDp1TY0trpgPzjVbgDwd09v2Ws37LABxzkicmUjhEeg/fAUjPJJB1v5Q==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@eslint-community/regexpp": "^4.10.0",
                "@typescript-eslint/scope-manager": "8.26.0",
                "@typescript-eslint/type-utils": "8.26.0",
                "@typescript-eslint/utils": "8.26.0",
                "@typescript-eslint/visitor-keys": "8.26.0",
                "graphemer": "^1.4.0",
                "ignore": "^5.3.1",
                "natural-compare": "^1.4.0",
                "ts-api-utils": "^2.0.1"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/typescript-eslint"
            },
            "peerDependencies": {
                "@typescript-eslint/parser": "^8.0.0 || ^8.0.0-alpha.0",
                "eslint": "^8.57.0 || ^9.0.0",
                "typescript": ">=4.8.4 <5.9.0"
            }
        },
        "node_modules/@typescript-eslint/parser": {
            "version": "8.26.0",
            "resolved": "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-8.26.0.tgz",
            "integrity": "sha512-mNtXP9LTVBy14ZF3o7JG69gRPBK/2QWtQd0j0oH26HcY/foyJJau6pNUez7QrM5UHnSvwlQcJXKsk0I99B9pOA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@typescript-eslint/scope-manager": "8.26.0",
                "@typescript-eslint/types": "8.26.0",
                "@typescript-eslint/typescript-estree": "8.26.0",
                "@typescript-eslint/visitor-keys": "8.26.0",
                "debug": "^4.3.4"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/typescript-eslint"
            },
            "peerDependencies": {
                "eslint": "^8.57.0 || ^9.0.0",
                "typescript": ">=4.8.4 <5.9.0"
            }
        },
        "node_modules/@typescript-eslint/scope-manager": {
            "version": "8.26.0",
            "resolved": "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-8.26.0.tgz",
            "integrity": "sha512-E0ntLvsfPqnPwng8b8y4OGuzh/iIOm2z8U3S9zic2TeMLW61u5IH2Q1wu0oSTkfrSzwbDJIB/Lm8O3//8BWMPA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@typescript-eslint/types": "8.26.0",
                "@typescript-eslint/visitor-keys": "8.26.0"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/typescript-eslint"
            }
        },
        "node_modules/@typescript-eslint/type-utils": {
            "version": "8.26.0",
            "resolved": "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-8.26.0.tgz",
            "integrity": "sha512-ruk0RNChLKz3zKGn2LwXuVoeBcUMh+jaqzN461uMMdxy5H9epZqIBtYj7UiPXRuOpaALXGbmRuZQhmwHhaS04Q==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@typescript-eslint/typescript-estree": "8.26.0",
                "@typescript-eslint/utils": "8.26.0",
                "debug": "^4.3.4",
                "ts-api-utils": "^2.0.1"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/typescript-eslint"
            },
            "peerDependencies": {
                "eslint": "^8.57.0 || ^9.0.0",
                "typescript": ">=4.8.4 <5.9.0"
            }
        },
        "node_modules/@typescript-eslint/types": {
            "version": "8.26.0",
            "resolved": "https://registry.npmjs.org/@typescript-eslint/types/-/types-8.26.0.tgz",
            "integrity": "sha512-89B1eP3tnpr9A8L6PZlSjBvnJhWXtYfZhECqlBl1D9Lme9mHO6iWlsprBtVenQvY1HMhax1mWOjhtL3fh/u+pA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/typescript-eslint"
            }
        },
        "node_modules/@typescript-eslint/typescript-estree": {
            "version": "8.26.0",
            "resolved": "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-8.26.0.tgz",
            "integrity": "sha512-tiJ1Hvy/V/oMVRTbEOIeemA2XoylimlDQ03CgPPNaHYZbpsc78Hmngnt+WXZfJX1pjQ711V7g0H7cSJThGYfPQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@typescript-eslint/types": "8.26.0",
                "@typescript-eslint/visitor-keys": "8.26.0",
                "debug": "^4.3.4",
                "fast-glob": "^3.3.2",
                "is-glob": "^4.0.3",
                "minimatch": "^9.0.4",
                "semver": "^7.6.0",
                "ts-api-utils": "^2.0.1"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/typescript-eslint"
            },
            "peerDependencies": {
                "typescript": ">=4.8.4 <5.9.0"
            }
        },
        "node_modules/@typescript-eslint/typescript-estree/node_modules/brace-expansion": {
            "version": "2.0.1",
            "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz",
            "integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "balanced-match": "^1.0.0"
            }
        },
        "node_modules/@typescript-eslint/typescript-estree/node_modules/minimatch": {
            "version": "9.0.5",
            "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz",
            "integrity": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "brace-expansion": "^2.0.1"
            },
            "engines": {
                "node": ">=16 || 14 >=14.17"
            },
            "funding": {
                "url": "https://github.com/sponsors/isaacs"
            }
        },
        "node_modules/@typescript-eslint/typescript-estree/node_modules/semver": {
            "version": "7.7.1",
            "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz",
            "integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==",
            "dev": true,
            "license": "ISC",
            "bin": {
                "semver": "bin/semver.js"
            },
            "engines": {
                "node": ">=10"
            }
        },
        "node_modules/@typescript-eslint/utils": {
            "version": "8.26.0",
            "resolved": "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-8.26.0.tgz",
            "integrity": "sha512-2L2tU3FVwhvU14LndnQCA2frYC8JnPDVKyQtWFPf8IYFMt/ykEN1bPolNhNbCVgOmdzTlWdusCTKA/9nKrf8Ig==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@eslint-community/eslint-utils": "^4.4.0",
                "@typescript-eslint/scope-manager": "8.26.0",
                "@typescript-eslint/types": "8.26.0",
                "@typescript-eslint/typescript-estree": "8.26.0"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/typescript-eslint"
            },
            "peerDependencies": {
                "eslint": "^8.57.0 || ^9.0.0",
                "typescript": ">=4.8.4 <5.9.0"
            }
        },
        "node_modules/@typescript-eslint/visitor-keys": {
            "version": "8.26.0",
            "resolved": "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-8.26.0.tgz",
            "integrity": "sha512-2z8JQJWAzPdDd51dRQ/oqIJxe99/hoLIqmf8RMCAJQtYDc535W/Jt2+RTP4bP0aKeBG1F65yjIZuczOXCmbWwg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@typescript-eslint/types": "8.26.0",
                "eslint-visitor-keys": "^4.2.0"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/typescript-eslint"
            }
        },
        "node_modules/@vitejs/plugin-react": {
            "version": "4.3.4",
            "resolved": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.3.4.tgz",
            "integrity": "sha512-SCCPBJtYLdE8PX/7ZQAs1QAZ8Jqwih+0VBLum1EGqmCCQal+MIUqLCzj3ZUy8ufbC0cAM4LRlSTm7IQJwWT4ug==",
            "license": "MIT",
            "dependencies": {
                "@babel/core": "^7.26.0",
                "@babel/plugin-transform-react-jsx-self": "^7.25.9",
                "@babel/plugin-transform-react-jsx-source": "^7.25.9",
                "@types/babel__core": "^7.20.5",
                "react-refresh": "^0.14.2"
            },
            "engines": {
                "node": "^14.18.0 || >=16.0.0"
            },
            "peerDependencies": {
                "vite": "^4.2.0 || ^5.0.0 || ^6.0.0"
            }
        },
        "node_modules/acorn": {
            "version": "8.14.1",
            "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz",
            "integrity": "sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg==",
            "dev": true,
            "license": "MIT",
            "bin": {
                "acorn": "bin/acorn"
            },
            "engines": {
                "node": ">=0.4.0"
            }
        },
        "node_modules/acorn-jsx": {
            "version": "5.3.2",
            "resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz",
            "integrity": "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==",
            "dev": true,
            "license": "MIT",
            "peerDependencies": {
                "acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"
            }
        },
        "node_modules/ajv": {
            "version": "6.12.6",
            "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz",
            "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "fast-deep-equal": "^3.1.1",
                "fast-json-stable-stringify": "^2.0.0",
                "json-schema-traverse": "^0.4.1",
                "uri-js": "^4.2.2"
            },
            "funding": {
                "type": "github",
                "url": "https://github.com/sponsors/epoberezkin"
            }
        },
        "node_modules/ansi-regex": {
            "version": "5.0.1",
            "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
            "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
            "license": "MIT",
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/ansi-styles": {
            "version": "4.3.0",
            "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz",
            "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==",
            "license": "MIT",
            "dependencies": {
                "color-convert": "^2.0.1"
            },
            "engines": {
                "node": ">=8"
            },
            "funding": {
                "url": "https://github.com/chalk/ansi-styles?sponsor=1"
            }
        },
        "node_modules/argparse": {
            "version": "2.0.1",
            "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz",
            "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==",
            "dev": true,
            "license": "Python-2.0"
        },
        "node_modules/aria-hidden": {
            "version": "1.2.4",
            "resolved": "https://registry.npmjs.org/aria-hidden/-/aria-hidden-1.2.4.tgz",
            "integrity": "sha512-y+CcFFwelSXpLZk/7fMB2mUbGtX9lKycf1MWJ7CaTIERyitVlyQx6C+sxcROU2BAJ24OiZyK+8wj2i8AlBoS3A==",
            "license": "MIT",
            "dependencies": {
                "tslib": "^2.0.0"
            },
            "engines": {
                "node": ">=10"
            }
        },
        "node_modules/array-buffer-byte-length": {
            "version": "1.0.2",
            "resolved": "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz",
            "integrity": "sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3",
                "is-array-buffer": "^3.0.5"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/array-includes": {
            "version": "3.1.8",
            "resolved": "https://registry.npmjs.org/array-includes/-/array-includes-3.1.8.tgz",
            "integrity": "sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.7",
                "define-properties": "^1.2.1",
                "es-abstract": "^1.23.2",
                "es-object-atoms": "^1.0.0",
                "get-intrinsic": "^1.2.4",
                "is-string": "^1.0.7"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/array.prototype.findlast": {
            "version": "1.2.5",
            "resolved": "https://registry.npmjs.org/array.prototype.findlast/-/array.prototype.findlast-1.2.5.tgz",
            "integrity": "sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.7",
                "define-properties": "^1.2.1",
                "es-abstract": "^1.23.2",
                "es-errors": "^1.3.0",
                "es-object-atoms": "^1.0.0",
                "es-shim-unscopables": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/array.prototype.flat": {
            "version": "1.3.3",
            "resolved": "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.3.tgz",
            "integrity": "sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "define-properties": "^1.2.1",
                "es-abstract": "^1.23.5",
                "es-shim-unscopables": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/array.prototype.flatmap": {
            "version": "1.3.3",
            "resolved": "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.3.tgz",
            "integrity": "sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "define-properties": "^1.2.1",
                "es-abstract": "^1.23.5",
                "es-shim-unscopables": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/array.prototype.tosorted": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/array.prototype.tosorted/-/array.prototype.tosorted-1.1.4.tgz",
            "integrity": "sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.7",
                "define-properties": "^1.2.1",
                "es-abstract": "^1.23.3",
                "es-errors": "^1.3.0",
                "es-shim-unscopables": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/arraybuffer.prototype.slice": {
            "version": "1.0.4",
            "resolved": "https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz",
            "integrity": "sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "array-buffer-byte-length": "^1.0.1",
                "call-bind": "^1.0.8",
                "define-properties": "^1.2.1",
                "es-abstract": "^1.23.5",
                "es-errors": "^1.3.0",
                "get-intrinsic": "^1.2.6",
                "is-array-buffer": "^3.0.4"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/async-function": {
            "version": "1.0.0",
            "resolved": "https://registry.npmjs.org/async-function/-/async-function-1.0.0.tgz",
            "integrity": "sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/asynckit": {
            "version": "0.4.0",
            "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz",
            "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==",
            "license": "MIT"
        },
        "node_modules/available-typed-arrays": {
            "version": "1.0.7",
            "resolved": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz",
            "integrity": "sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "possible-typed-array-names": "^1.0.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/axios": {
            "version": "1.8.4",
            "resolved": "https://registry.npmjs.org/axios/-/axios-1.8.4.tgz",
            "integrity": "sha512-eBSYY4Y68NNlHbHBMdeDmKNtDgXWhQsJcGqzO3iLUM0GraQFSS9cVgPX5I9b3lbdFKyYoAEGAZF1DwhTaljNAw==",
            "license": "MIT",
            "dependencies": {
                "follow-redirects": "^1.15.6",
                "form-data": "^4.0.0",
                "proxy-from-env": "^1.1.0"
            }
        },
        "node_modules/balanced-match": {
            "version": "1.0.2",
            "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz",
            "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/brace-expansion": {
            "version": "1.1.11",
            "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz",
            "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "balanced-match": "^1.0.0",
                "concat-map": "0.0.1"
            }
        },
        "node_modules/braces": {
            "version": "3.0.3",
            "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz",
            "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "fill-range": "^7.1.1"
            },
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/browserslist": {
            "version": "4.24.4",
            "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz",
            "integrity": "sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==",
            "funding": [
                {
                    "type": "opencollective",
                    "url": "https://opencollective.com/browserslist"
                },
                {
                    "type": "tidelift",
                    "url": "https://tidelift.com/funding/github/npm/browserslist"
                },
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "dependencies": {
                "caniuse-lite": "^1.0.30001688",
                "electron-to-chromium": "^1.5.73",
                "node-releases": "^2.0.19",
                "update-browserslist-db": "^1.1.1"
            },
            "bin": {
                "browserslist": "cli.js"
            },
            "engines": {
                "node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"
            }
        },
        "node_modules/call-bind": {
            "version": "1.0.8",
            "resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz",
            "integrity": "sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind-apply-helpers": "^1.0.0",
                "es-define-property": "^1.0.0",
                "get-intrinsic": "^1.2.4",
                "set-function-length": "^1.2.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/call-bind-apply-helpers": {
            "version": "1.0.2",
            "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz",
            "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==",
            "license": "MIT",
            "dependencies": {
                "es-errors": "^1.3.0",
                "function-bind": "^1.1.2"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/call-bound": {
            "version": "1.0.4",
            "resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz",
            "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==",
            "license": "MIT",
            "dependencies": {
                "call-bind-apply-helpers": "^1.0.2",
                "get-intrinsic": "^1.3.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/callsites": {
            "version": "3.1.0",
            "resolved": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz",
            "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=6"
            }
        },
        "node_modules/caniuse-lite": {
            "version": "1.0.30001702",
            "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001702.tgz",
            "integrity": "sha512-LoPe/D7zioC0REI5W73PeR1e1MLCipRGq/VkovJnd6Df+QVqT+vT33OXCp8QUd7kA7RZrHWxb1B36OQKI/0gOA==",
            "funding": [
                {
                    "type": "opencollective",
                    "url": "https://opencollective.com/browserslist"
                },
                {
                    "type": "tidelift",
                    "url": "https://tidelift.com/funding/github/npm/caniuse-lite"
                },
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "CC-BY-4.0"
        },
        "node_modules/chalk": {
            "version": "4.1.2",
            "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
            "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
            "license": "MIT",
            "dependencies": {
                "ansi-styles": "^4.1.0",
                "supports-color": "^7.1.0"
            },
            "engines": {
                "node": ">=10"
            },
            "funding": {
                "url": "https://github.com/chalk/chalk?sponsor=1"
            }
        },
        "node_modules/chalk/node_modules/supports-color": {
            "version": "7.2.0",
            "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz",
            "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==",
            "license": "MIT",
            "dependencies": {
                "has-flag": "^4.0.0"
            },
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/class-variance-authority": {
            "version": "0.7.1",
            "resolved": "https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.1.tgz",
            "integrity": "sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==",
            "license": "Apache-2.0",
            "dependencies": {
                "clsx": "^2.1.1"
            },
            "funding": {
                "url": "https://polar.sh/cva"
            }
        },
        "node_modules/cliui": {
            "version": "8.0.1",
            "resolved": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz",
            "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==",
            "license": "ISC",
            "dependencies": {
                "string-width": "^4.2.0",
                "strip-ansi": "^6.0.1",
                "wrap-ansi": "^7.0.0"
            },
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/clsx": {
            "version": "2.1.1",
            "resolved": "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz",
            "integrity": "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==",
            "license": "MIT",
            "engines": {
                "node": ">=6"
            }
        },
        "node_modules/color-convert": {
            "version": "2.0.1",
            "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz",
            "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==",
            "license": "MIT",
            "dependencies": {
                "color-name": "~1.1.4"
            },
            "engines": {
                "node": ">=7.0.0"
            }
        },
        "node_modules/color-name": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz",
            "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==",
            "license": "MIT"
        },
        "node_modules/combined-stream": {
            "version": "1.0.8",
            "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz",
            "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==",
            "license": "MIT",
            "dependencies": {
                "delayed-stream": "~1.0.0"
            },
            "engines": {
                "node": ">= 0.8"
            }
        },
        "node_modules/concat-map": {
            "version": "0.0.1",
            "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz",
            "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/concurrently": {
            "version": "9.1.2",
            "resolved": "https://registry.npmjs.org/concurrently/-/concurrently-9.1.2.tgz",
            "integrity": "sha512-H9MWcoPsYddwbOGM6difjVwVZHl63nwMEwDJG/L7VGtuaJhb12h2caPG2tVPWs7emuYix252iGfqOyrz1GczTQ==",
            "license": "MIT",
            "dependencies": {
                "chalk": "^4.1.2",
                "lodash": "^4.17.21",
                "rxjs": "^7.8.1",
                "shell-quote": "^1.8.1",
                "supports-color": "^8.1.1",
                "tree-kill": "^1.2.2",
                "yargs": "^17.7.2"
            },
            "bin": {
                "conc": "dist/bin/concurrently.js",
                "concurrently": "dist/bin/concurrently.js"
            },
            "engines": {
                "node": ">=18"
            },
            "funding": {
                "url": "https://github.com/open-cli-tools/concurrently?sponsor=1"
            }
        },
        "node_modules/convert-source-map": {
            "version": "2.0.0",
            "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz",
            "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==",
            "license": "MIT"
        },
        "node_modules/cross-spawn": {
            "version": "7.0.6",
            "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz",
            "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "path-key": "^3.1.0",
                "shebang-command": "^2.0.0",
                "which": "^2.0.1"
            },
            "engines": {
                "node": ">= 8"
            }
        },
        "node_modules/csstype": {
            "version": "3.1.3",
            "resolved": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz",
            "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==",
            "license": "MIT"
        },
        "node_modules/data-view-buffer": {
            "version": "1.0.2",
            "resolved": "https://registry.npmjs.org/data-view-buffer/-/data-view-buffer-1.0.2.tgz",
            "integrity": "sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3",
                "es-errors": "^1.3.0",
                "is-data-view": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/data-view-byte-length": {
            "version": "1.0.2",
            "resolved": "https://registry.npmjs.org/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz",
            "integrity": "sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3",
                "es-errors": "^1.3.0",
                "is-data-view": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/inspect-js"
            }
        },
        "node_modules/data-view-byte-offset": {
            "version": "1.0.1",
            "resolved": "https://registry.npmjs.org/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz",
            "integrity": "sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.2",
                "es-errors": "^1.3.0",
                "is-data-view": "^1.0.1"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/date-fns": {
            "version": "4.1.0",
            "resolved": "https://registry.npmjs.org/date-fns/-/date-fns-4.1.0.tgz",
            "integrity": "sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg==",
            "license": "MIT",
            "funding": {
                "type": "github",
                "url": "https://github.com/sponsors/kossnocorp"
            }
        },
        "node_modules/debug": {
            "version": "4.4.0",
            "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz",
            "integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==",
            "license": "MIT",
            "dependencies": {
                "ms": "^2.1.3"
            },
            "engines": {
                "node": ">=6.0"
            },
            "peerDependenciesMeta": {
                "supports-color": {
                    "optional": true
                }
            }
        },
        "node_modules/deep-is": {
            "version": "0.1.4",
            "resolved": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz",
            "integrity": "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/deepmerge": {
            "version": "4.3.1",
            "resolved": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz",
            "integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==",
            "license": "MIT",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/define-data-property": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz",
            "integrity": "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "es-define-property": "^1.0.0",
                "es-errors": "^1.3.0",
                "gopd": "^1.0.1"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/define-properties": {
            "version": "1.2.1",
            "resolved": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz",
            "integrity": "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "define-data-property": "^1.0.1",
                "has-property-descriptors": "^1.0.0",
                "object-keys": "^1.1.1"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/delayed-stream": {
            "version": "1.0.0",
            "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz",
            "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==",
            "license": "MIT",
            "engines": {
                "node": ">=0.4.0"
            }
        },
        "node_modules/detect-libc": {
            "version": "1.0.3",
            "resolved": "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz",
            "integrity": "sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==",
            "license": "Apache-2.0",
            "bin": {
                "detect-libc": "bin/detect-libc.js"
            },
            "engines": {
                "node": ">=0.10"
            }
        },
        "node_modules/detect-node-es": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz",
            "integrity": "sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==",
            "license": "MIT"
        },
        "node_modules/doctrine": {
            "version": "2.1.0",
            "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz",
            "integrity": "sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==",
            "dev": true,
            "license": "Apache-2.0",
            "dependencies": {
                "esutils": "^2.0.2"
            },
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/dunder-proto": {
            "version": "1.0.1",
            "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz",
            "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==",
            "license": "MIT",
            "dependencies": {
                "call-bind-apply-helpers": "^1.0.1",
                "es-errors": "^1.3.0",
                "gopd": "^1.2.0"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/electron-to-chromium": {
            "version": "1.5.112",
            "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.112.tgz",
            "integrity": "sha512-oen93kVyqSb3l+ziUgzIOlWt/oOuy4zRmpwestMn4rhFWAoFJeFuCVte9F2fASjeZZo7l/Cif9TiyrdW4CwEMA==",
            "license": "ISC"
        },
        "node_modules/emoji-regex": {
            "version": "8.0.0",
            "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",
            "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
            "license": "MIT"
        },
        "node_modules/enhanced-resolve": {
            "version": "5.18.1",
            "resolved": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.1.tgz",
            "integrity": "sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg==",
            "license": "MIT",
            "dependencies": {
                "graceful-fs": "^4.2.4",
                "tapable": "^2.2.0"
            },
            "engines": {
                "node": ">=10.13.0"
            }
        },
        "node_modules/es-abstract": {
            "version": "1.23.9",
            "resolved": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.9.tgz",
            "integrity": "sha512-py07lI0wjxAC/DcfK1S6G7iANonniZwTISvdPzk9hzeH0IZIshbuuFxLIU96OyF89Yb9hiqWn8M/bY83KY5vzA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "array-buffer-byte-length": "^1.0.2",
                "arraybuffer.prototype.slice": "^1.0.4",
                "available-typed-arrays": "^1.0.7",
                "call-bind": "^1.0.8",
                "call-bound": "^1.0.3",
                "data-view-buffer": "^1.0.2",
                "data-view-byte-length": "^1.0.2",
                "data-view-byte-offset": "^1.0.1",
                "es-define-property": "^1.0.1",
                "es-errors": "^1.3.0",
                "es-object-atoms": "^1.0.0",
                "es-set-tostringtag": "^2.1.0",
                "es-to-primitive": "^1.3.0",
                "function.prototype.name": "^1.1.8",
                "get-intrinsic": "^1.2.7",
                "get-proto": "^1.0.0",
                "get-symbol-description": "^1.1.0",
                "globalthis": "^1.0.4",
                "gopd": "^1.2.0",
                "has-property-descriptors": "^1.0.2",
                "has-proto": "^1.2.0",
                "has-symbols": "^1.1.0",
                "hasown": "^2.0.2",
                "internal-slot": "^1.1.0",
                "is-array-buffer": "^3.0.5",
                "is-callable": "^1.2.7",
                "is-data-view": "^1.0.2",
                "is-regex": "^1.2.1",
                "is-shared-array-buffer": "^1.0.4",
                "is-string": "^1.1.1",
                "is-typed-array": "^1.1.15",
                "is-weakref": "^1.1.0",
                "math-intrinsics": "^1.1.0",
                "object-inspect": "^1.13.3",
                "object-keys": "^1.1.1",
                "object.assign": "^4.1.7",
                "own-keys": "^1.0.1",
                "regexp.prototype.flags": "^1.5.3",
                "safe-array-concat": "^1.1.3",
                "safe-push-apply": "^1.0.0",
                "safe-regex-test": "^1.1.0",
                "set-proto": "^1.0.0",
                "string.prototype.trim": "^1.2.10",
                "string.prototype.trimend": "^1.0.9",
                "string.prototype.trimstart": "^1.0.8",
                "typed-array-buffer": "^1.0.3",
                "typed-array-byte-length": "^1.0.3",
                "typed-array-byte-offset": "^1.0.4",
                "typed-array-length": "^1.0.7",
                "unbox-primitive": "^1.1.0",
                "which-typed-array": "^1.1.18"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/es-define-property": {
            "version": "1.0.1",
            "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz",
            "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==",
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/es-errors": {
            "version": "1.3.0",
            "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz",
            "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==",
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/es-iterator-helpers": {
            "version": "1.2.1",
            "resolved": "https://registry.npmjs.org/es-iterator-helpers/-/es-iterator-helpers-1.2.1.tgz",
            "integrity": "sha512-uDn+FE1yrDzyC0pCo961B2IHbdM8y/ACZsKD4dG6WqrjV53BADjwa7D+1aom2rsNVfLyDgU/eigvlJGJ08OQ4w==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "call-bound": "^1.0.3",
                "define-properties": "^1.2.1",
                "es-abstract": "^1.23.6",
                "es-errors": "^1.3.0",
                "es-set-tostringtag": "^2.0.3",
                "function-bind": "^1.1.2",
                "get-intrinsic": "^1.2.6",
                "globalthis": "^1.0.4",
                "gopd": "^1.2.0",
                "has-property-descriptors": "^1.0.2",
                "has-proto": "^1.2.0",
                "has-symbols": "^1.1.0",
                "internal-slot": "^1.1.0",
                "iterator.prototype": "^1.1.4",
                "safe-array-concat": "^1.1.3"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/es-object-atoms": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz",
            "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==",
            "license": "MIT",
            "dependencies": {
                "es-errors": "^1.3.0"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/es-set-tostringtag": {
            "version": "2.1.0",
            "resolved": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz",
            "integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==",
            "license": "MIT",
            "dependencies": {
                "es-errors": "^1.3.0",
                "get-intrinsic": "^1.2.6",
                "has-tostringtag": "^1.0.2",
                "hasown": "^2.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/es-shim-unscopables": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz",
            "integrity": "sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "hasown": "^2.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/es-to-primitive": {
            "version": "1.3.0",
            "resolved": "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.3.0.tgz",
            "integrity": "sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "is-callable": "^1.2.7",
                "is-date-object": "^1.0.5",
                "is-symbol": "^1.0.4"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/esbuild": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.25.0.tgz",
            "integrity": "sha512-BXq5mqc8ltbaN34cDqWuYKyNhX8D/Z0J1xdtdQ8UcIIIyJyz+ZMKUt58tF3SrZ85jcfN/PZYhjR5uDQAYNVbuw==",
            "hasInstallScript": true,
            "license": "MIT",
            "bin": {
                "esbuild": "bin/esbuild"
            },
            "engines": {
                "node": ">=18"
            },
            "optionalDependencies": {
                "@esbuild/aix-ppc64": "0.25.0",
                "@esbuild/android-arm": "0.25.0",
                "@esbuild/android-arm64": "0.25.0",
                "@esbuild/android-x64": "0.25.0",
                "@esbuild/darwin-arm64": "0.25.0",
                "@esbuild/darwin-x64": "0.25.0",
                "@esbuild/freebsd-arm64": "0.25.0",
                "@esbuild/freebsd-x64": "0.25.0",
                "@esbuild/linux-arm": "0.25.0",
                "@esbuild/linux-arm64": "0.25.0",
                "@esbuild/linux-ia32": "0.25.0",
                "@esbuild/linux-loong64": "0.25.0",
                "@esbuild/linux-mips64el": "0.25.0",
                "@esbuild/linux-ppc64": "0.25.0",
                "@esbuild/linux-riscv64": "0.25.0",
                "@esbuild/linux-s390x": "0.25.0",
                "@esbuild/linux-x64": "0.25.0",
                "@esbuild/netbsd-arm64": "0.25.0",
                "@esbuild/netbsd-x64": "0.25.0",
                "@esbuild/openbsd-arm64": "0.25.0",
                "@esbuild/openbsd-x64": "0.25.0",
                "@esbuild/sunos-x64": "0.25.0",
                "@esbuild/win32-arm64": "0.25.0",
                "@esbuild/win32-ia32": "0.25.0",
                "@esbuild/win32-x64": "0.25.0"
            }
        },
        "node_modules/escalade": {
            "version": "3.2.0",
            "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz",
            "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==",
            "license": "MIT",
            "engines": {
                "node": ">=6"
            }
        },
        "node_modules/escape-string-regexp": {
            "version": "4.0.0",
            "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz",
            "integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=10"
            },
            "funding": {
                "url": "https://github.com/sponsors/sindresorhus"
            }
        },
        "node_modules/eslint": {
            "version": "9.21.0",
            "resolved": "https://registry.npmjs.org/eslint/-/eslint-9.21.0.tgz",
            "integrity": "sha512-KjeihdFqTPhOMXTt7StsDxriV4n66ueuF/jfPNC3j/lduHwr/ijDwJMsF+wyMJethgiKi5wniIE243vi07d3pg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@eslint-community/eslint-utils": "^4.2.0",
                "@eslint-community/regexpp": "^4.12.1",
                "@eslint/config-array": "^0.19.2",
                "@eslint/core": "^0.12.0",
                "@eslint/eslintrc": "^3.3.0",
                "@eslint/js": "9.21.0",
                "@eslint/plugin-kit": "^0.2.7",
                "@humanfs/node": "^0.16.6",
                "@humanwhocodes/module-importer": "^1.0.1",
                "@humanwhocodes/retry": "^0.4.2",
                "@types/estree": "^1.0.6",
                "@types/json-schema": "^7.0.15",
                "ajv": "^6.12.4",
                "chalk": "^4.0.0",
                "cross-spawn": "^7.0.6",
                "debug": "^4.3.2",
                "escape-string-regexp": "^4.0.0",
                "eslint-scope": "^8.2.0",
                "eslint-visitor-keys": "^4.2.0",
                "espree": "^10.3.0",
                "esquery": "^1.5.0",
                "esutils": "^2.0.2",
                "fast-deep-equal": "^3.1.3",
                "file-entry-cache": "^8.0.0",
                "find-up": "^5.0.0",
                "glob-parent": "^6.0.2",
                "ignore": "^5.2.0",
                "imurmurhash": "^0.1.4",
                "is-glob": "^4.0.0",
                "json-stable-stringify-without-jsonify": "^1.0.1",
                "lodash.merge": "^4.6.2",
                "minimatch": "^3.1.2",
                "natural-compare": "^1.4.0",
                "optionator": "^0.9.3"
            },
            "bin": {
                "eslint": "bin/eslint.js"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "url": "https://eslint.org/donate"
            },
            "peerDependencies": {
                "jiti": "*"
            },
            "peerDependenciesMeta": {
                "jiti": {
                    "optional": true
                }
            }
        },
        "node_modules/eslint-config-prettier": {
            "version": "10.0.2",
            "resolved": "https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-10.0.2.tgz",
            "integrity": "sha512-1105/17ZIMjmCOJOPNfVdbXafLCLj3hPmkmB7dLgt7XsQ/zkxSuDerE/xgO3RxoHysR1N1whmquY0lSn2O0VLg==",
            "dev": true,
            "license": "MIT",
            "bin": {
                "eslint-config-prettier": "build/bin/cli.js"
            },
            "peerDependencies": {
                "eslint": ">=7.0.0"
            }
        },
        "node_modules/eslint-plugin-react": {
            "version": "7.37.4",
            "resolved": "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.37.4.tgz",
            "integrity": "sha512-BGP0jRmfYyvOyvMoRX/uoUeW+GqNj9y16bPQzqAHf3AYII/tDs+jMN0dBVkl88/OZwNGwrVFxE7riHsXVfy/LQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "array-includes": "^3.1.8",
                "array.prototype.findlast": "^1.2.5",
                "array.prototype.flatmap": "^1.3.3",
                "array.prototype.tosorted": "^1.1.4",
                "doctrine": "^2.1.0",
                "es-iterator-helpers": "^1.2.1",
                "estraverse": "^5.3.0",
                "hasown": "^2.0.2",
                "jsx-ast-utils": "^2.4.1 || ^3.0.0",
                "minimatch": "^3.1.2",
                "object.entries": "^1.1.8",
                "object.fromentries": "^2.0.8",
                "object.values": "^1.2.1",
                "prop-types": "^15.8.1",
                "resolve": "^2.0.0-next.5",
                "semver": "^6.3.1",
                "string.prototype.matchall": "^4.0.12",
                "string.prototype.repeat": "^1.0.0"
            },
            "engines": {
                "node": ">=4"
            },
            "peerDependencies": {
                "eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7"
            }
        },
        "node_modules/eslint-plugin-react-hooks": {
            "version": "5.2.0",
            "resolved": "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-5.2.0.tgz",
            "integrity": "sha512-+f15FfK64YQwZdJNELETdn5ibXEUQmW1DZL6KXhNnc2heoy/sg9VJJeT7n8TlMWouzWqSWavFkIhHyIbIAEapg==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=10"
            },
            "peerDependencies": {
                "eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0"
            }
        },
        "node_modules/eslint-scope": {
            "version": "8.2.0",
            "resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.2.0.tgz",
            "integrity": "sha512-PHlWUfG6lvPc3yvP5A4PNyBL1W8fkDUccmI21JUu/+GKZBoH/W5u6usENXUrWFRsyoW5ACUjFGgAFQp5gUlb/A==",
            "dev": true,
            "license": "BSD-2-Clause",
            "dependencies": {
                "esrecurse": "^4.3.0",
                "estraverse": "^5.2.0"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "url": "https://opencollective.com/eslint"
            }
        },
        "node_modules/eslint-visitor-keys": {
            "version": "4.2.0",
            "resolved": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-4.2.0.tgz",
            "integrity": "sha512-UyLnSehNt62FFhSwjZlHmeokpRK59rcz29j+F1/aDgbkbRTk7wIc9XzdoasMUbRNKDM0qQt/+BJ4BrpFeABemw==",
            "dev": true,
            "license": "Apache-2.0",
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "url": "https://opencollective.com/eslint"
            }
        },
        "node_modules/espree": {
            "version": "10.3.0",
            "resolved": "https://registry.npmjs.org/espree/-/espree-10.3.0.tgz",
            "integrity": "sha512-0QYC8b24HWY8zjRnDTL6RiHfDbAWn63qb4LMj1Z4b076A4une81+z03Kg7l7mn/48PUTqoLptSXez8oknU8Clg==",
            "dev": true,
            "license": "BSD-2-Clause",
            "dependencies": {
                "acorn": "^8.14.0",
                "acorn-jsx": "^5.3.2",
                "eslint-visitor-keys": "^4.2.0"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "url": "https://opencollective.com/eslint"
            }
        },
        "node_modules/esquery": {
            "version": "1.6.0",
            "resolved": "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz",
            "integrity": "sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==",
            "dev": true,
            "license": "BSD-3-Clause",
            "dependencies": {
                "estraverse": "^5.1.0"
            },
            "engines": {
                "node": ">=0.10"
            }
        },
        "node_modules/esrecurse": {
            "version": "4.3.0",
            "resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz",
            "integrity": "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==",
            "dev": true,
            "license": "BSD-2-Clause",
            "dependencies": {
                "estraverse": "^5.2.0"
            },
            "engines": {
                "node": ">=4.0"
            }
        },
        "node_modules/estraverse": {
            "version": "5.3.0",
            "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz",
            "integrity": "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==",
            "dev": true,
            "license": "BSD-2-Clause",
            "engines": {
                "node": ">=4.0"
            }
        },
        "node_modules/esutils": {
            "version": "2.0.3",
            "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz",
            "integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==",
            "dev": true,
            "license": "BSD-2-Clause",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/fast-deep-equal": {
            "version": "3.1.3",
            "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz",
            "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/fast-glob": {
            "version": "3.3.3",
            "resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz",
            "integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@nodelib/fs.stat": "^2.0.2",
                "@nodelib/fs.walk": "^1.2.3",
                "glob-parent": "^5.1.2",
                "merge2": "^1.3.0",
                "micromatch": "^4.0.8"
            },
            "engines": {
                "node": ">=8.6.0"
            }
        },
        "node_modules/fast-glob/node_modules/glob-parent": {
            "version": "5.1.2",
            "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz",
            "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "is-glob": "^4.0.1"
            },
            "engines": {
                "node": ">= 6"
            }
        },
        "node_modules/fast-json-stable-stringify": {
            "version": "2.1.0",
            "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz",
            "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/fast-levenshtein": {
            "version": "2.0.6",
            "resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz",
            "integrity": "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/fastq": {
            "version": "1.19.1",
            "resolved": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz",
            "integrity": "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "reusify": "^1.0.4"
            }
        },
        "node_modules/file-entry-cache": {
            "version": "8.0.0",
            "resolved": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-8.0.0.tgz",
            "integrity": "sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "flat-cache": "^4.0.0"
            },
            "engines": {
                "node": ">=16.0.0"
            }
        },
        "node_modules/fill-range": {
            "version": "7.1.1",
            "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz",
            "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "to-regex-range": "^5.0.1"
            },
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/find-up": {
            "version": "5.0.0",
            "resolved": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz",
            "integrity": "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "locate-path": "^6.0.0",
                "path-exists": "^4.0.0"
            },
            "engines": {
                "node": ">=10"
            },
            "funding": {
                "url": "https://github.com/sponsors/sindresorhus"
            }
        },
        "node_modules/flat-cache": {
            "version": "4.0.1",
            "resolved": "https://registry.npmjs.org/flat-cache/-/flat-cache-4.0.1.tgz",
            "integrity": "sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "flatted": "^3.2.9",
                "keyv": "^4.5.4"
            },
            "engines": {
                "node": ">=16"
            }
        },
        "node_modules/flatted": {
            "version": "3.3.3",
            "resolved": "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz",
            "integrity": "sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==",
            "dev": true,
            "license": "ISC"
        },
        "node_modules/follow-redirects": {
            "version": "1.15.9",
            "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz",
            "integrity": "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==",
            "funding": [
                {
                    "type": "individual",
                    "url": "https://github.com/sponsors/RubenVerborgh"
                }
            ],
            "license": "MIT",
            "engines": {
                "node": ">=4.0"
            },
            "peerDependenciesMeta": {
                "debug": {
                    "optional": true
                }
            }
        },
        "node_modules/for-each": {
            "version": "0.3.5",
            "resolved": "https://registry.npmjs.org/for-each/-/for-each-0.3.5.tgz",
            "integrity": "sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "is-callable": "^1.2.7"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/form-data": {
            "version": "4.0.2",
            "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz",
            "integrity": "sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==",
            "license": "MIT",
            "dependencies": {
                "asynckit": "^0.4.0",
                "combined-stream": "^1.0.8",
                "es-set-tostringtag": "^2.1.0",
                "mime-types": "^2.1.12"
            },
            "engines": {
                "node": ">= 6"
            }
        },
        "node_modules/framer-motion": {
            "version": "12.11.0",
            "resolved": "https://registry.npmjs.org/framer-motion/-/framer-motion-12.11.0.tgz",
            "integrity": "sha512-BaBPmkhaC2l0n619Kt1nQaxSdUdyyz5V1Z7EKJ1CcraOTZitgVx0RTbL8lmg2XesaFi6o8MPBIhkWDIvzDpGaQ==",
            "license": "MIT",
            "dependencies": {
                "motion-dom": "^12.11.0",
                "motion-utils": "^12.9.4",
                "tslib": "^2.4.0"
            },
            "peerDependencies": {
                "@emotion/is-prop-valid": "*",
                "react": "^18.0.0 || ^19.0.0",
                "react-dom": "^18.0.0 || ^19.0.0"
            },
            "peerDependenciesMeta": {
                "@emotion/is-prop-valid": {
                    "optional": true
                },
                "react": {
                    "optional": true
                },
                "react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/fsevents": {
            "version": "2.3.3",
            "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz",
            "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==",
            "hasInstallScript": true,
            "license": "MIT",
            "optional": true,
            "os": [
                "darwin"
            ],
            "engines": {
                "node": "^8.16.0 || ^10.6.0 || >=11.0.0"
            }
        },
        "node_modules/function-bind": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz",
            "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==",
            "license": "MIT",
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/function.prototype.name": {
            "version": "1.1.8",
            "resolved": "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.8.tgz",
            "integrity": "sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "call-bound": "^1.0.3",
                "define-properties": "^1.2.1",
                "functions-have-names": "^1.2.3",
                "hasown": "^2.0.2",
                "is-callable": "^1.2.7"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/functions-have-names": {
            "version": "1.2.3",
            "resolved": "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz",
            "integrity": "sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==",
            "dev": true,
            "license": "MIT",
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/gensync": {
            "version": "1.0.0-beta.2",
            "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz",
            "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==",
            "license": "MIT",
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/get-caller-file": {
            "version": "2.0.5",
            "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz",
            "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==",
            "license": "ISC",
            "engines": {
                "node": "6.* || 8.* || >= 10.*"
            }
        },
        "node_modules/get-intrinsic": {
            "version": "1.3.0",
            "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz",
            "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==",
            "license": "MIT",
            "dependencies": {
                "call-bind-apply-helpers": "^1.0.2",
                "es-define-property": "^1.0.1",
                "es-errors": "^1.3.0",
                "es-object-atoms": "^1.1.1",
                "function-bind": "^1.1.2",
                "get-proto": "^1.0.1",
                "gopd": "^1.2.0",
                "has-symbols": "^1.1.0",
                "hasown": "^2.0.2",
                "math-intrinsics": "^1.1.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/get-nonce": {
            "version": "1.0.1",
            "resolved": "https://registry.npmjs.org/get-nonce/-/get-nonce-1.0.1.tgz",
            "integrity": "sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==",
            "license": "MIT",
            "engines": {
                "node": ">=6"
            }
        },
        "node_modules/get-proto": {
            "version": "1.0.1",
            "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz",
            "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==",
            "license": "MIT",
            "dependencies": {
                "dunder-proto": "^1.0.1",
                "es-object-atoms": "^1.0.0"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/get-symbol-description": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.1.0.tgz",
            "integrity": "sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3",
                "es-errors": "^1.3.0",
                "get-intrinsic": "^1.2.6"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/glob-parent": {
            "version": "6.0.2",
            "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz",
            "integrity": "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "is-glob": "^4.0.3"
            },
            "engines": {
                "node": ">=10.13.0"
            }
        },
        "node_modules/globals": {
            "version": "15.15.0",
            "resolved": "https://registry.npmjs.org/globals/-/globals-15.15.0.tgz",
            "integrity": "sha512-7ACyT3wmyp3I61S4fG682L0VA2RGD9otkqGJIwNUMF1SWUombIIk+af1unuDYgMm082aHYwD+mzJvv9Iu8dsgg==",
            "license": "MIT",
            "engines": {
                "node": ">=18"
            },
            "funding": {
                "url": "https://github.com/sponsors/sindresorhus"
            }
        },
        "node_modules/globalthis": {
            "version": "1.0.4",
            "resolved": "https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz",
            "integrity": "sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "define-properties": "^1.2.1",
                "gopd": "^1.0.1"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/gopd": {
            "version": "1.2.0",
            "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz",
            "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==",
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/graceful-fs": {
            "version": "4.2.11",
            "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz",
            "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==",
            "license": "ISC"
        },
        "node_modules/graphemer": {
            "version": "1.4.0",
            "resolved": "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz",
            "integrity": "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/has-bigints": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/has-bigints/-/has-bigints-1.1.0.tgz",
            "integrity": "sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/has-flag": {
            "version": "4.0.0",
            "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz",
            "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==",
            "license": "MIT",
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/has-property-descriptors": {
            "version": "1.0.2",
            "resolved": "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz",
            "integrity": "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "es-define-property": "^1.0.0"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/has-proto": {
            "version": "1.2.0",
            "resolved": "https://registry.npmjs.org/has-proto/-/has-proto-1.2.0.tgz",
            "integrity": "sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "dunder-proto": "^1.0.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/has-symbols": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz",
            "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==",
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/has-tostringtag": {
            "version": "1.0.2",
            "resolved": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz",
            "integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==",
            "license": "MIT",
            "dependencies": {
                "has-symbols": "^1.0.3"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/hasown": {
            "version": "2.0.2",
            "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz",
            "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==",
            "license": "MIT",
            "dependencies": {
                "function-bind": "^1.1.2"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/hi-base32": {
            "version": "0.5.1",
            "resolved": "https://registry.npmjs.org/hi-base32/-/hi-base32-0.5.1.tgz",
            "integrity": "sha512-EmBBpvdYh/4XxsnUybsPag6VikPYnN30td+vQk+GI3qpahVEG9+gTkG0aXVxTjBqQ5T6ijbWIu77O+C5WFWsnA==",
            "license": "MIT"
        },
        "node_modules/ignore": {
            "version": "5.3.2",
            "resolved": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz",
            "integrity": "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 4"
            }
        },
        "node_modules/import-fresh": {
            "version": "3.3.1",
            "resolved": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz",
            "integrity": "sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "parent-module": "^1.0.0",
                "resolve-from": "^4.0.0"
            },
            "engines": {
                "node": ">=6"
            },
            "funding": {
                "url": "https://github.com/sponsors/sindresorhus"
            }
        },
        "node_modules/imurmurhash": {
            "version": "0.1.4",
            "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz",
            "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=0.8.19"
            }
        },
        "node_modules/internal-slot": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.1.0.tgz",
            "integrity": "sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "es-errors": "^1.3.0",
                "hasown": "^2.0.2",
                "side-channel": "^1.1.0"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/is-array-buffer": {
            "version": "3.0.5",
            "resolved": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.5.tgz",
            "integrity": "sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "call-bound": "^1.0.3",
                "get-intrinsic": "^1.2.6"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-async-function": {
            "version": "2.1.1",
            "resolved": "https://registry.npmjs.org/is-async-function/-/is-async-function-2.1.1.tgz",
            "integrity": "sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "async-function": "^1.0.0",
                "call-bound": "^1.0.3",
                "get-proto": "^1.0.1",
                "has-tostringtag": "^1.0.2",
                "safe-regex-test": "^1.1.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-bigint": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/is-bigint/-/is-bigint-1.1.0.tgz",
            "integrity": "sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "has-bigints": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-boolean-object": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.2.2.tgz",
            "integrity": "sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3",
                "has-tostringtag": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-callable": {
            "version": "1.2.7",
            "resolved": "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz",
            "integrity": "sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-core-module": {
            "version": "2.16.1",
            "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz",
            "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "hasown": "^2.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-data-view": {
            "version": "1.0.2",
            "resolved": "https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.2.tgz",
            "integrity": "sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.2",
                "get-intrinsic": "^1.2.6",
                "is-typed-array": "^1.1.13"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-date-object": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz",
            "integrity": "sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.2",
                "has-tostringtag": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-extglob": {
            "version": "2.1.1",
            "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz",
            "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/is-finalizationregistry": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz",
            "integrity": "sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-fullwidth-code-point": {
            "version": "3.0.0",
            "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz",
            "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==",
            "license": "MIT",
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/is-generator-function": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.1.0.tgz",
            "integrity": "sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3",
                "get-proto": "^1.0.0",
                "has-tostringtag": "^1.0.2",
                "safe-regex-test": "^1.1.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-glob": {
            "version": "4.0.3",
            "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz",
            "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "is-extglob": "^2.1.1"
            },
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/is-map": {
            "version": "2.0.3",
            "resolved": "https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz",
            "integrity": "sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-number": {
            "version": "7.0.0",
            "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz",
            "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=0.12.0"
            }
        },
        "node_modules/is-number-object": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.1.1.tgz",
            "integrity": "sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3",
                "has-tostringtag": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-regex": {
            "version": "1.2.1",
            "resolved": "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz",
            "integrity": "sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.2",
                "gopd": "^1.2.0",
                "has-tostringtag": "^1.0.2",
                "hasown": "^2.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-set": {
            "version": "2.0.3",
            "resolved": "https://registry.npmjs.org/is-set/-/is-set-2.0.3.tgz",
            "integrity": "sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-shared-array-buffer": {
            "version": "1.0.4",
            "resolved": "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz",
            "integrity": "sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-string": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/is-string/-/is-string-1.1.1.tgz",
            "integrity": "sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3",
                "has-tostringtag": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-symbol": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.1.tgz",
            "integrity": "sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.2",
                "has-symbols": "^1.1.0",
                "safe-regex-test": "^1.1.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-typed-array": {
            "version": "1.1.15",
            "resolved": "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz",
            "integrity": "sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "which-typed-array": "^1.1.16"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-weakmap": {
            "version": "2.0.2",
            "resolved": "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.2.tgz",
            "integrity": "sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-weakref": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/is-weakref/-/is-weakref-1.1.1.tgz",
            "integrity": "sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-weakset": {
            "version": "2.0.4",
            "resolved": "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.4.tgz",
            "integrity": "sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3",
                "get-intrinsic": "^1.2.6"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/isarray": {
            "version": "2.0.5",
            "resolved": "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz",
            "integrity": "sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/isexe": {
            "version": "2.0.0",
            "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz",
            "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==",
            "dev": true,
            "license": "ISC"
        },
        "node_modules/iterator.prototype": {
            "version": "1.1.5",
            "resolved": "https://registry.npmjs.org/iterator.prototype/-/iterator.prototype-1.1.5.tgz",
            "integrity": "sha512-H0dkQoCa3b2VEeKQBOxFph+JAbcrQdE7KC0UkqwpLmv2EC4P41QXP+rqo9wYodACiG5/WM5s9oDApTU8utwj9g==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "define-data-property": "^1.1.4",
                "es-object-atoms": "^1.0.0",
                "get-intrinsic": "^1.2.6",
                "get-proto": "^1.0.0",
                "has-symbols": "^1.1.0",
                "set-function-name": "^2.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/jiti": {
            "version": "2.4.2",
            "resolved": "https://registry.npmjs.org/jiti/-/jiti-2.4.2.tgz",
            "integrity": "sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==",
            "license": "MIT",
            "bin": {
                "jiti": "lib/jiti-cli.mjs"
            }
        },
        "node_modules/js-tokens": {
            "version": "4.0.0",
            "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz",
            "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==",
            "license": "MIT"
        },
        "node_modules/js-yaml": {
            "version": "4.1.0",
            "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz",
            "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "argparse": "^2.0.1"
            },
            "bin": {
                "js-yaml": "bin/js-yaml.js"
            }
        },
        "node_modules/jsesc": {
            "version": "3.1.0",
            "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz",
            "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==",
            "license": "MIT",
            "bin": {
                "jsesc": "bin/jsesc"
            },
            "engines": {
                "node": ">=6"
            }
        },
        "node_modules/json-buffer": {
            "version": "3.0.1",
            "resolved": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz",
            "integrity": "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/json-schema-traverse": {
            "version": "0.4.1",
            "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz",
            "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/json-stable-stringify-without-jsonify": {
            "version": "1.0.1",
            "resolved": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz",
            "integrity": "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/json5": {
            "version": "2.2.3",
            "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz",
            "integrity": "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==",
            "license": "MIT",
            "bin": {
                "json5": "lib/cli.js"
            },
            "engines": {
                "node": ">=6"
            }
        },
        "node_modules/jsx-ast-utils": {
            "version": "3.3.5",
            "resolved": "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz",
            "integrity": "sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "array-includes": "^3.1.6",
                "array.prototype.flat": "^1.3.1",
                "object.assign": "^4.1.4",
                "object.values": "^1.1.6"
            },
            "engines": {
                "node": ">=4.0"
            }
        },
        "node_modules/keyv": {
            "version": "4.5.4",
            "resolved": "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz",
            "integrity": "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "json-buffer": "3.0.1"
            }
        },
        "node_modules/laravel-vite-plugin": {
            "version": "1.2.0",
            "resolved": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.2.0.tgz",
            "integrity": "sha512-R0pJ+IcTVeqEMoKz/B2Ij57QVq3sFTABiFmb06gAwFdivbOgsUtuhX6N2MGLEArajrS3U5JbberzwOe7uXHMHQ==",
            "license": "MIT",
            "dependencies": {
                "picocolors": "^1.0.0",
                "vite-plugin-full-reload": "^1.1.0"
            },
            "bin": {
                "clean-orphaned-assets": "bin/clean.js"
            },
            "engines": {
                "node": "^18.0.0 || ^20.0.0 || >=22.0.0"
            },
            "peerDependencies": {
                "vite": "^5.0.0 || ^6.0.0"
            }
        },
        "node_modules/levn": {
            "version": "0.4.1",
            "resolved": "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz",
            "integrity": "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "prelude-ls": "^1.2.1",
                "type-check": "~0.4.0"
            },
            "engines": {
                "node": ">= 0.8.0"
            }
        },
        "node_modules/lightningcss": {
            "version": "1.29.1",
            "resolved": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.29.1.tgz",
            "integrity": "sha512-FmGoeD4S05ewj+AkhTY+D+myDvXI6eL27FjHIjoyUkO/uw7WZD1fBVs0QxeYWa7E17CUHJaYX/RUGISCtcrG4Q==",
            "license": "MPL-2.0",
            "dependencies": {
                "detect-libc": "^1.0.3"
            },
            "engines": {
                "node": ">= 12.0.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/parcel"
            },
            "optionalDependencies": {
                "lightningcss-darwin-arm64": "1.29.1",
                "lightningcss-darwin-x64": "1.29.1",
                "lightningcss-freebsd-x64": "1.29.1",
                "lightningcss-linux-arm-gnueabihf": "1.29.1",
                "lightningcss-linux-arm64-gnu": "1.29.1",
                "lightningcss-linux-arm64-musl": "1.29.1",
                "lightningcss-linux-x64-gnu": "1.29.1",
                "lightningcss-linux-x64-musl": "1.29.1",
                "lightningcss-win32-arm64-msvc": "1.29.1",
                "lightningcss-win32-x64-msvc": "1.29.1"
            }
        },
        "node_modules/lightningcss-darwin-arm64": {
            "version": "1.29.1",
            "resolved": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.29.1.tgz",
            "integrity": "sha512-HtR5XJ5A0lvCqYAoSv2QdZZyoHNttBpa5EP9aNuzBQeKGfbyH5+UipLWvVzpP4Uml5ej4BYs5I9Lco9u1fECqw==",
            "cpu": [
                "arm64"
            ],
            "license": "MPL-2.0",
            "optional": true,
            "os": [
                "darwin"
            ],
            "engines": {
                "node": ">= 12.0.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/parcel"
            }
        },
        "node_modules/lightningcss-darwin-x64": {
            "version": "1.29.1",
            "resolved": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.29.1.tgz",
            "integrity": "sha512-k33G9IzKUpHy/J/3+9MCO4e+PzaFblsgBjSGlpAaFikeBFm8B/CkO3cKU9oI4g+fjS2KlkLM/Bza9K/aw8wsNA==",
            "cpu": [
                "x64"
            ],
            "license": "MPL-2.0",
            "optional": true,
            "os": [
                "darwin"
            ],
            "engines": {
                "node": ">= 12.0.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/parcel"
            }
        },
        "node_modules/lightningcss-freebsd-x64": {
            "version": "1.29.1",
            "resolved": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.29.1.tgz",
            "integrity": "sha512-0SUW22fv/8kln2LnIdOCmSuXnxgxVC276W5KLTwoehiO0hxkacBxjHOL5EtHD8BAXg2BvuhsJPmVMasvby3LiQ==",
            "cpu": [
                "x64"
            ],
            "license": "MPL-2.0",
            "optional": true,
            "os": [
                "freebsd"
            ],
            "engines": {
                "node": ">= 12.0.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/parcel"
            }
        },
        "node_modules/lightningcss-linux-arm-gnueabihf": {
            "version": "1.29.1",
            "resolved": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.29.1.tgz",
            "integrity": "sha512-sD32pFvlR0kDlqsOZmYqH/68SqUMPNj+0pucGxToXZi4XZgZmqeX/NkxNKCPsswAXU3UeYgDSpGhu05eAufjDg==",
            "cpu": [
                "arm"
            ],
            "license": "MPL-2.0",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 12.0.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/parcel"
            }
        },
        "node_modules/lightningcss-linux-arm64-gnu": {
            "version": "1.29.1",
            "resolved": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.29.1.tgz",
            "integrity": "sha512-0+vClRIZ6mmJl/dxGuRsE197o1HDEeeRk6nzycSy2GofC2JsY4ifCRnvUWf/CUBQmlrvMzt6SMQNMSEu22csWQ==",
            "cpu": [
                "arm64"
            ],
            "license": "MPL-2.0",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 12.0.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/parcel"
            }
        },
        "node_modules/lightningcss-linux-arm64-musl": {
            "version": "1.29.1",
            "resolved": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.29.1.tgz",
            "integrity": "sha512-UKMFrG4rL/uHNgelBsDwJcBqVpzNJbzsKkbI3Ja5fg00sgQnHw/VrzUTEc4jhZ+AN2BvQYz/tkHu4vt1kLuJyw==",
            "cpu": [
                "arm64"
            ],
            "license": "MPL-2.0",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 12.0.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/parcel"
            }
        },
        "node_modules/lightningcss-linux-x64-gnu": {
            "version": "1.29.1",
            "resolved": "https://registry.npmjs.org/lightningcss-linux-x64-gnu/-/lightningcss-linux-x64-gnu-1.29.1.tgz",
            "integrity": "sha512-u1S+xdODy/eEtjADqirA774y3jLcm8RPtYztwReEXoZKdzgsHYPl0s5V52Tst+GKzqjebkULT86XMSxejzfISw==",
            "cpu": [
                "x64"
            ],
            "license": "MPL-2.0",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 12.0.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/parcel"
            }
        },
        "node_modules/lightningcss-linux-x64-musl": {
            "version": "1.29.1",
            "resolved": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.29.1.tgz",
            "integrity": "sha512-L0Tx0DtaNUTzXv0lbGCLB/c/qEADanHbu4QdcNOXLIe1i8i22rZRpbT3gpWYsCh9aSL9zFujY/WmEXIatWvXbw==",
            "cpu": [
                "x64"
            ],
            "license": "MPL-2.0",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 12.0.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/parcel"
            }
        },
        "node_modules/lightningcss-win32-arm64-msvc": {
            "version": "1.29.1",
            "resolved": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.29.1.tgz",
            "integrity": "sha512-QoOVnkIEFfbW4xPi+dpdft/zAKmgLgsRHfJalEPYuJDOWf7cLQzYg0DEh8/sn737FaeMJxHZRc1oBreiwZCjog==",
            "cpu": [
                "arm64"
            ],
            "license": "MPL-2.0",
            "optional": true,
            "os": [
                "win32"
            ],
            "engines": {
                "node": ">= 12.0.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/parcel"
            }
        },
        "node_modules/lightningcss-win32-x64-msvc": {
            "version": "1.29.1",
            "resolved": "https://registry.npmjs.org/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.29.1.tgz",
            "integrity": "sha512-NygcbThNBe4JElP+olyTI/doBNGJvLs3bFCRPdvuCcxZCcCZ71B858IHpdm7L1btZex0FvCmM17FK98Y9MRy1Q==",
            "cpu": [
                "x64"
            ],
            "license": "MPL-2.0",
            "optional": true,
            "os": [
                "win32"
            ],
            "engines": {
                "node": ">= 12.0.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/parcel"
            }
        },
        "node_modules/locate-path": {
            "version": "6.0.0",
            "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz",
            "integrity": "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "p-locate": "^5.0.0"
            },
            "engines": {
                "node": ">=10"
            },
            "funding": {
                "url": "https://github.com/sponsors/sindresorhus"
            }
        },
        "node_modules/lodash": {
            "version": "4.17.21",
            "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz",
            "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==",
            "license": "MIT"
        },
        "node_modules/lodash.isequal": {
            "version": "4.5.0",
            "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz",
            "integrity": "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==",
            "deprecated": "This package is deprecated. Use require('node:util').isDeepStrictEqual instead.",
            "license": "MIT"
        },
        "node_modules/lodash.merge": {
            "version": "4.6.2",
            "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz",
            "integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/loose-envify": {
            "version": "1.4.0",
            "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz",
            "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "js-tokens": "^3.0.0 || ^4.0.0"
            },
            "bin": {
                "loose-envify": "cli.js"
            }
        },
        "node_modules/lru-cache": {
            "version": "5.1.1",
            "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz",
            "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==",
            "license": "ISC",
            "dependencies": {
                "yallist": "^3.0.2"
            }
        },
        "node_modules/lucide-react": {
            "version": "0.475.0",
            "resolved": "https://registry.npmjs.org/lucide-react/-/lucide-react-0.475.0.tgz",
            "integrity": "sha512-NJzvVu1HwFVeZ+Gwq2q00KygM1aBhy/ZrhY9FsAgJtpB+E4R7uxRk9M2iKvHa6/vNxZydIB59htha4c2vvwvVg==",
            "license": "ISC",
            "peerDependencies": {
                "react": "^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0"
            }
        },
        "node_modules/math-intrinsics": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz",
            "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==",
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/merge2": {
            "version": "1.4.1",
            "resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz",
            "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 8"
            }
        },
        "node_modules/micromatch": {
            "version": "4.0.8",
            "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz",
            "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "braces": "^3.0.3",
                "picomatch": "^2.3.1"
            },
            "engines": {
                "node": ">=8.6"
            }
        },
        "node_modules/mime-db": {
            "version": "1.52.0",
            "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz",
            "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==",
            "license": "MIT",
            "engines": {
                "node": ">= 0.6"
            }
        },
        "node_modules/mime-types": {
            "version": "2.1.35",
            "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz",
            "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==",
            "license": "MIT",
            "dependencies": {
                "mime-db": "1.52.0"
            },
            "engines": {
                "node": ">= 0.6"
            }
        },
        "node_modules/minimatch": {
            "version": "3.1.2",
            "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz",
            "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "brace-expansion": "^1.1.7"
            },
            "engines": {
                "node": "*"
            }
        },
        "node_modules/motion-dom": {
            "version": "12.11.0",
            "resolved": "https://registry.npmjs.org/motion-dom/-/motion-dom-12.11.0.tgz",
            "integrity": "sha512-CItkGYJenn5ZsbzTX0D9mE0UWdjdd9r535FrxEXhzR8Kwa9I2dLr1uhEJgQPWbgaIJ6i0sNFnf2T9NvVDWQVBw==",
            "license": "MIT",
            "dependencies": {
                "motion-utils": "^12.9.4"
            }
        },
        "node_modules/motion-utils": {
            "version": "12.9.4",
            "resolved": "https://registry.npmjs.org/motion-utils/-/motion-utils-12.9.4.tgz",
            "integrity": "sha512-BW3I65zeM76CMsfh3kHid9ansEJk9Qvl+K5cu4DVHKGsI52n76OJ4z2CUJUV+Mn3uEP9k1JJA3tClG0ggSrRcg==",
            "license": "MIT"
        },
        "node_modules/ms": {
            "version": "2.1.3",
            "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz",
            "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==",
            "license": "MIT"
        },
        "node_modules/nanoid": {
            "version": "3.3.8",
            "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.8.tgz",
            "integrity": "sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==",
            "funding": [
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "bin": {
                "nanoid": "bin/nanoid.cjs"
            },
            "engines": {
                "node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"
            }
        },
        "node_modules/natural-compare": {
            "version": "1.4.0",
            "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz",
            "integrity": "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/node-releases": {
            "version": "2.0.19",
            "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz",
            "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==",
            "license": "MIT"
        },
        "node_modules/object-assign": {
            "version": "4.1.1",
            "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz",
            "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/object-inspect": {
            "version": "1.13.4",
            "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz",
            "integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==",
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/object-keys": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz",
            "integrity": "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/object.assign": {
            "version": "4.1.7",
            "resolved": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz",
            "integrity": "sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "call-bound": "^1.0.3",
                "define-properties": "^1.2.1",
                "es-object-atoms": "^1.0.0",
                "has-symbols": "^1.1.0",
                "object-keys": "^1.1.1"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/object.entries": {
            "version": "1.1.8",
            "resolved": "https://registry.npmjs.org/object.entries/-/object.entries-1.1.8.tgz",
            "integrity": "sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.7",
                "define-properties": "^1.2.1",
                "es-object-atoms": "^1.0.0"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/object.fromentries": {
            "version": "2.0.8",
            "resolved": "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.8.tgz",
            "integrity": "sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.7",
                "define-properties": "^1.2.1",
                "es-abstract": "^1.23.2",
                "es-object-atoms": "^1.0.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/object.values": {
            "version": "1.2.1",
            "resolved": "https://registry.npmjs.org/object.values/-/object.values-1.2.1.tgz",
            "integrity": "sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "call-bound": "^1.0.3",
                "define-properties": "^1.2.1",
                "es-object-atoms": "^1.0.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/optionator": {
            "version": "0.9.4",
            "resolved": "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz",
            "integrity": "sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "deep-is": "^0.1.3",
                "fast-levenshtein": "^2.0.6",
                "levn": "^0.4.1",
                "prelude-ls": "^1.2.1",
                "type-check": "^0.4.0",
                "word-wrap": "^1.2.5"
            },
            "engines": {
                "node": ">= 0.8.0"
            }
        },
        "node_modules/own-keys": {
            "version": "1.0.1",
            "resolved": "https://registry.npmjs.org/own-keys/-/own-keys-1.0.1.tgz",
            "integrity": "sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "get-intrinsic": "^1.2.6",
                "object-keys": "^1.1.1",
                "safe-push-apply": "^1.0.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/p-limit": {
            "version": "3.1.0",
            "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz",
            "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "yocto-queue": "^0.1.0"
            },
            "engines": {
                "node": ">=10"
            },
            "funding": {
                "url": "https://github.com/sponsors/sindresorhus"
            }
        },
        "node_modules/p-locate": {
            "version": "5.0.0",
            "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz",
            "integrity": "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "p-limit": "^3.0.2"
            },
            "engines": {
                "node": ">=10"
            },
            "funding": {
                "url": "https://github.com/sponsors/sindresorhus"
            }
        },
        "node_modules/parent-module": {
            "version": "1.0.1",
            "resolved": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz",
            "integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "callsites": "^3.0.0"
            },
            "engines": {
                "node": ">=6"
            }
        },
        "node_modules/path-exists": {
            "version": "4.0.0",
            "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz",
            "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/path-key": {
            "version": "3.1.1",
            "resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz",
            "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/path-parse": {
            "version": "1.0.7",
            "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz",
            "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/picocolors": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz",
            "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==",
            "license": "ISC"
        },
        "node_modules/picomatch": {
            "version": "2.3.1",
            "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz",
            "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==",
            "license": "MIT",
            "engines": {
                "node": ">=8.6"
            },
            "funding": {
                "url": "https://github.com/sponsors/jonschlinkert"
            }
        },
        "node_modules/possible-typed-array-names": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz",
            "integrity": "sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/postcss": {
            "version": "8.5.3",
            "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.3.tgz",
            "integrity": "sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==",
            "funding": [
                {
                    "type": "opencollective",
                    "url": "https://opencollective.com/postcss/"
                },
                {
                    "type": "tidelift",
                    "url": "https://tidelift.com/funding/github/npm/postcss"
                },
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "dependencies": {
                "nanoid": "^3.3.8",
                "picocolors": "^1.1.1",
                "source-map-js": "^1.2.1"
            },
            "engines": {
                "node": "^10 || ^12 || >=14"
            }
        },
        "node_modules/prelude-ls": {
            "version": "1.2.1",
            "resolved": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz",
            "integrity": "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 0.8.0"
            }
        },
        "node_modules/prettier": {
            "version": "3.5.3",
            "resolved": "https://registry.npmjs.org/prettier/-/prettier-3.5.3.tgz",
            "integrity": "sha512-QQtaxnoDJeAkDvDKWCLiwIXkTgRhwYDEQCghU9Z6q03iyek/rxRh/2lC3HB7P8sWT2xC/y5JDctPLBIGzHKbhw==",
            "dev": true,
            "license": "MIT",
            "bin": {
                "prettier": "bin/prettier.cjs"
            },
            "engines": {
                "node": ">=14"
            },
            "funding": {
                "url": "https://github.com/prettier/prettier?sponsor=1"
            }
        },
        "node_modules/prettier-plugin-organize-imports": {
            "version": "4.1.0",
            "resolved": "https://registry.npmjs.org/prettier-plugin-organize-imports/-/prettier-plugin-organize-imports-4.1.0.tgz",
            "integrity": "sha512-5aWRdCgv645xaa58X8lOxzZoiHAldAPChljr/MT0crXVOWTZ+Svl4hIWlz+niYSlO6ikE5UXkN1JrRvIP2ut0A==",
            "dev": true,
            "license": "MIT",
            "peerDependencies": {
                "prettier": ">=2.0",
                "typescript": ">=2.9",
                "vue-tsc": "^2.1.0"
            },
            "peerDependenciesMeta": {
                "vue-tsc": {
                    "optional": true
                }
            }
        },
        "node_modules/prettier-plugin-tailwindcss": {
            "version": "0.6.11",
            "resolved": "https://registry.npmjs.org/prettier-plugin-tailwindcss/-/prettier-plugin-tailwindcss-0.6.11.tgz",
            "integrity": "sha512-YxaYSIvZPAqhrrEpRtonnrXdghZg1irNg4qrjboCXrpybLWVs55cW2N3juhspVJiO0JBvYJT8SYsJpc8OQSnsA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=14.21.3"
            },
            "peerDependencies": {
                "@ianvs/prettier-plugin-sort-imports": "*",
                "@prettier/plugin-pug": "*",
                "@shopify/prettier-plugin-liquid": "*",
                "@trivago/prettier-plugin-sort-imports": "*",
                "@zackad/prettier-plugin-twig": "*",
                "prettier": "^3.0",
                "prettier-plugin-astro": "*",
                "prettier-plugin-css-order": "*",
                "prettier-plugin-import-sort": "*",
                "prettier-plugin-jsdoc": "*",
                "prettier-plugin-marko": "*",
                "prettier-plugin-multiline-arrays": "*",
                "prettier-plugin-organize-attributes": "*",
                "prettier-plugin-organize-imports": "*",
                "prettier-plugin-sort-imports": "*",
                "prettier-plugin-style-order": "*",
                "prettier-plugin-svelte": "*"
            },
            "peerDependenciesMeta": {
                "@ianvs/prettier-plugin-sort-imports": {
                    "optional": true
                },
                "@prettier/plugin-pug": {
                    "optional": true
                },
                "@shopify/prettier-plugin-liquid": {
                    "optional": true
                },
                "@trivago/prettier-plugin-sort-imports": {
                    "optional": true
                },
                "@zackad/prettier-plugin-twig": {
                    "optional": true
                },
                "prettier-plugin-astro": {
                    "optional": true
                },
                "prettier-plugin-css-order": {
                    "optional": true
                },
                "prettier-plugin-import-sort": {
                    "optional": true
                },
                "prettier-plugin-jsdoc": {
                    "optional": true
                },
                "prettier-plugin-marko": {
                    "optional": true
                },
                "prettier-plugin-multiline-arrays": {
                    "optional": true
                },
                "prettier-plugin-organize-attributes": {
                    "optional": true
                },
                "prettier-plugin-organize-imports": {
                    "optional": true
                },
                "prettier-plugin-sort-imports": {
                    "optional": true
                },
                "prettier-plugin-style-order": {
                    "optional": true
                },
                "prettier-plugin-svelte": {
                    "optional": true
                }
            }
        },
        "node_modules/promise-polyfill": {
            "version": "8.3.0",
            "resolved": "https://registry.npmjs.org/promise-polyfill/-/promise-polyfill-8.3.0.tgz",
            "integrity": "sha512-H5oELycFml5yto/atYqmjyigJoAo3+OXwolYiH7OfQuYlAqhxNvTfiNMbV9hsC6Yp83yE5r2KTVmtrG6R9i6Pg==",
            "license": "MIT"
        },
        "node_modules/prop-types": {
            "version": "15.8.1",
            "resolved": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz",
            "integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "loose-envify": "^1.4.0",
                "object-assign": "^4.1.1",
                "react-is": "^16.13.1"
            }
        },
        "node_modules/proxy-from-env": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz",
            "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==",
            "license": "MIT"
        },
        "node_modules/punycode": {
            "version": "2.3.1",
            "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz",
            "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=6"
            }
        },
        "node_modules/qs": {
            "version": "6.14.0",
            "resolved": "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz",
            "integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==",
            "license": "BSD-3-Clause",
            "dependencies": {
                "side-channel": "^1.1.0"
            },
            "engines": {
                "node": ">=0.6"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/queue-microtask": {
            "version": "1.2.3",
            "resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz",
            "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==",
            "dev": true,
            "funding": [
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/feross"
                },
                {
                    "type": "patreon",
                    "url": "https://www.patreon.com/feross"
                },
                {
                    "type": "consulting",
                    "url": "https://feross.org/support"
                }
            ],
            "license": "MIT"
        },
        "node_modules/react": {
            "version": "19.0.0",
            "resolved": "https://registry.npmjs.org/react/-/react-19.0.0.tgz",
            "integrity": "sha512-V8AVnmPIICiWpGfm6GLzCR/W5FXLchHop40W4nXBmdlEceh16rCN8O8LNWm5bh5XUX91fh7KpA+W0TgMKmgTpQ==",
            "license": "MIT",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/react-dom": {
            "version": "19.0.0",
            "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-19.0.0.tgz",
            "integrity": "sha512-4GV5sHFG0e/0AD4X+ySy6UJd3jVl1iNsNHdpad0qhABJ11twS3TTBnseqsKurKcsNqCEFeGL3uLpVChpIO3QfQ==",
            "license": "MIT",
            "dependencies": {
                "scheduler": "^0.25.0"
            },
            "peerDependencies": {
                "react": "^19.0.0"
            }
        },
        "node_modules/react-is": {
            "version": "16.13.1",
            "resolved": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz",
            "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/react-refresh": {
            "version": "0.14.2",
            "resolved": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.2.tgz",
            "integrity": "sha512-jCvmsr+1IUSMUyzOkRcvnVbX3ZYC6g9TDrDbFuFmRDq7PD4yaGbLKNQL6k2jnArV8hjYxh7hVhAZB6s9HDGpZA==",
            "license": "MIT",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/react-remove-scroll": {
            "version": "2.6.3",
            "resolved": "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.6.3.tgz",
            "integrity": "sha512-pnAi91oOk8g8ABQKGF5/M9qxmmOPxaAnopyTHYfqYEwJhyFrbbBtHuSgtKEoH0jpcxx5o3hXqH1mNd9/Oi+8iQ==",
            "license": "MIT",
            "dependencies": {
                "react-remove-scroll-bar": "^2.3.7",
                "react-style-singleton": "^2.2.3",
                "tslib": "^2.1.0",
                "use-callback-ref": "^1.3.3",
                "use-sidecar": "^1.1.3"
            },
            "engines": {
                "node": ">=10"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/react-remove-scroll-bar": {
            "version": "2.3.8",
            "resolved": "https://registry.npmjs.org/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz",
            "integrity": "sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==",
            "license": "MIT",
            "dependencies": {
                "react-style-singleton": "^2.2.2",
                "tslib": "^2.0.0"
            },
            "engines": {
                "node": ">=10"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/react-style-singleton": {
            "version": "2.2.3",
            "resolved": "https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.3.tgz",
            "integrity": "sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==",
            "license": "MIT",
            "dependencies": {
                "get-nonce": "^1.0.0",
                "tslib": "^2.0.0"
            },
            "engines": {
                "node": ">=10"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/reflect.getprototypeof": {
            "version": "1.0.10",
            "resolved": "https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz",
            "integrity": "sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "define-properties": "^1.2.1",
                "es-abstract": "^1.23.9",
                "es-errors": "^1.3.0",
                "es-object-atoms": "^1.0.0",
                "get-intrinsic": "^1.2.7",
                "get-proto": "^1.0.1",
                "which-builtin-type": "^1.2.1"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/regexp.prototype.flags": {
            "version": "1.5.4",
            "resolved": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz",
            "integrity": "sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "define-properties": "^1.2.1",
                "es-errors": "^1.3.0",
                "get-proto": "^1.0.1",
                "gopd": "^1.2.0",
                "set-function-name": "^2.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/require-directory": {
            "version": "2.1.1",
            "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz",
            "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==",
            "license": "MIT",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/resolve": {
            "version": "2.0.0-next.5",
            "resolved": "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.5.tgz",
            "integrity": "sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "is-core-module": "^2.13.0",
                "path-parse": "^1.0.7",
                "supports-preserve-symlinks-flag": "^1.0.0"
            },
            "bin": {
                "resolve": "bin/resolve"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/resolve-from": {
            "version": "4.0.0",
            "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz",
            "integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=4"
            }
        },
        "node_modules/reusify": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz",
            "integrity": "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "iojs": ">=1.0.0",
                "node": ">=0.10.0"
            }
        },
        "node_modules/rollup": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/rollup/-/rollup-4.34.9.tgz",
            "integrity": "sha512-nF5XYqWWp9hx/LrpC8sZvvvmq0TeTjQgaZHYmAgwysT9nh8sWnZhBnM8ZyVbbJFIQBLwHDNoMqsBZBbUo4U8sQ==",
            "license": "MIT",
            "dependencies": {
                "@types/estree": "1.0.6"
            },
            "bin": {
                "rollup": "dist/bin/rollup"
            },
            "engines": {
                "node": ">=18.0.0",
                "npm": ">=8.0.0"
            },
            "optionalDependencies": {
                "@rollup/rollup-android-arm-eabi": "4.34.9",
                "@rollup/rollup-android-arm64": "4.34.9",
                "@rollup/rollup-darwin-arm64": "4.34.9",
                "@rollup/rollup-darwin-x64": "4.34.9",
                "@rollup/rollup-freebsd-arm64": "4.34.9",
                "@rollup/rollup-freebsd-x64": "4.34.9",
                "@rollup/rollup-linux-arm-gnueabihf": "4.34.9",
                "@rollup/rollup-linux-arm-musleabihf": "4.34.9",
                "@rollup/rollup-linux-arm64-gnu": "4.34.9",
                "@rollup/rollup-linux-arm64-musl": "4.34.9",
                "@rollup/rollup-linux-loongarch64-gnu": "4.34.9",
                "@rollup/rollup-linux-powerpc64le-gnu": "4.34.9",
                "@rollup/rollup-linux-riscv64-gnu": "4.34.9",
                "@rollup/rollup-linux-s390x-gnu": "4.34.9",
                "@rollup/rollup-linux-x64-gnu": "4.34.9",
                "@rollup/rollup-linux-x64-musl": "4.34.9",
                "@rollup/rollup-win32-arm64-msvc": "4.34.9",
                "@rollup/rollup-win32-ia32-msvc": "4.34.9",
                "@rollup/rollup-win32-x64-msvc": "4.34.9",
                "fsevents": "~2.3.2"
            }
        },
        "node_modules/rollup/node_modules/@rollup/rollup-linux-x64-gnu": {
            "version": "4.34.9",
            "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.9.tgz",
            "integrity": "sha512-FwBHNSOjUTQLP4MG7y6rR6qbGw4MFeQnIBrMe161QGaQoBQLqSUEKlHIiVgF3g/mb3lxlxzJOpIBhaP+C+KP2A==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ]
        },
        "node_modules/run-parallel": {
            "version": "1.2.0",
            "resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz",
            "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==",
            "dev": true,
            "funding": [
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/feross"
                },
                {
                    "type": "patreon",
                    "url": "https://www.patreon.com/feross"
                },
                {
                    "type": "consulting",
                    "url": "https://feross.org/support"
                }
            ],
            "license": "MIT",
            "dependencies": {
                "queue-microtask": "^1.2.2"
            }
        },
        "node_modules/rxjs": {
            "version": "7.8.2",
            "resolved": "https://registry.npmjs.org/rxjs/-/rxjs-7.8.2.tgz",
            "integrity": "sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==",
            "license": "Apache-2.0",
            "dependencies": {
                "tslib": "^2.1.0"
            }
        },
        "node_modules/safe-array-concat": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.3.tgz",
            "integrity": "sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "call-bound": "^1.0.2",
                "get-intrinsic": "^1.2.6",
                "has-symbols": "^1.1.0",
                "isarray": "^2.0.5"
            },
            "engines": {
                "node": ">=0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/safe-push-apply": {
            "version": "1.0.0",
            "resolved": "https://registry.npmjs.org/safe-push-apply/-/safe-push-apply-1.0.0.tgz",
            "integrity": "sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "es-errors": "^1.3.0",
                "isarray": "^2.0.5"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/safe-regex-test": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.1.0.tgz",
            "integrity": "sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.2",
                "es-errors": "^1.3.0",
                "is-regex": "^1.2.1"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/scheduler": {
            "version": "0.25.0",
            "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.25.0.tgz",
            "integrity": "sha512-xFVuu11jh+xcO7JOAGJNOXld8/TcEHK/4CituBUeUb5hqxJLj9YuemAEuvm9gQ/+pgXYfbQuqAkiYu+u7YEsNA==",
            "license": "MIT"
        },
        "node_modules/semver": {
            "version": "6.3.1",
            "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz",
            "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==",
            "license": "ISC",
            "bin": {
                "semver": "bin/semver.js"
            }
        },
        "node_modules/set-function-length": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz",
            "integrity": "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "define-data-property": "^1.1.4",
                "es-errors": "^1.3.0",
                "function-bind": "^1.1.2",
                "get-intrinsic": "^1.2.4",
                "gopd": "^1.0.1",
                "has-property-descriptors": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/set-function-name": {
            "version": "2.0.2",
            "resolved": "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz",
            "integrity": "sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "define-data-property": "^1.1.4",
                "es-errors": "^1.3.0",
                "functions-have-names": "^1.2.3",
                "has-property-descriptors": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/set-proto": {
            "version": "1.0.0",
            "resolved": "https://registry.npmjs.org/set-proto/-/set-proto-1.0.0.tgz",
            "integrity": "sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "dunder-proto": "^1.0.1",
                "es-errors": "^1.3.0",
                "es-object-atoms": "^1.0.0"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/shebang-command": {
            "version": "2.0.0",
            "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz",
            "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "shebang-regex": "^3.0.0"
            },
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/shebang-regex": {
            "version": "3.0.0",
            "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz",
            "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/shell-quote": {
            "version": "1.8.2",
            "resolved": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.2.tgz",
            "integrity": "sha512-AzqKpGKjrj7EM6rKVQEPpB288oCfnrEIuyoT9cyF4nmGa7V8Zk6f7RRqYisX8X9m+Q7bd632aZW4ky7EhbQztA==",
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/side-channel": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz",
            "integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==",
            "license": "MIT",
            "dependencies": {
                "es-errors": "^1.3.0",
                "object-inspect": "^1.13.3",
                "side-channel-list": "^1.0.0",
                "side-channel-map": "^1.0.1",
                "side-channel-weakmap": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/side-channel-list": {
            "version": "1.0.0",
            "resolved": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz",
            "integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==",
            "license": "MIT",
            "dependencies": {
                "es-errors": "^1.3.0",
                "object-inspect": "^1.13.3"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/side-channel-map": {
            "version": "1.0.1",
            "resolved": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz",
            "integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==",
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.2",
                "es-errors": "^1.3.0",
                "get-intrinsic": "^1.2.5",
                "object-inspect": "^1.13.3"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/side-channel-weakmap": {
            "version": "1.0.2",
            "resolved": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz",
            "integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==",
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.2",
                "es-errors": "^1.3.0",
                "get-intrinsic": "^1.2.5",
                "object-inspect": "^1.13.3",
                "side-channel-map": "^1.0.1"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/source-map-js": {
            "version": "1.2.1",
            "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz",
            "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==",
            "license": "BSD-3-Clause",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/string-width": {
            "version": "4.2.3",
            "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",
            "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
            "license": "MIT",
            "dependencies": {
                "emoji-regex": "^8.0.0",
                "is-fullwidth-code-point": "^3.0.0",
                "strip-ansi": "^6.0.1"
            },
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/string.prototype.matchall": {
            "version": "4.0.12",
            "resolved": "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.12.tgz",
            "integrity": "sha512-6CC9uyBL+/48dYizRf7H7VAYCMCNTBeM78x/VTUe9bFEaxBepPJDa1Ow99LqI/1yF7kuy7Q3cQsYMrcjGUcskA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "call-bound": "^1.0.3",
                "define-properties": "^1.2.1",
                "es-abstract": "^1.23.6",
                "es-errors": "^1.3.0",
                "es-object-atoms": "^1.0.0",
                "get-intrinsic": "^1.2.6",
                "gopd": "^1.2.0",
                "has-symbols": "^1.1.0",
                "internal-slot": "^1.1.0",
                "regexp.prototype.flags": "^1.5.3",
                "set-function-name": "^2.0.2",
                "side-channel": "^1.1.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/string.prototype.repeat": {
            "version": "1.0.0",
            "resolved": "https://registry.npmjs.org/string.prototype.repeat/-/string.prototype.repeat-1.0.0.tgz",
            "integrity": "sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "define-properties": "^1.1.3",
                "es-abstract": "^1.17.5"
            }
        },
        "node_modules/string.prototype.trim": {
            "version": "1.2.10",
            "resolved": "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz",
            "integrity": "sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "call-bound": "^1.0.2",
                "define-data-property": "^1.1.4",
                "define-properties": "^1.2.1",
                "es-abstract": "^1.23.5",
                "es-object-atoms": "^1.0.0",
                "has-property-descriptors": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/string.prototype.trimend": {
            "version": "1.0.9",
            "resolved": "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz",
            "integrity": "sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "call-bound": "^1.0.2",
                "define-properties": "^1.2.1",
                "es-object-atoms": "^1.0.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/string.prototype.trimstart": {
            "version": "1.0.8",
            "resolved": "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz",
            "integrity": "sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.7",
                "define-properties": "^1.2.1",
                "es-object-atoms": "^1.0.0"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/strip-ansi": {
            "version": "6.0.1",
            "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
            "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
            "license": "MIT",
            "dependencies": {
                "ansi-regex": "^5.0.1"
            },
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/strip-json-comments": {
            "version": "3.1.1",
            "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz",
            "integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=8"
            },
            "funding": {
                "url": "https://github.com/sponsors/sindresorhus"
            }
        },
        "node_modules/supports-color": {
            "version": "8.1.1",
            "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz",
            "integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==",
            "license": "MIT",
            "dependencies": {
                "has-flag": "^4.0.0"
            },
            "engines": {
                "node": ">=10"
            },
            "funding": {
                "url": "https://github.com/chalk/supports-color?sponsor=1"
            }
        },
        "node_modules/supports-preserve-symlinks-flag": {
            "version": "1.0.0",
            "resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz",
            "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/tabbable": {
            "version": "6.2.0",
            "resolved": "https://registry.npmjs.org/tabbable/-/tabbable-6.2.0.tgz",
            "integrity": "sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==",
            "license": "MIT"
        },
        "node_modules/tailwind-merge": {
            "version": "3.2.0",
            "resolved": "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-3.2.0.tgz",
            "integrity": "sha512-FQT/OVqCD+7edmmJpsgCsY820RTD5AkBryuG5IUqR5YQZSdj5xlH5nLgH7YPths7WsLPSpSBNneJdM8aS8aeFA==",
            "license": "MIT",
            "funding": {
                "type": "github",
                "url": "https://github.com/sponsors/dcastil"
            }
        },
        "node_modules/tailwindcss": {
            "version": "4.0.10",
            "resolved": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-4.0.10.tgz",
            "integrity": "sha512-Z8U/6E2BWSdDkt3IWPiphoV+8V6aNzRmu2SriSbuhm6i3QIcY3TdUJzUP5NX8M8MZuIl+v4/77Rer8u4YSrSsg==",
            "license": "MIT"
        },
        "node_modules/tailwindcss-animate": {
            "version": "1.0.7",
            "resolved": "https://registry.npmjs.org/tailwindcss-animate/-/tailwindcss-animate-1.0.7.tgz",
            "integrity": "sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA==",
            "license": "MIT",
            "peerDependencies": {
                "tailwindcss": ">=3.0.0 || insiders"
            }
        },
        "node_modules/tapable": {
            "version": "2.2.1",
            "resolved": "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz",
            "integrity": "sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==",
            "license": "MIT",
            "engines": {
                "node": ">=6"
            }
        },
        "node_modules/tinyglobby": {
            "version": "0.2.13",
            "resolved": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.13.tgz",
            "integrity": "sha512-mEwzpUgrLySlveBwEVDMKk5B57bhLPYovRfPAXD5gA/98Opn0rCDj3GtLwFvCvH5RK9uPCExUROW5NjDwvqkxw==",
            "license": "MIT",
            "dependencies": {
                "fdir": "^6.4.4",
                "picomatch": "^4.0.2"
            },
            "engines": {
                "node": ">=12.0.0"
            },
            "funding": {
                "url": "https://github.com/sponsors/SuperchupuDev"
            }
        },
        "node_modules/tinyglobby/node_modules/fdir": {
            "version": "6.4.4",
            "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.4.tgz",
            "integrity": "sha512-1NZP+GK4GfuAv3PqKvxQRDMjdSRZjnkq7KfhlNrCNNlZ0ygQFpebfrnfnq/W7fpUnAv9aGWmY1zKx7FYL3gwhg==",
            "license": "MIT",
            "peerDependencies": {
                "picomatch": "^3 || ^4"
            },
            "peerDependenciesMeta": {
                "picomatch": {
                    "optional": true
                }
            }
        },
        "node_modules/tinyglobby/node_modules/picomatch": {
            "version": "4.0.2",
            "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz",
            "integrity": "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==",
            "license": "MIT",
            "engines": {
                "node": ">=12"
            },
            "funding": {
                "url": "https://github.com/sponsors/jonschlinkert"
            }
        },
        "node_modules/to-regex-range": {
            "version": "5.0.1",
            "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz",
            "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "is-number": "^7.0.0"
            },
            "engines": {
                "node": ">=8.0"
            }
        },
        "node_modules/tree-kill": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.2.tgz",
            "integrity": "sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==",
            "license": "MIT",
            "bin": {
                "tree-kill": "cli.js"
            }
        },
        "node_modules/ts-api-utils": {
            "version": "2.0.1",
            "resolved": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-2.0.1.tgz",
            "integrity": "sha512-dnlgjFSVetynI8nzgJ+qF62efpglpWRk8isUEWZGWlJYySCTD6aKvbUDu+zbPeDakk3bg5H4XpitHukgfL1m9w==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=18.12"
            },
            "peerDependencies": {
                "typescript": ">=4.8.4"
            }
        },
        "node_modules/tslib": {
            "version": "2.8.1",
            "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz",
            "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==",
            "license": "0BSD"
        },
        "node_modules/type-check": {
            "version": "0.4.0",
            "resolved": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz",
            "integrity": "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "prelude-ls": "^1.2.1"
            },
            "engines": {
                "node": ">= 0.8.0"
            }
        },
        "node_modules/typed-array-buffer": {
            "version": "1.0.3",
            "resolved": "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz",
            "integrity": "sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3",
                "es-errors": "^1.3.0",
                "is-typed-array": "^1.1.14"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/typed-array-byte-length": {
            "version": "1.0.3",
            "resolved": "https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz",
            "integrity": "sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.8",
                "for-each": "^0.3.3",
                "gopd": "^1.2.0",
                "has-proto": "^1.2.0",
                "is-typed-array": "^1.1.14"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/typed-array-byte-offset": {
            "version": "1.0.4",
            "resolved": "https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz",
            "integrity": "sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "available-typed-arrays": "^1.0.7",
                "call-bind": "^1.0.8",
                "for-each": "^0.3.3",
                "gopd": "^1.2.0",
                "has-proto": "^1.2.0",
                "is-typed-array": "^1.1.15",
                "reflect.getprototypeof": "^1.0.9"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/typed-array-length": {
            "version": "1.0.7",
            "resolved": "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.7.tgz",
            "integrity": "sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bind": "^1.0.7",
                "for-each": "^0.3.3",
                "gopd": "^1.0.1",
                "is-typed-array": "^1.1.13",
                "possible-typed-array-names": "^1.0.0",
                "reflect.getprototypeof": "^1.0.6"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/typescript": {
            "version": "5.8.2",
            "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.8.2.tgz",
            "integrity": "sha512-aJn6wq13/afZp/jT9QZmwEjDqqvSGp1VT5GVg+f/t6/oVyrgXM6BY1h9BRh/O5p3PlUPAe+WuiEZOmb/49RqoQ==",
            "license": "Apache-2.0",
            "bin": {
                "tsc": "bin/tsc",
                "tsserver": "bin/tsserver"
            },
            "engines": {
                "node": ">=14.17"
            }
        },
        "node_modules/typescript-eslint": {
            "version": "8.26.0",
            "resolved": "https://registry.npmjs.org/typescript-eslint/-/typescript-eslint-8.26.0.tgz",
            "integrity": "sha512-PtVz9nAnuNJuAVeUFvwztjuUgSnJInODAUx47VDwWPXzd5vismPOtPtt83tzNXyOjVQbPRp786D6WFW/M2koIA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@typescript-eslint/eslint-plugin": "8.26.0",
                "@typescript-eslint/parser": "8.26.0",
                "@typescript-eslint/utils": "8.26.0"
            },
            "engines": {
                "node": "^18.18.0 || ^20.9.0 || >=21.1.0"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/typescript-eslint"
            },
            "peerDependencies": {
                "eslint": "^8.57.0 || ^9.0.0",
                "typescript": ">=4.8.4 <5.9.0"
            }
        },
        "node_modules/unbox-primitive": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.1.0.tgz",
            "integrity": "sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.3",
                "has-bigints": "^1.0.2",
                "has-symbols": "^1.1.0",
                "which-boxed-primitive": "^1.1.1"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/undici-types": {
            "version": "6.20.0",
            "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.20.0.tgz",
            "integrity": "sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==",
            "devOptional": true,
            "license": "MIT"
        },
        "node_modules/update-browserslist-db": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz",
            "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==",
            "funding": [
                {
                    "type": "opencollective",
                    "url": "https://opencollective.com/browserslist"
                },
                {
                    "type": "tidelift",
                    "url": "https://tidelift.com/funding/github/npm/browserslist"
                },
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "dependencies": {
                "escalade": "^3.2.0",
                "picocolors": "^1.1.1"
            },
            "bin": {
                "update-browserslist-db": "cli.js"
            },
            "peerDependencies": {
                "browserslist": ">= 4.21.0"
            }
        },
        "node_modules/uri-js": {
            "version": "4.4.1",
            "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz",
            "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==",
            "dev": true,
            "license": "BSD-2-Clause",
            "dependencies": {
                "punycode": "^2.1.0"
            }
        },
        "node_modules/use-callback-ref": {
            "version": "1.3.3",
            "resolved": "https://registry.npmjs.org/use-callback-ref/-/use-callback-ref-1.3.3.tgz",
            "integrity": "sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==",
            "license": "MIT",
            "dependencies": {
                "tslib": "^2.0.0"
            },
            "engines": {
                "node": ">=10"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/use-sidecar": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/use-sidecar/-/use-sidecar-1.1.3.tgz",
            "integrity": "sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==",
            "license": "MIT",
            "dependencies": {
                "detect-node-es": "^1.1.0",
                "tslib": "^2.0.0"
            },
            "engines": {
                "node": ">=10"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/vite": {
            "version": "6.3.5",
            "resolved": "https://registry.npmjs.org/vite/-/vite-6.3.5.tgz",
            "integrity": "sha512-cZn6NDFE7wdTpINgs++ZJ4N49W2vRp8LCKrn3Ob1kYNtOo21vfDoaV5GzBfLU4MovSAB8uNRm4jgzVQZ+mBzPQ==",
            "license": "MIT",
            "dependencies": {
                "esbuild": "^0.25.0",
                "fdir": "^6.4.4",
                "picomatch": "^4.0.2",
                "postcss": "^8.5.3",
                "rollup": "^4.34.9",
                "tinyglobby": "^0.2.13"
            },
            "bin": {
                "vite": "bin/vite.js"
            },
            "engines": {
                "node": "^18.0.0 || ^20.0.0 || >=22.0.0"
            },
            "funding": {
                "url": "https://github.com/vitejs/vite?sponsor=1"
            },
            "optionalDependencies": {
                "fsevents": "~2.3.3"
            },
            "peerDependencies": {
                "@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0",
                "jiti": ">=1.21.0",
                "less": "*",
                "lightningcss": "^1.21.0",
                "sass": "*",
                "sass-embedded": "*",
                "stylus": "*",
                "sugarss": "*",
                "terser": "^5.16.0",
                "tsx": "^4.8.1",
                "yaml": "^2.4.2"
            },
            "peerDependenciesMeta": {
                "@types/node": {
                    "optional": true
                },
                "jiti": {
                    "optional": true
                },
                "less": {
                    "optional": true
                },
                "lightningcss": {
                    "optional": true
                },
                "sass": {
                    "optional": true
                },
                "sass-embedded": {
                    "optional": true
                },
                "stylus": {
                    "optional": true
                },
                "sugarss": {
                    "optional": true
                },
                "terser": {
                    "optional": true
                },
                "tsx": {
                    "optional": true
                },
                "yaml": {
                    "optional": true
                }
            }
        },
        "node_modules/vite-plugin-full-reload": {
            "version": "1.2.0",
            "resolved": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-1.2.0.tgz",
            "integrity": "sha512-kz18NW79x0IHbxRSHm0jttP4zoO9P9gXh+n6UTwlNKnviTTEpOlum6oS9SmecrTtSr+muHEn5TUuC75UovQzcA==",
            "license": "MIT",
            "dependencies": {
                "picocolors": "^1.0.0",
                "picomatch": "^2.3.1"
            }
        },
        "node_modules/vite/node_modules/fdir": {
            "version": "6.4.4",
            "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.4.tgz",
            "integrity": "sha512-1NZP+GK4GfuAv3PqKvxQRDMjdSRZjnkq7KfhlNrCNNlZ0ygQFpebfrnfnq/W7fpUnAv9aGWmY1zKx7FYL3gwhg==",
            "license": "MIT",
            "peerDependencies": {
                "picomatch": "^3 || ^4"
            },
            "peerDependenciesMeta": {
                "picomatch": {
                    "optional": true
                }
            }
        },
        "node_modules/vite/node_modules/picomatch": {
            "version": "4.0.2",
            "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz",
            "integrity": "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==",
            "license": "MIT",
            "engines": {
                "node": ">=12"
            },
            "funding": {
                "url": "https://github.com/sponsors/jonschlinkert"
            }
        },
        "node_modules/which": {
            "version": "2.0.2",
            "resolved": "https://registry.npmjs.org/which/-/which-2.0.2.tgz",
            "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "isexe": "^2.0.0"
            },
            "bin": {
                "node-which": "bin/node-which"
            },
            "engines": {
                "node": ">= 8"
            }
        },
        "node_modules/which-boxed-primitive": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz",
            "integrity": "sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "is-bigint": "^1.1.0",
                "is-boolean-object": "^1.2.1",
                "is-number-object": "^1.1.1",
                "is-string": "^1.1.1",
                "is-symbol": "^1.1.1"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/which-builtin-type": {
            "version": "1.2.1",
            "resolved": "https://registry.npmjs.org/which-builtin-type/-/which-builtin-type-1.2.1.tgz",
            "integrity": "sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "call-bound": "^1.0.2",
                "function.prototype.name": "^1.1.6",
                "has-tostringtag": "^1.0.2",
                "is-async-function": "^2.0.0",
                "is-date-object": "^1.1.0",
                "is-finalizationregistry": "^1.1.0",
                "is-generator-function": "^1.0.10",
                "is-regex": "^1.2.1",
                "is-weakref": "^1.0.2",
                "isarray": "^2.0.5",
                "which-boxed-primitive": "^1.1.0",
                "which-collection": "^1.0.2",
                "which-typed-array": "^1.1.16"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/which-collection": {
            "version": "1.0.2",
            "resolved": "https://registry.npmjs.org/which-collection/-/which-collection-1.0.2.tgz",
            "integrity": "sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "is-map": "^2.0.3",
                "is-set": "^2.0.3",
                "is-weakmap": "^2.0.2",
                "is-weakset": "^2.0.3"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/which-typed-array": {
            "version": "1.1.18",
            "resolved": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.18.tgz",
            "integrity": "sha512-qEcY+KJYlWyLH9vNbsr6/5j59AXk5ni5aakf8ldzBvGde6Iz4sxZGkJyWSAueTG7QhOvNRYb1lDdFmL5Td0QKA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "available-typed-arrays": "^1.0.7",
                "call-bind": "^1.0.8",
                "call-bound": "^1.0.3",
                "for-each": "^0.3.3",
                "gopd": "^1.2.0",
                "has-tostringtag": "^1.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/word-wrap": {
            "version": "1.2.5",
            "resolved": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz",
            "integrity": "sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/wrap-ansi": {
            "version": "7.0.0",
            "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz",
            "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==",
            "license": "MIT",
            "dependencies": {
                "ansi-styles": "^4.0.0",
                "string-width": "^4.1.0",
                "strip-ansi": "^6.0.0"
            },
            "engines": {
                "node": ">=10"
            },
            "funding": {
                "url": "https://github.com/chalk/wrap-ansi?sponsor=1"
            }
        },
        "node_modules/y18n": {
            "version": "5.0.8",
            "resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz",
            "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==",
            "license": "ISC",
            "engines": {
                "node": ">=10"
            }
        },
        "node_modules/yallist": {
            "version": "3.1.1",
            "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz",
            "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==",
            "license": "ISC"
        },
        "node_modules/yargs": {
            "version": "17.7.2",
            "resolved": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz",
            "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==",
            "license": "MIT",
            "dependencies": {
                "cliui": "^8.0.1",
                "escalade": "^3.1.1",
                "get-caller-file": "^2.0.5",
                "require-directory": "^2.1.1",
                "string-width": "^4.2.3",
                "y18n": "^5.0.5",
                "yargs-parser": "^21.1.1"
            },
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/yargs-parser": {
            "version": "21.1.1",
            "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz",
            "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==",
            "license": "ISC",
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/yocto-queue": {
            "version": "0.1.0",
            "resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz",
            "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=10"
            },
            "funding": {
                "url": "https://github.com/sponsors/sindresorhus"
            }
        }
    }
}
