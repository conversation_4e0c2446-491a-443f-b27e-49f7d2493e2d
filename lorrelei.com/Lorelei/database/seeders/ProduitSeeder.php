<?php

namespace Database\Seeders;

use App\Models\Categorie;
use App\Models\Marchand;
use App\Models\Produit;
use Illuminate\Database\Seeder;

class ProduitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les catégories
        $categories = Categorie::all();
        if ($categories->isEmpty()) {
            $this->command->info('Aucune catégorie trouvée. Exécutez d\'abord le seeder de catégories.');
            return;
        }

        // Récupérer les marchands
        $marchands = Marchand::all();
        if ($marchands->isEmpty()) {
            $this->command->info('Aucun marchand trouvé. Exécutez d\'abord le seeder d\'utilisateurs.');
            return;
        }

        // Produits pour la catégorie Vêtements - Hommes
        $categorieHommes = Categorie::where('nom', 'Hommes')->first();
        if ($categorieHommes) {
            $produits = [
                [
                    'nom' => 'T-shirt Homme Premium',
                    'description' => 'T-shirt en coton bio pour homme, coupe régulière',
                    'prix' => 29.99,
                    'stock' => 100,
                    'images' => json_encode(['/images/products/tshirt.png', '/images/products/tshirt-2.png']),
                ],
                [
                    'nom' => 'Jeans Slim Homme',
                    'description' => 'Jeans slim fit pour homme, coton stretch',
                    'prix' => 59.99,
                    'stock' => 50,
                    'images' => json_encode(['/images/products/jeans.png']),
                ],
            ];

            foreach ($produits as $produit) {
                Produit::firstOrCreate(
                    ['nom' => $produit['nom']],
                    [
                        'description' => $produit['description'],
                        'prix' => $produit['prix'],
                        'stock' => $produit['stock'],
                        'images' => $produit['images'],
                        'categorie_id' => $categorieHommes->id,
                        'marchand_id' => $marchands->random()->id,
                        'creeLe' => now(),
                    ]
                );
            }
        }

        // Produits pour la catégorie Vêtements - Femmes
        $categorieFemmes = Categorie::where('nom', 'Femmes')->first();
        if ($categorieFemmes) {
            $produits = [
                [
                    'nom' => 'Robe d\'Eté Femme',
                    'description' => 'Robe légère pour l\'eté, motif floral',
                    'prix' => 49.99,
                    'stock' => 75,
                    'images' => json_encode(['/images/products/dress.png']),
                ],
                [
                    'nom' => 'Blouse Femme',
                    'description' => 'Blouse élégante pour femme, tissu fluide',
                    'prix' => 39.99,
                    'stock' => 60,
                    'images' => json_encode(['/images/products/blouse.png']),
                ],
            ];

            foreach ($produits as $produit) {
                Produit::firstOrCreate(
                    ['nom' => $produit['nom']],
                    [
                        'description' => $produit['description'],
                        'prix' => $produit['prix'],
                        'stock' => $produit['stock'],
                        'images' => $produit['images'],
                        'categorie_id' => $categorieFemmes->id,
                        'marchand_id' => $marchands->random()->id,
                        'creeLe' => now(),
                    ]
                );
            }
        }

        // Produits pour la catégorie Chaussures
        $categorieChaussures = Categorie::where('nom', 'Chaussures')->first();
        if ($categorieChaussures) {
            $produits = [
                [
                    'nom' => 'Baskets Urbaines',
                    'description' => 'Baskets confortables pour un style urbain',
                    'prix' => 79.99,
                    'stock' => 40,
                    'images' => json_encode(['/images/products/shoes.png', '/images/products/shoes-2.png']),
                ],
            ];

            foreach ($produits as $produit) {
                Produit::firstOrCreate(
                    ['nom' => $produit['nom']],
                    [
                        'description' => $produit['description'],
                        'prix' => $produit['prix'],
                        'stock' => $produit['stock'],
                        'images' => $produit['images'],
                        'categorie_id' => $categorieChaussures->id,
                        'marchand_id' => $marchands->random()->id,
                        'creeLe' => now(),
                    ]
                );
            }
        }

        // Produits pour la catégorie Électronique - Smartphones
        $categorieSmartphones = Categorie::where('nom', 'Smartphones')->first();
        if ($categorieSmartphones) {
            $produits = [
                [
                    'nom' => 'Smartphone XYZ Pro',
                    'description' => 'Smartphone haut de gamme avec caméra 108MP',
                    'prix' => 899.99,
                    'stock' => 25,
                    'images' => json_encode(['/images/products/smartphone.png', '/images/products/smartphone-2.png']),
                ],
            ];

            foreach ($produits as $produit) {
                Produit::firstOrCreate(
                    ['nom' => $produit['nom']],
                    [
                        'description' => $produit['description'],
                        'prix' => $produit['prix'],
                        'stock' => $produit['stock'],
                        'images' => $produit['images'],
                        'categorie_id' => $categorieSmartphones->id,
                        'marchand_id' => $marchands->random()->id,
                        'creeLe' => now(),
                    ]
                );
            }
        }

        // Produits pour la catégorie Électronique - Ordinateurs portables
        $categorieLaptops = Categorie::where('nom', 'Ordinateurs portables')->first();
        if ($categorieLaptops) {
            $produits = [
                [
                    'nom' => 'Ordinateur Portable UltraBook',
                    'description' => 'Ordinateur portable léger et puissant pour les professionnels',
                    'prix' => 1299.99,
                    'stock' => 15,
                    'images' => json_encode(['/images/products/laptop.png', '/images/products/laptop-2.png']),
                ],
            ];

            foreach ($produits as $produit) {
                Produit::firstOrCreate(
                    ['nom' => $produit['nom']],
                    [
                        'description' => $produit['description'],
                        'prix' => $produit['prix'],
                        'stock' => $produit['stock'],
                        'images' => $produit['images'],
                        'categorie_id' => $categorieLaptops->id,
                        'marchand_id' => $marchands->random()->id,
                        'creeLe' => now(),
                    ]
                );
            }
        }

        // Produits pour la catégorie Accessoires
        $categorieAccessoires = Categorie::where('nom', 'Accessoires')->first();
        if ($categorieAccessoires) {
            $produits = [
                [
                    'nom' => 'Sac à Dos Urbain',
                    'description' => 'Sac à dos spacieux avec compartiment pour ordinateur portable',
                    'prix' => 69.99,
                    'stock' => 30,
                    'images' => json_encode(['/images/products/backpack.png', '/images/products/backpack-2.png']),
                ],
                [
                    'nom' => 'Casque Audio Sans Fil',
                    'description' => 'Casque audio bluetooth avec réduction de bruit active',
                    'prix' => 149.99,
                    'stock' => 20,
                    'images' => json_encode(['/images/products/headphones.png', '/images/products/headphones-2.png']),
                ],
            ];

            foreach ($produits as $produit) {
                Produit::firstOrCreate(
                    ['nom' => $produit['nom']],
                    [
                        'description' => $produit['description'],
                        'prix' => $produit['prix'],
                        'stock' => $produit['stock'],
                        'images' => $produit['images'],
                        'categorie_id' => $categorieAccessoires->id,
                        'marchand_id' => $marchands->random()->id,
                        'creeLe' => now(),
                    ]
                );
            }
        }
    }
}
