<?php

namespace Database\Seeders;

use App\Models\Size;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SizeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tailles de vêtements standards
        $clothingSizes = [
            ['name_fr' => 'Très petit', 'name_en' => 'Extra Small', 'code' => 'XS', 'category' => 'clothing'],
            ['name_fr' => 'Petit', 'name_en' => 'Small', 'code' => 'S', 'category' => 'clothing'],
            ['name_fr' => 'Moyen', 'name_en' => 'Medium', 'code' => 'M', 'category' => 'clothing'],
            ['name_fr' => 'Grand', 'name_en' => 'Large', 'code' => 'L', 'category' => 'clothing'],
            ['name_fr' => 'Très grand', 'name_en' => 'Extra Large', 'code' => 'XL', 'category' => 'clothing'],
            ['name_fr' => 'Très très grand', 'name_en' => 'Double Extra Large', 'code' => 'XXL', 'category' => 'clothing'],
        ];

        // Tailles de chaussures européennes
        $shoeSizes = [];
        for ($i = 35; $i <= 47; $i++) {
            $shoeSizes[] = [
                'name_fr' => 'Pointure ' . $i,
                'name_en' => 'Size ' . $i,
                'code' => (string) $i,
                'category' => 'shoes',
            ];
        }

        // Tailles numériques pour vêtements
        $numericSizes = [];
        for ($i = 34; $i <= 50; $i += 2) {
            $numericSizes[] = [
                'name_fr' => 'Taille ' . $i,
                'name_en' => 'Size ' . $i,
                'code' => (string) $i,
                'category' => 'clothing',
            ];
        }

        // Fusionner toutes les tailles
        $allSizes = array_merge($clothingSizes, $shoeSizes, $numericSizes);

        // Créer les tailles
        foreach ($allSizes as $size) {
            Size::updateOrCreate(
                ['code' => $size['code'], 'category' => $size['category']],
                [
                    'name_fr' => $size['name_fr'],
                    'name_en' => $size['name_en'],
                    'is_active' => true,
                ]
            );
        }
    }
}
