<?php

namespace Database\Seeders;

use App\Models\SizeGuide;
use App\Models\Categorie;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SizeGuideSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Guide des tailles pour vêtements
        $clothingGuide = SizeGuide::create([
            'name_fr' => 'Guide des tailles - Vêtements',
            'name_en' => 'Size Guide - Clothing',
            'category' => 'clothing',
            'measurement_types' => [
                [
                    'name_fr' => 'Tour de poitrine',
                    'name_en' => 'Chest',
                    'code' => 'chest',
                    'unit' => 'cm'
                ],
                [
                    'name_fr' => 'Tour de taille',
                    'name_en' => 'Waist',
                    'code' => 'waist',
                    'unit' => 'cm'
                ],
                [
                    'name_fr' => 'Tour de hanches',
                    'name_en' => 'Hips',
                    'code' => 'hips',
                    'unit' => 'cm'
                ]
            ],
            'size_systems' => [
                [
                    'name_fr' => 'Standard',
                    'name_en' => 'Standard',
                    'code' => 'standard'
                ],
                [
                    'name_fr' => 'Europe',
                    'name_en' => 'Europe',
                    'code' => 'eu'
                ],
                [
                    'name_fr' => 'Royaume-Uni',
                    'name_en' => 'United Kingdom',
                    'code' => 'uk'
                ],
                [
                    'name_fr' => 'États-Unis',
                    'name_en' => 'United States',
                    'code' => 'us'
                ]
            ],
            'size_chart' => [
                [
                    'size_code' => 'xs',
                    'system_values' => [
                        ['system' => 'standard', 'value' => 'XS'],
                        ['system' => 'eu', 'value' => '34-36'],
                        ['system' => 'uk', 'value' => '6-8'],
                        ['system' => 'us', 'value' => '2-4']
                    ],
                    'measurements' => [
                        ['type' => 'chest', 'value' => 82],
                        ['type' => 'waist', 'value' => 66],
                        ['type' => 'hips', 'value' => 90]
                    ]
                ],
                [
                    'size_code' => 's',
                    'system_values' => [
                        ['system' => 'standard', 'value' => 'S'],
                        ['system' => 'eu', 'value' => '38-40'],
                        ['system' => 'uk', 'value' => '10-12'],
                        ['system' => 'us', 'value' => '6-8']
                    ],
                    'measurements' => [
                        ['type' => 'chest', 'value' => 88],
                        ['type' => 'waist', 'value' => 72],
                        ['type' => 'hips', 'value' => 96]
                    ]
                ],
                [
                    'size_code' => 'm',
                    'system_values' => [
                        ['system' => 'standard', 'value' => 'M'],
                        ['system' => 'eu', 'value' => '42-44'],
                        ['system' => 'uk', 'value' => '14-16'],
                        ['system' => 'us', 'value' => '10-12']
                    ],
                    'measurements' => [
                        ['type' => 'chest', 'value' => 94],
                        ['type' => 'waist', 'value' => 78],
                        ['type' => 'hips', 'value' => 102]
                    ]
                ],
                [
                    'size_code' => 'l',
                    'system_values' => [
                        ['system' => 'standard', 'value' => 'L'],
                        ['system' => 'eu', 'value' => '46-48'],
                        ['system' => 'uk', 'value' => '18-20'],
                        ['system' => 'us', 'value' => '14-16']
                    ],
                    'measurements' => [
                        ['type' => 'chest', 'value' => 100],
                        ['type' => 'waist', 'value' => 84],
                        ['type' => 'hips', 'value' => 108]
                    ]
                ],
                [
                    'size_code' => 'xl',
                    'system_values' => [
                        ['system' => 'standard', 'value' => 'XL'],
                        ['system' => 'eu', 'value' => '50-52'],
                        ['system' => 'uk', 'value' => '22-24'],
                        ['system' => 'us', 'value' => '18-20']
                    ],
                    'measurements' => [
                        ['type' => 'chest', 'value' => 106],
                        ['type' => 'waist', 'value' => 90],
                        ['type' => 'hips', 'value' => 114]
                    ]
                ]
            ],
            'instructions_fr' => '<p><strong>Comment prendre vos mesures :</strong></p><ul><li><strong>Tour de poitrine :</strong> Mesurez autour de la partie la plus large de votre poitrine.</li><li><strong>Tour de taille :</strong> Mesurez autour de votre taille naturelle, au niveau du nombril.</li><li><strong>Tour de hanches :</strong> Mesurez autour de la partie la plus large de vos hanches.</li></ul><p>Utilisez un mètre ruban et tenez-vous droit. Le mètre doit être à plat contre votre corps mais pas trop serré.</p>',
            'instructions_en' => '<p><strong>How to measure yourself:</strong></p><ul><li><strong>Chest:</strong> Measure around the fullest part of your chest.</li><li><strong>Waist:</strong> Measure around your natural waistline, at the level of your navel.</li><li><strong>Hips:</strong> Measure around the fullest part of your hips.</li></ul><p>Use a soft measuring tape and stand straight. The tape should be flat against your body but not too tight.</p>',
            'is_active' => true
        ]);

        // Guide des tailles pour chaussures
        $shoesGuide = SizeGuide::create([
            'name_fr' => 'Guide des tailles - Chaussures',
            'name_en' => 'Size Guide - Shoes',
            'category' => 'shoes',
            'measurement_types' => [
                [
                    'name_fr' => 'Longueur du pied',
                    'name_en' => 'Foot length',
                    'code' => 'foot_length',
                    'unit' => 'cm'
                ]
            ],
            'size_systems' => [
                [
                    'name_fr' => 'Europe',
                    'name_en' => 'Europe',
                    'code' => 'eu'
                ],
                [
                    'name_fr' => 'Royaume-Uni',
                    'name_en' => 'United Kingdom',
                    'code' => 'uk'
                ],
                [
                    'name_fr' => 'États-Unis',
                    'name_en' => 'United States',
                    'code' => 'us'
                ]
            ],
            'size_chart' => [
                [
                    'size_code' => '35',
                    'system_values' => [
                        ['system' => 'eu', 'value' => '35'],
                        ['system' => 'uk', 'value' => '2.5'],
                        ['system' => 'us', 'value' => '5']
                    ],
                    'measurements' => [
                        ['type' => 'foot_length', 'value' => 22.1]
                    ]
                ],
                [
                    'size_code' => '36',
                    'system_values' => [
                        ['system' => 'eu', 'value' => '36'],
                        ['system' => 'uk', 'value' => '3.5'],
                        ['system' => 'us', 'value' => '6']
                    ],
                    'measurements' => [
                        ['type' => 'foot_length', 'value' => 22.8]
                    ]
                ],
                [
                    'size_code' => '37',
                    'system_values' => [
                        ['system' => 'eu', 'value' => '37'],
                        ['system' => 'uk', 'value' => '4'],
                        ['system' => 'us', 'value' => '6.5']
                    ],
                    'measurements' => [
                        ['type' => 'foot_length', 'value' => 23.5]
                    ]
                ],
                [
                    'size_code' => '38',
                    'system_values' => [
                        ['system' => 'eu', 'value' => '38'],
                        ['system' => 'uk', 'value' => '5'],
                        ['system' => 'us', 'value' => '7.5']
                    ],
                    'measurements' => [
                        ['type' => 'foot_length', 'value' => 24.1]
                    ]
                ],
                [
                    'size_code' => '39',
                    'system_values' => [
                        ['system' => 'eu', 'value' => '39'],
                        ['system' => 'uk', 'value' => '6'],
                        ['system' => 'us', 'value' => '8.5']
                    ],
                    'measurements' => [
                        ['type' => 'foot_length', 'value' => 24.8]
                    ]
                ],
                [
                    'size_code' => '40',
                    'system_values' => [
                        ['system' => 'eu', 'value' => '40'],
                        ['system' => 'uk', 'value' => '6.5'],
                        ['system' => 'us', 'value' => '9']
                    ],
                    'measurements' => [
                        ['type' => 'foot_length', 'value' => 25.5]
                    ]
                ],
                [
                    'size_code' => '41',
                    'system_values' => [
                        ['system' => 'eu', 'value' => '41'],
                        ['system' => 'uk', 'value' => '7.5'],
                        ['system' => 'us', 'value' => '10']
                    ],
                    'measurements' => [
                        ['type' => 'foot_length', 'value' => 26.1]
                    ]
                ],
                [
                    'size_code' => '42',
                    'system_values' => [
                        ['system' => 'eu', 'value' => '42'],
                        ['system' => 'uk', 'value' => '8'],
                        ['system' => 'us', 'value' => '10.5']
                    ],
                    'measurements' => [
                        ['type' => 'foot_length', 'value' => 26.8]
                    ]
                ],
                [
                    'size_code' => '43',
                    'system_values' => [
                        ['system' => 'eu', 'value' => '43'],
                        ['system' => 'uk', 'value' => '9'],
                        ['system' => 'us', 'value' => '11.5']
                    ],
                    'measurements' => [
                        ['type' => 'foot_length', 'value' => 27.5]
                    ]
                ],
                [
                    'size_code' => '44',
                    'system_values' => [
                        ['system' => 'eu', 'value' => '44'],
                        ['system' => 'uk', 'value' => '9.5'],
                        ['system' => 'us', 'value' => '12']
                    ],
                    'measurements' => [
                        ['type' => 'foot_length', 'value' => 28.1]
                    ]
                ],
                [
                    'size_code' => '45',
                    'system_values' => [
                        ['system' => 'eu', 'value' => '45'],
                        ['system' => 'uk', 'value' => '10.5'],
                        ['system' => 'us', 'value' => '13']
                    ],
                    'measurements' => [
                        ['type' => 'foot_length', 'value' => 28.8]
                    ]
                ]
            ],
            'instructions_fr' => '<p><strong>Comment mesurer votre pied :</strong></p><ol><li>Placez une feuille de papier sur le sol contre un mur.</li><li>Tenez-vous debout sur la feuille, le talon contre le mur.</li><li>Marquez la position de votre orteil le plus long.</li><li>Mesurez la distance entre le mur et la marque.</li></ol><p>Mesurez les deux pieds et utilisez la mesure du pied le plus grand. Mesurez de préférence en fin de journée car les pieds ont tendance à gonfler pendant la journée.</p>',
            'instructions_en' => '<p><strong>How to measure your foot:</strong></p><ol><li>Place a sheet of paper on the floor against a wall.</li><li>Stand on the paper with your heel against the wall.</li><li>Mark the position of your longest toe.</li><li>Measure the distance from the wall to the mark.</li></ol><p>Measure both feet and use the measurement of the larger foot. It\'s best to measure in the evening as feet tend to swell during the day.</p>',
            'is_active' => true
        ]);

        // Associer les guides aux catégories appropriées
        $clothingCategories = Categorie::where('nom->fr', 'like', '%vêtement%')
            ->orWhere('nom->fr', 'like', '%robe%')
            ->orWhere('nom->fr', 'like', '%pantalon%')
            ->orWhere('nom->fr', 'like', '%chemise%')
            ->orWhere('nom->en', 'like', '%clothing%')
            ->orWhere('nom->en', 'like', '%dress%')
            ->orWhere('nom->en', 'like', '%pant%')
            ->orWhere('nom->en', 'like', '%shirt%')
            ->get();

        foreach ($clothingCategories as $category) {
            $clothingGuide->categories()->attach($category->id);
        }

        $shoesCategories = Categorie::where('nom->fr', 'like', '%chaussure%')
            ->orWhere('nom->fr', 'like', '%basket%')
            ->orWhere('nom->fr', 'like', '%botte%')
            ->orWhere('nom->en', 'like', '%shoe%')
            ->orWhere('nom->en', 'like', '%sneaker%')
            ->orWhere('nom->en', 'like', '%boot%')
            ->get();

        foreach ($shoesCategories as $category) {
            $shoesGuide->categories()->attach($category->id);
        }
    }
}
