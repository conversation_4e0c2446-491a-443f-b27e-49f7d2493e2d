<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modifier la table adresses existante
        Schema::table('adresses', function (Blueprint $table) {
            // Ajouter les nouvelles colonnes
            $table->string('nom')->nullable()->after('type');
            $table->string('prenom')->nullable()->after('nom');
            $table->string('telephone')->nullable()->after('prenom');
            $table->boolean('est_defaut')->default(false)->after('telephone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('adresses', function (Blueprint $table) {
            $table->dropColumn(['nom', 'prenom', 'telephone', 'est_defaut']);
        });
    }
};
