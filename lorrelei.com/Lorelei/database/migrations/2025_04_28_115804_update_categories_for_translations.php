<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modifier la table categories pour les champs traduisibles
        Schema::table('categories', function (Blueprint $table) {
            // Sauvegarder les données existantes
            $categories = DB::table('categories')->get();

            // Modifier les colonnes pour stocker des données JSON
            $table->json('nom')->change();
            $table->json('description')->nullable()->change();

            // Restaurer les données avec le format JSON
            foreach ($categories as $category) {
                $nomJson = json_encode(['fr' => $category->nom]);
                $descriptionJson = $category->description ? json_encode(['fr' => $category->description]) : null;

                DB::table('categories')
                    ->where('id', $category->id)
                    ->update([
                        'nom' => $nomJson,
                        'description' => $descriptionJson,
                    ]);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revenir aux types de colonnes d'origine
        Schema::table('categories', function (Blueprint $table) {
            // Sauvegarder les données existantes
            $categories = DB::table('categories')->get();

            // Modifier les colonnes pour revenir à des types simples
            $table->string('nom', 255)->change();
            $table->text('description')->nullable()->change();

            // Restaurer les données avec le premier élément de la traduction (fr)
            foreach ($categories as $category) {
                $nom = json_decode($category->nom, true);
                $description = $category->description ? json_decode($category->description, true) : null;

                DB::table('categories')
                    ->where('id', $category->id)
                    ->update([
                        'nom' => $nom['fr'] ?? '',
                        'description' => $description ? ($description['fr'] ?? '') : null,
                    ]);
            }
        });
    }
};
