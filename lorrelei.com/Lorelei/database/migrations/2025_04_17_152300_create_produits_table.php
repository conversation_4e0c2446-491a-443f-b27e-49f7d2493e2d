<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('produits', function (Blueprint $table) {
            $table->id();
            $table->foreignId('marchand_id')->constrained('marchands');
            $table->string('nom', 255);
            $table->text('description');
            $table->decimal('prix', 10, 2);
            $table->integer('stock');
            $table->json('images')->nullable();
            $table->foreignId('categorie_id')->constrained('categories');
            $table->timestamp('creeLe');
            $table->timestamp('misAJourLe')->nullable();
            $table->decimal('poids', 8, 2)->nullable();
            $table->json('dimensions')->nullable();
            $table->decimal('discount_price', 10, 2)->nullable();
            $table->timestamp('discount_start_date')->nullable();
            $table->timestamp('discount_end_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('produits');
    }
};
