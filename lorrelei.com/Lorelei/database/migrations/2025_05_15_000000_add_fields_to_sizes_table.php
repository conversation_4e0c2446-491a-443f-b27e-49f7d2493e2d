<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sizes', function (Blueprint $table) {
            // Ajouter les nouveaux champs
            $table->integer('order')->nullable()->after('is_active')
                ->comment('Ordre d\'affichage de la taille');
            $table->float('foot_length_cm')->nullable()->after('order')
                ->comment('Longueur du pied en cm (pour les chaussures)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sizes', function (Blueprint $table) {
            $table->dropColumn(['order', 'foot_length_cm']);
        });
    }
};
