<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('adresses', function (Blueprint $table) {
            $table->id();
            $table->string('rue', 255);
            $table->string('ville', 100);
            $table->string('etat', 100);
            $table->string('pays', 100);
            $table->string('codePostal', 20);
            $table->enum('type', ['Livraison', 'Facturation', 'Entreprise'])->default('Livraison');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('adresses');
    }
};
