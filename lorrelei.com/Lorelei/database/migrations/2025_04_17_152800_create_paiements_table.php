<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('paiements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('marchand_id')->constrained('marchands');
            $table->foreignId('commande_id')->nullable()->constrained('commandes');
            $table->decimal('montant', 10, 2);
            $table->enum('statut', ['EnAttente', 'Complété', 'Écho<PERSON>', 'Remboursé']);
            $table->timestamp('creeLe');
            $table->string('transaction_id', 255)->nullable();
            $table->string('methode', 100)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('paiements');
    }
};
