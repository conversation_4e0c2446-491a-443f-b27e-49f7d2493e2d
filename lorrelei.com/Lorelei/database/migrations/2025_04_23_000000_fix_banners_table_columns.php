<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Vérifier si la colonne title existe déjà correctement
        if (!Schema::hasColumn('banners', 'title') && Schema::hasColumn('banners', 'titlevarchar')) {
            // Renommer la colonne titlevarchar en title
            Schema::table('banners', function (Blueprint $table) {
                $table->renameColumn('titlevarchar', 'title');
            });
        } 
        // Si la colonne title n'existe pas du tout, la créer
        else if (!Schema::hasColumn('banners', 'title') && !Schema::hasColumn('banners', 'titlevarchar')) {
            Schema::table('banners', function (Blueprint $table) {
                $table->string('title', 255)->nullable()->after('position');
            });
        }

        // Vérifier si les autres colonnes existent, sinon les créer
        if (!Schema::hasColumn('banners', 'description')) {
            Schema::table('banners', function (Blueprint $table) {
                $table->text('description')->nullable()->after('title');
            });
        }

        if (!Schema::hasColumn('banners', 'button_text')) {
            Schema::table('banners', function (Blueprint $table) {
                $table->string('button_text', 100)->nullable()->after('description');
            });
        }

        if (!Schema::hasColumn('banners', 'type')) {
            Schema::table('banners', function (Blueprint $table) {
                $table->string('type', 50)->nullable()->after('button_text');
            });
        }

        // S'assurer que image_url est nullable
        Schema::table('banners', function (Blueprint $table) {
            $table->string('image_url', 255)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Pas de rollback pour cette migration de correction
    }
};
