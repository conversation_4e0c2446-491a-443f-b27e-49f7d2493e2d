<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('size_guides', function (Blueprint $table) {
            $table->id();
            $table->string('name_fr')->comment('Nom du guide en français');
            $table->string('name_en')->comment('Nom du guide en anglais');
            $table->string('category')->comment('Catégorie (vêtements, chaussures, etc.)');
            $table->json('measurement_types')->comment('Types de mesures (tour de poitrine, taille, etc.)');
            $table->json('size_systems')->comment('Systèmes de tailles disponibles (Standard, EU, UK, US, etc.)');
            $table->json('size_chart')->comment('Tableau des tailles au format JSON');
            $table->text('instructions_fr')->nullable()->comment('Instructions de mesure en français');
            $table->text('instructions_en')->nullable()->comment('Instructions de mesure en anglais');
            $table->string('image')->nullable()->comment('Image illustrant comment prendre les mesures');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Table pivot pour associer les guides de tailles aux catégories de produits
        Schema::create('categorie_size_guide', function (Blueprint $table) {
            $table->id();
            $table->foreignId('categorie_id')->constrained('categories')->onDelete('cascade');
            $table->foreignId('size_guide_id')->constrained('size_guides')->onDelete('cascade');
            $table->timestamps();
            
            // Contrainte d'unicité
            $table->unique(['categorie_id', 'size_guide_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categorie_size_guide');
        Schema::dropIfExists('size_guides');
    }
};
