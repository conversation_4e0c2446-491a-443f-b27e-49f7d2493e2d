-- Dés<PERSON>r les contraintes de clé étrangère
SET FOREIGN_KEY_CHECKS=0;

-- Supprimer la base de données si elle existe
DROP DATABASE IF EXISTS lorelei_ecom;

-- C<PERSON>er la base de données
CREATE DATABASE lorelei_ecom CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Utiliser la base de données
USE lorelei_ecom;

-- Table migrations
CREATE TABLE migrations (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT,
    migration VARCHAR(255) NOT NULL,
    batch INT NOT NULL,
    PRIMARY KEY (id)
);

-- Table users
CREATE TABLE users (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    name VARCHAR(255) NULL,
    email VARCHAR(255) NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    role VARCHAR(255) NOT NULL DEFAULT 'client',
    prenom VARCHAR(255) NULL,
    telephone VARCHAR(255) NULL,
    date_naissance DATE NULL,
    PRIMARY KEY (id),
    UNIQUE INDEX users_email_unique (email)
);

-- Table cache
CREATE TABLE cache (
    `key` VARCHAR(255) NOT NULL,
    value MEDIUMTEXT NOT NULL,
    expiration INT NOT NULL,
    PRIMARY KEY (`key`)
);

-- Table cache_locks
CREATE TABLE cache_locks (
    `key` VARCHAR(255) NOT NULL,
    owner VARCHAR(255) NOT NULL,
    expiration INT NOT NULL,
    PRIMARY KEY (`key`)
);

-- Table jobs
CREATE TABLE jobs (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    queue VARCHAR(255) NOT NULL,
    payload LONGTEXT NOT NULL,
    attempts TINYINT UNSIGNED NOT NULL,
    reserved_at INT UNSIGNED NULL,
    available_at INT UNSIGNED NOT NULL,
    created_at INT UNSIGNED NOT NULL,
    PRIMARY KEY (id),
    INDEX jobs_queue_index (queue)
);

-- Table failed_jobs
CREATE TABLE failed_jobs (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    uuid VARCHAR(255) NOT NULL,
    connection TEXT NOT NULL,
    queue TEXT NOT NULL,
    payload LONGTEXT NOT NULL,
    exception LONGTEXT NOT NULL,
    failed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE INDEX failed_jobs_uuid_unique (uuid)
);

-- Table job_batches
CREATE TABLE job_batches (
    id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    total_jobs INT NOT NULL,
    pending_jobs INT NOT NULL,
    failed_jobs INT NOT NULL,
    failed_job_ids TEXT NOT NULL,
    options MEDIUMTEXT NULL,
    cancelled_at INT UNSIGNED NULL,
    created_at INT UNSIGNED NOT NULL,
    finished_at INT UNSIGNED NULL,
    PRIMARY KEY (id)
);

-- Table password_reset_tokens
CREATE TABLE password_reset_tokens (
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NULL,
    PRIMARY KEY (email)
);

-- Table sessions
CREATE TABLE sessions (
    id VARCHAR(255) NOT NULL,
    user_id BIGINT UNSIGNED NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    payload LONGTEXT NOT NULL,
    last_activity INT NOT NULL,
    PRIMARY KEY (id)
);

-- Table sizes
CREATE TABLE sizes (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'clothing',
    description TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    size_order INT NOT NULL DEFAULT 0,
    is_active TINYINT(1) NOT NULL DEFAULT 1,
    PRIMARY KEY (id),
    UNIQUE INDEX sizes_code_unique (code)
);

-- Table adresses
CREATE TABLE adresses (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    rue VARCHAR(255) NOT NULL,
    ville VARCHAR(100) NOT NULL,
    etat VARCHAR(100) NOT NULL,
    pays VARCHAR(100) NOT NULL,
    codePostal VARCHAR(20) NOT NULL,
    type ENUM('Livraison', 'Facturation', 'Entreprise') NOT NULL DEFAULT 'Livraison',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    user_id BIGINT UNSIGNED NULL,
    nom VARCHAR(255) NULL,
    prenom VARCHAR(255) NULL,
    telephone VARCHAR(255) NULL,
    est_defaut TINYINT(1) NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

-- Table categories
CREATE TABLE categories (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    nom VARCHAR(255) NOT NULL,
    description TEXT NULL,
    image_url VARCHAR(255) NULL,
    categorie_parent_id BIGINT UNSIGNED NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    slug VARCHAR(255) NULL,
    nom_en VARCHAR(255) NULL,
    description_en TEXT NULL,
    niveau INT NOT NULL DEFAULT 0,
    category_path VARCHAR(255) NULL,
    PRIMARY KEY (id),
    UNIQUE INDEX categories_slug_unique (slug)
);

-- Table marchands
CREATE TABLE marchands (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    user_id BIGINT UNSIGNED NOT NULL,
    nomEntreprise VARCHAR(255) NOT NULL,
    adresse_id BIGINT UNSIGNED NULL,
    idFiscal VARCHAR(50) NULL,
    banqueNom VARCHAR(100) NULL,
    banqueNumeroCompte VARCHAR(50) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    PRIMARY KEY (id)
);

-- Table produits
CREATE TABLE produits (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    marchand_id BIGINT UNSIGNED NOT NULL,
    nom VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    prix DECIMAL(10, 2) NOT NULL,
    stock INT NOT NULL,
    images JSON NULL,
    categorie_id BIGINT UNSIGNED NOT NULL,
    creeLe TIMESTAMP NOT NULL,
    misAJourLe TIMESTAMP NULL,
    poids DECIMAL(8, 2) NULL,
    attributs JSON NULL,
    discount_price DECIMAL(10, 2) NULL,
    discount_start_date TIMESTAMP NULL,
    discount_end_date TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    slug VARCHAR(255) NULL,
    nom_en VARCHAR(255) NULL,
    description_en TEXT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'XAF',
    product_code VARCHAR(255) NULL,
    brand VARCHAR(255) NULL,
    PRIMARY KEY (id),
    UNIQUE INDEX produits_slug_unique (slug)
);

-- Table produit_size
CREATE TABLE produit_size (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    produit_id BIGINT UNSIGNED NOT NULL,
    size_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    PRIMARY KEY (id),
    UNIQUE INDEX produit_size_produit_id_size_id_unique (produit_id, size_id)
);

-- Réactiver les contraintes de clé étrangère
SET FOREIGN_KEY_CHECKS=1;

-- Ajouter les contraintes de clé étrangère
ALTER TABLE adresses ADD CONSTRAINT adresses_user_id_foreign FOREIGN KEY (user_id) REFERENCES users (id);
ALTER TABLE categories ADD CONSTRAINT categories_categorie_parent_id_foreign FOREIGN KEY (categorie_parent_id) REFERENCES categories (id);
ALTER TABLE marchands ADD CONSTRAINT marchands_user_id_foreign FOREIGN KEY (user_id) REFERENCES users (id);
ALTER TABLE marchands ADD CONSTRAINT marchands_adresse_id_foreign FOREIGN KEY (adresse_id) REFERENCES adresses (id);
ALTER TABLE produits ADD CONSTRAINT produits_marchand_id_foreign FOREIGN KEY (marchand_id) REFERENCES marchands (id);
ALTER TABLE produits ADD CONSTRAINT produits_categorie_id_foreign FOREIGN KEY (categorie_id) REFERENCES categories (id);
ALTER TABLE produit_size ADD CONSTRAINT produit_size_produit_id_foreign FOREIGN KEY (produit_id) REFERENCES produits (id) ON DELETE CASCADE;
ALTER TABLE produit_size ADD CONSTRAINT produit_size_size_id_foreign FOREIGN KEY (size_id) REFERENCES sizes (id) ON DELETE CASCADE;
ALTER TABLE sessions ADD CONSTRAINT sessions_user_id_foreign FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;
