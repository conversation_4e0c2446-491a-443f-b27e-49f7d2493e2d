<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\ArticleCommande;
use App\Models\Commande;
use App\Models\Produit;

class ArticleCommandeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ArticleCommande::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'commande_id' => Commande::factory(),
            'produit_id' => Produit::factory(),
            'quantite' => fake()->numberBetween(-10000, 10000),
            'prixUnitaire' => fake()->randomFloat(2, 0, 99999999.99),
        ];
    }
}
