<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\User;

class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'email' => fake()->safeEmail(),
            'password' => fake()->password(),
            'role' => fake()->randomElement(["Client","Marchand","Admin"]),
            'created_at' => fake()->dateTime(),
            'last_login_at' => fake()->dateTime(),
            'is_active' => fake()->boolean(),
            'email_verification_token' => fake()->regexify('[A-Za-z0-9]{100}'),
            'password_reset_token' => fake()->regexify('[A-Za-z0-9]{100}'),
            'password_reset_expires_at' => fake()->dateTime(),
        ];
    }
}
