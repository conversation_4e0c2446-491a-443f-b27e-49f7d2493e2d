<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Categorie;
use App\Models\Category;

class CategorieFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Categorie::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'nom' => fake()->regexify('[A-Za-z0-9]{255}'),
            'categorie_parent_id' => Category::factory(),
            'description' => fake()->text(),
            'image_url' => fake()->regexify('[A-Za-z0-9]{255}'),
            'categorie_id' => Categorie::factory(),
        ];
    }
}
