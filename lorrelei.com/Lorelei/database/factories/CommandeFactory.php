<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Adress;
use App\Models\Adresse;
use App\Models\Client;
use App\Models\Commande;
use App\Models\Marchand;

class CommandeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Commande::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'client_id' => Client::factory(),
            'marchand_id' => Marchand::factory(),
            'montantTotal' => fake()->randomFloat(2, 0, 99999999.99),
            'statut' => fake()->randomElement(["EnAttente","Exp\u00e9di\u00e9","Livr\u00e9","Annul\u00e9","EnCoursDeTraitement","Rembours\u00e9"]),
            'adresse_livraison_id' => Adress::factory(),
            'creeLe' => fake()->dateTime(),
            'dateExpeditionPrevue' => fake()->date(),
            'dateLivraisonPrevue' => fake()->date(),
            'codeSuivi' => fake()->regexify('[A-Za-z0-9]{100}'),
            'adresse_id' => Adresse::factory(),
        ];
    }
}
