<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Coupon;

class CouponFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Coupon::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'code' => fake()->regexify('[A-Za-z0-9]{50}'),
            'type' => fake()->randomElement(["Pourcentage","MontantFixe"]),
            'valeur' => fake()->randomFloat(2, 0, 99999999.99),
            'date_debut' => fake()->dateTime(),
            'date_fin' => fake()->dateTime(),
            'utilisation_max' => fake()->numberBetween(-10000, 10000),
            'utilisation_compteur' => fake()->numberBetween(-10000, 10000),
            'est_actif' => fake()->boolean(),
        ];
    }
}
