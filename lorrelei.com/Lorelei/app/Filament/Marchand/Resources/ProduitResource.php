<?php

namespace App\Filament\Marchand\Resources;

use App\Filament\Marchand\Resources\ProduitResource\Pages;
use App\Filament\Marchand\Resources\ProduitResource\RelationManagers;
use App\Models\Produit;
use App\Models\Categorie;
use App\Models\Currency;
use Filament\Forms;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ProduitResource extends Resource
{
    protected static ?string $model = Produit::class;

    public static function mutateFormDataBeforeCreate(array $data): array
    {
        // Vérifier si des attributs sont définis
        if (isset($data['attributs']) && is_array($data['attributs'])) {
            $colorCount = 0;

            // Compter le nombre d'attributs de type couleur
            foreach ($data['attributs'] as $attribut) {
                if (isset($attribut['type']) && $attribut['type'] === 'couleur') {
                    $colorCount++;
                }
            }

            // Si plus d'une couleur est définie, ne garder que la première
            if ($colorCount > 1) {
                $firstColorFound = false;

                foreach ($data['attributs'] as $key => $attribut) {
                    if (isset($attribut['type']) && $attribut['type'] === 'couleur') {
                        if ($firstColorFound) {
                            // Supprimer les couleurs supplémentaires
                            unset($data['attributs'][$key]);
                        } else {
                            $firstColorFound = true;
                        }
                    }
                }

                // Afficher une notification d'avertissement
                \Filament\Notifications\Notification::make()
                    ->title('Plusieurs couleurs détectées')
                    ->body('Un produit ne peut avoir qu\'une seule couleur. Seule la première couleur a été conservée.')
                    ->warning()
                    ->send();

                // Réindexer le tableau si des éléments ont été supprimés
                if (count($data['attributs']) > 0) {
                    $data['attributs'] = array_values($data['attributs']);
                }
            }
        }

        // Faire la même chose pour les variantes
        if (isset($data['variants']) && is_array($data['variants'])) {
            foreach ($data['variants'] as $variantIndex => $variant) {
                if (isset($variant['attributs']) && is_array($variant['attributs'])) {
                    $colorCount = 0;

                    // Compter le nombre d'attributs de type couleur
                    foreach ($variant['attributs'] as $attribut) {
                        if (isset($attribut['type']) && $attribut['type'] === 'couleur') {
                            $colorCount++;
                        }
                    }

                    // Si plus d'une couleur est définie, ne garder que la première
                    if ($colorCount > 1) {
                        $firstColorFound = false;

                        foreach ($variant['attributs'] as $key => $attribut) {
                            if (isset($attribut['type']) && $attribut['type'] === 'couleur') {
                                if ($firstColorFound) {
                                    // Supprimer les couleurs supplémentaires
                                    unset($data['variants'][$variantIndex]['attributs'][$key]);
                                } else {
                                    $firstColorFound = true;
                                }
                            }
                        }

                        // Afficher une notification d'avertissement
                        \Filament\Notifications\Notification::make()
                            ->title('Plusieurs couleurs détectées dans une variante')
                            ->body('Une variante ne peut avoir qu\'une seule couleur. Seule la première couleur a été conservée.')
                            ->warning()
                            ->send();

                        // Réindexer le tableau si des éléments ont été supprimés
                        if (count($data['variants'][$variantIndex]['attributs']) > 0) {
                            $data['variants'][$variantIndex]['attributs'] = array_values($data['variants'][$variantIndex]['attributs']);
                        }
                    }
                }
            }
        }

        return $data;
    }

    public static function mutateFormDataBeforeSave(array $data): array
    {
        // Appliquer la même logique lors de la sauvegarde
        return static::mutateFormDataBeforeCreate($data);
    }

    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';

    protected static ?string $navigationGroup = 'Gestion des Produits';

    protected static ?int $navigationSort = 1;

    protected static ?string $recordTitleAttribute = 'nom';

    protected static ?string $recordRouteKeyName = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informations Générales')
                            ->schema([
                                Tabs::make('Traductions')
                                    ->tabs([
                                        Tabs\Tab::make('Français')
                                            ->icon('heroicon-m-flag')
                                            ->schema([
                                                Forms\Components\TextInput::make('nom.fr')
                                                    ->label('Nom du produit (FR)')
                                                    ->required()
                                                    ->maxLength(255)
                                                    ->live(onBlur: true)
                                                    ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                                        if ($operation === 'create' || $operation === 'edit') {
                                                            $set('slug', \Illuminate\Support\Str::slug($state));
                                                        }
                                                    }),

                                                Forms\Components\Textarea::make('description.fr')
                                                    ->label('Description (FR)')
                                                    ->required()
                                                    ->rows(5),
                                            ]),

                                        Tabs\Tab::make('English')
                                            ->icon('heroicon-m-flag')
                                            ->schema([
                                                Forms\Components\TextInput::make('nom.en')
                                                    ->label('Nom du produit (EN)')
                                                    ->maxLength(255),

                                                Forms\Components\Textarea::make('description.en')
                                                    ->label('Description (EN)')
                                                    ->rows(5),
                                            ]),
                                    ])
                                    ->columnSpanFull(),

                                Forms\Components\Hidden::make('slug')
                                    ->dehydrated(fn ($state) => filled($state))
                                    ->afterStateHydrated(function (Forms\Get $get, Forms\Set $set, $state) {
                                        // Si le slug est vide, le générer à partir du nom
                                        if (blank($state) && filled($get('nom.fr'))) {
                                            $set('slug', \Illuminate\Support\Str::slug($get('nom.fr')));
                                        }
                                    }),

                                Forms\Components\TextInput::make('product_code')
                                    ->label('Code produit (Product code)')
                                    ->helperText('Code EAN ou référence du produit (optionnel)')
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('marque')
                                    ->label('Marque (Brand)')
                                    ->helperText('Marque du produit (optionnel)')
                                    ->maxLength(255),

                                Forms\Components\Select::make('categorie_id')
                                    ->label('Catégorie (Category)')
                                    ->options(Categorie::all()->pluck('nom', 'id'))
                                    ->required()
                                    ->searchable(),
                            ]),

                        Forms\Components\Section::make('Prix et Stock')
                            ->schema([
                                Forms\Components\Grid::make()
                                    ->schema([
                                        Forms\Components\TextInput::make('prix')
                                            ->label('Prix (Price)')
                                            ->numeric()
                                            ->required(),

                                        Forms\Components\Select::make('currency')
                                            ->label('Devise (Currency)')
                                            ->options(Currency::where('is_active', true)->pluck('name', 'code'))
                                            ->default('FCFA')
                                            ->required(),
                                    ])
                                    ->columns(2),

                                Forms\Components\TextInput::make('stock')
                                    ->label('Stock disponible (Available stock)')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0),

                                Forms\Components\Grid::make()
                                    ->schema([
                                        Forms\Components\TextInput::make('discount_price')
                                            ->label('Prix promotionnel (Discount price)')
                                            ->numeric(),

                                        Forms\Components\Placeholder::make('currency_placeholder')
                                            ->label('Devise (Currency)')
                                            ->content(fn (Forms\Get $get): string => $get('currency') ?: 'FCFA'),
                                    ])
                                    ->columns(2),

                                Forms\Components\DateTimePicker::make('discount_start_date')
                                    ->label('Début de la promotion (Discount start)'),

                                Forms\Components\DateTimePicker::make('discount_end_date')
                                    ->label('Fin de la promotion (Discount end)'),
                            ]),

                        Forms\Components\Section::make('Caractéristiques')
                            ->schema([
                                Forms\Components\TextInput::make('poids')
                                    ->label('Poids (Weight in kg)')
                                    ->numeric(),

                                Forms\Components\Repeater::make('attributs')
                                    ->label('Attributs du produit')
                                    ->schema([
                                        Forms\Components\Select::make('type')
                                            ->label('Type d\'attribut (Attribute type)')
                                            ->options([
                                                'couleur' => 'Couleur (Color)',
                                                'taille' => 'Taille (Size)',
                                                'matiere' => 'Matière (Material)',
                                                'autre' => 'Autre (Other)',
                                            ])
                                            ->helperText('Note: Un produit ne peut avoir qu\'une seule couleur')
                                            ->required()
                                            ->reactive()
                                            ->afterStateUpdated(function (Forms\Set $set, $state) {
                                                $set('valeur', null);

                                                // Si le type est couleur, définir automatiquement le code couleur par défaut
                                                if ($state === 'couleur') {
                                                    $set('code', '#e80a0a');

                                                    // Afficher une notification pour confirmer
                                                    \Filament\Notifications\Notification::make()
                                                        ->title('Type couleur sélectionné')
                                                        ->body('Code couleur par défaut défini: #e80a0a')
                                                        ->success()
                                                        ->send();
                                                }
                                            }),

                                        Forms\Components\Grid::make()
                                            ->schema(function (Forms\Get $get) {
                                                $type = $get('type');

                                                if ($type === 'couleur') {
                                                    return [
                                                        Forms\Components\TextInput::make('nom')
                                                            ->label('Nom de la couleur (Color name)')
                                                            ->placeholder('Ex: Rouge, Bleu, Vert')
                                                            ->required(),

                                                        Forms\Components\ColorPicker::make('code')
                                                            ->label('Code couleur (Color code)')
                                                            ->required()
                                                            ->default('#e80a0a')
                                                            ->live()
                                                            ->afterStateUpdated(function ($state) {
                                                                // Afficher une notification pour confirmer la sélection de couleur
                                                                \Filament\Notifications\Notification::make()
                                                                    ->title('Couleur sélectionnée')
                                                                    ->body('Code: ' . ($state ?: '#e80a0a'))
                                                                    ->success()
                                                                    ->send();
                                                            }),

                                                        Forms\Components\Toggle::make('with_image')
                                                            ->label('Utiliser une image pour cette couleur')
                                                            ->helperText('Si activé, une image sera utilisée à la place de la couleur dans le sélecteur')
                                                            ->default(false)
                                                            ->dehydrated(true)
                                                            ->live(),

                                                        Forms\Components\FileUpload::make('color_image')
                                                            ->label('Image de la couleur')
                                                            ->helperText('Image qui représente cette couleur (texture, motif, etc.)')
                                                            ->image()
                                                            ->imageEditor()
                                                            ->directory('color-images')
                                                            ->visibility('public')
                                                            ->dehydrated(true)
                                                            ->visible(fn (Forms\Get $get) => $get('with_image') === true),
                                                    ];
                                                }

                                                if ($type === 'taille') {
                                                    return [
                                                        Forms\Components\Select::make('category')
                                                            ->label('Catégorie de taille (Size category)')
                                                            ->options(function () {
                                                                return \App\Models\Size::getDistinctCategories();
                                                            })
                                                            ->required()
                                                            ->reactive()
                                                            ->afterStateUpdated(fn (Forms\Set $set) => $set('valeur', null)),

                                                        Forms\Components\Select::make('valeur')
                                                            ->label('Taille (Size)')
                                                            ->options(function (Forms\Get $get) {
                                                                $category = $get('category');

                                                                if ($category) {
                                                                    return \App\Models\Size::where('is_active', true)
                                                                        ->where('category', $category)
                                                                        ->get()
                                                                        ->pluck('full_name', 'code')
                                                                        ->toArray();
                                                                }

                                                                return [];
                                                            })
                                                            ->searchable()
                                                            ->preload()
                                                            ->required()
                                                            ->disabled(fn (Forms\Get $get) => !$get('category'))
                                                            ->helperText('Sélectionnez une taille prédéfinie'),

                                                        Forms\Components\TextInput::make('quantity')
                                                            ->label('Quantité pour cette taille')
                                                            ->numeric()
                                                            ->minValue(0)
                                                            ->default(0)
                                                            ->required()
                                                            ->helperText('Quantité de produits disponibles pour cette taille'),
                                                    ];
                                                }

                                                return [
                                                    Forms\Components\TextInput::make('valeur')
                                                        ->label('Valeur (Value)')
                                                        ->placeholder(function (Forms\Get $get) {
                                                            $type = $get('type');
                                                            return match ($type) {
                                                                'matiere' => 'Ex: Coton, Laine, Polyester',
                                                                default => 'Ex: Valeur de l\'attribut',
                                                            };
                                                        })
                                                        ->required(),
                                                ];
                                            })
                                            ->columns(2),
                                    ])
                                    ->itemLabel(function (array $state): ?string {
                                        $type = $state['type'] ?? null;

                                        if ($type === 'couleur' && isset($state['nom'])) {
                                            return ucfirst($type) . ': ' . $state['nom'];
                                        }

                                        if (isset($state['valeur'])) {
                                            return ucfirst($type) . ': ' . $state['valeur'];
                                        }

                                        return ucfirst($type ?? 'Attribut');
                                    })
                                    ->collapsible()
                                    ->collapsed(false)
                                    ->addActionLabel('Ajouter un attribut (Add attribute)')
                                    ->reorderable()
                                    ->columnSpanFull()
                                    ->helperText('Ajoutez des attributs comme la couleur, la taille, le matériau, etc. Pour les couleurs, vous pourrez sélectionner une couleur dans la palette.'),

                                // Section des variantes déplacée au niveau principal
                            ]),

                        // Section des variantes supprimée - les variantes seront créées via le VariantsRelationManager
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Images')
                            ->schema([
                                Forms\Components\FileUpload::make('images')
                                    ->label('Images du produit')
                                    ->helperText('Les 2 premières images seront utilisées comme images principales (pour l\'affichage dans les cartes et l\'effet de survol). Les images suivantes seront des images additionnelles.')
                                    ->multiple()
                                    ->image()
                                    ->imageEditor()
                                    ->disk('public_images')
                                    ->directory('products')
                                    ->visibility('public')
                                    ->maxFiles(10)
                                    ->reorderable()
                                    ->columnSpanFull(),

                                Forms\Components\Placeholder::make('main_images_info')
                                    ->label('Images principales')
                                    ->content('Les 2 premières images seront utilisées comme images principales. La première image sera affichée dans les cartes de produits, et la seconde apparaîtra lors du survol.')
                                    ->columnSpanFull(),

                                Forms\Components\Placeholder::make('additional_images_info')
                                    ->label('Images additionnelles')
                                    ->content('Toutes les images au-delà des 2 premières seront considérées comme des images additionnelles et seront affichées uniquement dans la page de détail du produit.')
                                    ->columnSpanFull(),
                            ]),

                        Forms\Components\Section::make('Statut')
                            ->schema([
                                Forms\Components\DateTimePicker::make('creeLe')
                                    ->label('Créé le')
                                    ->default(now())
                                    ->disabled(),

                                Forms\Components\DateTimePicker::make('misAJourLe')
                                    ->label('Mis à jour le')
                                    ->default(now()),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('images')
                    ->label('Image')
                    ->circular()
                    ->stacked()
                    ->disk('public_images')
                    ->limit(3),

                Tables\Columns\TextColumn::make('nom')
                    ->formatStateUsing(function ($state) {
                        if (is_array($state)) {
                            return $state['fr'] ?? '';
                        } elseif (is_string($state)) {
                            try {
                                $decoded = json_decode($state, true);
                                if (is_array($decoded)) {
                                    return $decoded['fr'] ?? '';
                                }
                            } catch (\Exception) {
                                // Si le décodage échoue, retourner l'état tel quel
                            }
                        }
                        return $state;
                    })
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('product_code')
                    ->label('Code EAN')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('marque')
                    ->label('Marque')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('categorie.nom')
                    ->label('Catégorie')
                    ->sortable(),

                Tables\Columns\TextColumn::make('prix')
                    ->formatStateUsing(function ($state, $record) {
                        return number_format($state, 2) . ' ' . ($record->currency ?: 'FCFA');
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('discount_price')
                    ->label('Prix promo')
                    ->formatStateUsing(function ($state, $record) {
                        if (empty($state)) return '';
                        return number_format($state, 2) . ' ' . ($record->currency ?: 'FCFA');
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('stock')
                    ->sortable(),

                Tables\Columns\IconColumn::make('discount_price')
                    ->label('En promotion')
                    ->boolean()
                    ->getStateUsing(fn (Produit $record): bool =>
                        $record->discount_price !== null &&
                        $record->discount_price > 0 &&
                        ($record->discount_start_date === null || $record->discount_start_date <= now()) &&
                        ($record->discount_end_date === null || $record->discount_end_date >= now())
                    ),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('categorie_id')
                    ->label('Catégorie')
                    ->options(Categorie::all()->pluck('nom', 'id')),

                Tables\Filters\Filter::make('en_promotion')
                    ->label('En promotion')
                    ->query(fn (Builder $query): Builder =>
                        $query->whereNotNull('discount_price')
                            ->where('discount_price', '>', 0)
                            ->where(function (Builder $query) {
                                $query->whereNull('discount_start_date')
                                    ->orWhere('discount_start_date', '<=', now());
                            })
                            ->where(function (Builder $query) {
                                $query->whereNull('discount_end_date')
                                    ->orWhere('discount_end_date', '>=', now());
                            })
                    ),

                Tables\Filters\Filter::make('en_stock')
                    ->label('En stock')
                    ->query(fn (Builder $query): Builder => $query->where('stock', '>', 0)),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\SizesRelationManager::class,
            RelationManagers\VariantsRelationManager::class,
            RelationManagers\ZonesLivraisonRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProduits::route('/'),
            'create' => Pages\CreateProduit::route('/create'),
            'edit' => Pages\EditProduit::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        // Filtrer les produits pour n'afficher que ceux du marchand connecté
        return parent::getEloquentQuery()
            ->where('marchand_id', Auth::user()->marchands->first()->id ?? null);
    }
}
