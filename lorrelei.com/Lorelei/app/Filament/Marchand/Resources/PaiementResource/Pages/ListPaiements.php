<?php

namespace App\Filament\Marchand\Resources\PaiementResource\Pages;

use App\Filament\Marchand\Resources\PaiementResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPaiements extends ListRecords
{
    protected static string $resource = PaiementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Les marchands ne peuvent pas créer de paiements manuellement
        ];
    }
}
