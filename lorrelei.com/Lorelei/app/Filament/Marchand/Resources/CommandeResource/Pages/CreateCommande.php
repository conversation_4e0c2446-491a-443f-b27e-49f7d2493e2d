<?php

namespace App\Filament\Marchand\Resources\CommandeResource\Pages;

use App\Filament\Marchand\Resources\CommandeResource;
use Filament\Resources\Pages\CreateRecord;

class CreateCommande extends CreateRecord
{
    protected static string $resource = CommandeResource::class;
    
    // Les marchands ne devraient pas pouvoir créer des commandes manuellement,
    // mais cette classe est nécessaire pour la structure de Filament
    
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ajouter automatiquement l'ID du marchand connecté
        $data['marchand_id'] = auth()->user()->marchands->first()->id ?? null;
        
        return $data;
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
