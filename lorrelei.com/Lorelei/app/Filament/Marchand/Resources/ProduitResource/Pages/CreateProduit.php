<?php

namespace App\Filament\Marchand\Resources\ProduitResource\Pages;

use App\Filament\Marchand\Resources\ProduitResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class CreateProduit extends CreateRecord
{
    protected static string $resource = ProduitResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ajouter automatiquement l'ID du marchand connecté
        $data['marchand_id'] = Auth::user()->marchands->first()->id ?? null;

        // Définir la date de création
        $data['creeLe'] = now();

        // Définir la date de mise à jour
        $data['misAJourLe'] = now();

        // S'assurer que le slug est défini
        if (empty($data['slug']) && isset($data['nom']['fr'])) {
            $data['slug'] = \Illuminate\Support\Str::slug($data['nom']['fr']);
        }

        return $data;
    }

    protected function afterCreate(): void
    {
        // Définir une variable de session pour indiquer qu'un produit vient d'être créé
        Session::put('product_created', true);

        // Afficher une notification de succès
        \Filament\Notifications\Notification::make()
            ->title('Produit créé')
            ->body('Le produit a été créé avec succès. Vous pouvez maintenant ajouter des variantes.')
            ->success()
            ->send();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('edit', ['record' => $this->record->id]);
    }
}
