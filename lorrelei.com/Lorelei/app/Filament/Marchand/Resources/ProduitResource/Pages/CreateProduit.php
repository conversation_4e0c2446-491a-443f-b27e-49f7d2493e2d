<?php

namespace App\Filament\Marchand\Resources\ProduitResource\Pages;

use App\Filament\Marchand\Resources\ProduitResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateProduit extends CreateRecord
{
    protected static string $resource = ProduitResource::class;
    
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ajouter automatiquement l'ID du marchand connecté
        $data['marchand_id'] = auth()->user()->marchands->first()->id ?? null;
        
        return $data;
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
