<?php

namespace App\Filament\Traits;

use App\Helpers\ImageStorage;
use Filament\Forms\Components\FileUpload;
use Illuminate\Database\Eloquent\Model;

trait HandlesImageStorage
{
    /**
     * Configure un composant FileUpload pour utiliser notre système de stockage basé sur l'ID
     *
     * @param FileUpload $component Le composant FileUpload
     * @param string $baseDir Le dossier de base (ex: 'products', 'categories', 'banners')
     * @return FileUpload Le composant configuré
     */
    public static function configureImageUpload(FileUpload $component, string $baseDir): FileUpload
    {
        return $component
            ->disk('public_images')
            ->visibility('public')
            ->directory($baseDir)
            ->saveUploadedFileUsing(function ($file, $record) use ($baseDir) {
                if (!$record || !$record->exists) {
                    // Si le record n'existe pas encore, stocker temporairement dans le dossier '0'
                    return $file->store("{$baseDir}/0", 'public_images');
                }

                // Déterminer le dossier basé sur l'ID
                $folderPrefix = ImageStorage::getFolderPrefix($record->id);
                $path = "{$baseDir}/{$folderPrefix}";

                // Créer le dossier s'il n'existe pas
                $fullPath = public_path("images/{$path}");
                if (!file_exists($fullPath)) {
                    mkdir($fullPath, 0755, true);
                }

                // Générer un nom de fichier unique et stocker le fichier
                $filename = $file->hashName();
                $file->storeAs($path, $filename, 'public_images');

                return "{$path}/{$filename}";
            });
    }

    /**
     * Méthode à appeler après la création d'un enregistrement pour déplacer les images
     * du dossier temporaire vers le dossier basé sur l'ID
     *
     * @param Model $record L'enregistrement créé
     * @param string $baseDir Le dossier de base
     * @param string $imageField Le nom du champ d'image
     * @return void
     */
    public static function moveImagesAfterCreate(Model $record, string $baseDir, string $imageField = 'images'): void
    {
        $images = $record->{$imageField};
        if (empty($images)) {
            return;
        }

        $folderPrefix = ImageStorage::getFolderPrefix($record->id);
        $newPath = "{$baseDir}/{$folderPrefix}";

        // Créer le dossier s'il n'existe pas
        $fullPath = public_path("images/{$newPath}");
        if (!file_exists($fullPath)) {
            mkdir($fullPath, 0755, true);
        }

        // Si c'est un tableau d'images
        if (is_array($images)) {
            $newImages = [];
            foreach ($images as $image) {
                // Vérifier si l'image est dans le dossier temporaire
                if (strpos($image, "{$baseDir}/0/") === 0) {
                    $filename = basename($image);
                    $oldPath = public_path("images/{$image}");
                    $newFilePath = "{$newPath}/{$filename}";
                    $newFullPath = public_path("images/{$newFilePath}");

                    // Déplacer le fichier
                    if (file_exists($oldPath)) {
                        rename($oldPath, $newFullPath);
                        $newImages[] = $newFilePath;
                    } else {
                        $newImages[] = $image;
                    }
                } else {
                    $newImages[] = $image;
                }
            }

            // Mettre à jour l'enregistrement
            $record->{$imageField} = $newImages;
            $record->save();
        }
        // Si c'est une seule image
        else if (is_string($images) && strpos($images, "{$baseDir}/0/") === 0) {
            $filename = basename($images);
            $oldPath = public_path("images/{$images}");
            $newFilePath = "{$newPath}/{$filename}";
            $newFullPath = public_path("images/{$newFilePath}");

            // Déplacer le fichier
            if (file_exists($oldPath)) {
                rename($oldPath, $newFullPath);
                $record->{$imageField} = $newFilePath;
                $record->save();
            }
        }
    }
}
