<?php

namespace App\Filament\Resources\MarchandResource\Pages;

use App\Filament\Resources\MarchandResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use App\Filament\Resources\MarchandResource\Widgets\MarchandStatsOverview;
use App\Filament\Resources\MarchandResource\Widgets\MarchandSalesChart;
use App\Filament\Resources\MarchandResource\Widgets\MarchandOrdersChart;

class ViewMarchand extends ViewRecord
{
    protected static string $resource = MarchandResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('toggle_active')
                ->label(fn (): string => $this->record->user->is_active ? 'Désactiver' : 'Activer')
                ->icon(fn (): string => $this->record->user->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                ->color(fn (): string => $this->record->user->is_active ? 'danger' : 'success')
                ->requiresConfirmation()
                ->action(function (): void {
                    $user = $this->record->user;
                    $user->is_active = !$user->is_active;
                    $user->save();

                    $this->notify('success', $user->is_active ? 'Marchand activé avec succès' : 'Marchand désactivé avec succès');
                }),
        ];
    }

    public function getFooterWidgets(): array
    {
        return [
            MarchandStatsOverview::class,
            MarchandSalesChart::class,
            MarchandOrdersChart::class,
        ];
    }
}
