<?php

namespace App\Filament\Resources\MarchandResource\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Database\Eloquent\Model;
use App\Models\Produit;
use App\Models\Commande;
use App\Models\Paiement;
use Carbon\Carbon;

class MarchandStatsOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    public ?Model $record = null;

    protected function getStats(): array
    {
        // Récupérer le marchand
        $marchand = $this->record;

        if (!$marchand) {
            return [];
        }

        // Statistiques des produits
        $totalProduits = $marchand->produits()->count();
        $produitsEnStock = $marchand->produits()->where('stock', '>', 0)->count();
        $produitsEnRupture = $marchand->produits()->where('stock', '=', 0)->count();
        $produitsEnPromotion = $marchand->produits()->whereNotNull('discount_price')->count();

        // Statistiques des commandes
        $totalCommandes = $marchand->commandes()->count();
        $commandesEnAttente = $marchand->commandes()->where('statut', 'EnAttente')->count();
        $commandesEnCours = $marchand->commandes()->where('statut', 'EnCoursDeTraitement')->count();
        $commandesExpediees = $marchand->commandes()->where('statut', 'Expédié')->count();
        $commandesLivrees = $marchand->commandes()->where('statut', 'Livré')->count();
        $commandesAnnulees = $marchand->commandes()->where('statut', 'Annulé')->count();
        $commandesRemboursees = $marchand->commandes()->where('statut', 'Remboursé')->count();

        // Statistiques des paiements
        $totalPaiements = $marchand->paiements()->count();
        $paiementsCompletes = $marchand->paiements()->where('statut', 'Complété')->count();
        $montantTotalPaiements = $marchand->paiements()->where('statut', 'Complété')->sum('montant');

        // Statistiques des ventes récentes
        $ventesAujourdhui = $marchand->commandes()
            ->whereDate('created_at', Carbon::today())
            ->count();
        $ventesCetteSemaine = $marchand->commandes()
            ->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
            ->count();
        $ventesCeMois = $marchand->commandes()
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();

        // Montant des ventes récentes
        $montantVentesAujourdhui = $marchand->commandes()
            ->whereDate('created_at', Carbon::today())
            ->sum('montantTotal');
        $montantVentesCetteSemaine = $marchand->commandes()
            ->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
            ->sum('montantTotal');
        $montantVentesCeMois = $marchand->commandes()
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->sum('montantTotal');

        return [
            // Statistiques des produits
            Stat::make('Total des produits', $totalProduits)
                ->description('Nombre total de produits')
                ->descriptionIcon('heroicon-m-shopping-bag')
                ->color('primary'),
            Stat::make('Produits en stock', $produitsEnStock)
                ->description($totalProduits > 0 ? round(($produitsEnStock / $totalProduits) * 100) . '% des produits' : '0% des produits')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
            Stat::make('Produits en rupture', $produitsEnRupture)
                ->description($totalProduits > 0 ? round(($produitsEnRupture / $totalProduits) * 100) . '% des produits' : '0% des produits')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger'),

            // Statistiques des commandes
            Stat::make('Total des commandes', $totalCommandes)
                ->description('Nombre total de commandes')
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('primary'),
            Stat::make('Commandes en attente', $commandesEnAttente)
                ->description($totalCommandes > 0 ? round(($commandesEnAttente / $totalCommandes) * 100) . '% des commandes' : '0% des commandes')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),
            Stat::make('Commandes livrées', $commandesLivrees)
                ->description($totalCommandes > 0 ? round(($commandesLivrees / $totalCommandes) * 100) . '% des commandes' : '0% des commandes')
                ->descriptionIcon('heroicon-m-truck')
                ->color('success'),

            // Statistiques des paiements
            Stat::make('Total des paiements', number_format($montantTotalPaiements, 2) . ' €')
                ->description($totalPaiements . ' paiements complétés')
                ->descriptionIcon('heroicon-m-currency-euro')
                ->color('success'),
            Stat::make('Ventes aujourd\'hui', number_format($montantVentesAujourdhui, 2) . ' €')
                ->description($ventesAujourdhui . ' commandes')
                ->descriptionIcon('heroicon-m-calendar')
                ->color('primary'),
            Stat::make('Ventes ce mois', number_format($montantVentesCeMois, 2) . ' €')
                ->description($ventesCeMois . ' commandes')
                ->descriptionIcon('heroicon-m-calendar')
                ->color('primary'),
        ];
    }
}
