<?php

namespace App\Filament\Resources\MarchandResource\Widgets;

use Filament\Widgets\ChartWidget;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class MarchandSalesChart extends ChartWidget
{
    protected static ?string $heading = 'Évolution des ventes';

    protected static ?int $sort = 2;

    protected static ?string $maxHeight = '300px';

    protected static ?string $pollingInterval = null;

    public ?Model $record = null;

    protected function getData(): array
    {
        // Récupérer le marchand
        $marchand = $this->record;

        if (!$marchand) {
            return [];
        }

        // Récupérer les données des 30 derniers jours
        $startDate = Carbon::now()->subDays(30);
        $endDate = Carbon::now();

        // Récupérer les ventes par jour
        $sales = $marchand->commandes()
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(montantTotal) as total')
            )
            ->where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Préparer les données pour le graphique
        $dates = [];
        $totals = [];

        // Initialiser toutes les dates avec des valeurs à 0
        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $dateString = $currentDate->format('Y-m-d');
            $dates[] = $currentDate->format('d/m');
            $totals[$dateString] = 0;
            $currentDate->addDay();
        }

        // Remplir les données des ventes
        foreach ($sales as $sale) {
            $totals[$sale->date] = (float) $sale->total;
        }

        return [
            'labels' => $dates,
            'datasets' => [
                [
                    'label' => 'Ventes (€)',
                    'data' => array_values($totals),
                    'fill' => false,
                    'borderColor' => 'rgb(75, 192, 192)',
                    'tension' => 0.1,
                ],
            ],
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}
