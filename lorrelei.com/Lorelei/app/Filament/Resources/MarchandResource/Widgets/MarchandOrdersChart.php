<?php

namespace App\Filament\Resources\MarchandResource\Widgets;

use Filament\Widgets\ChartWidget;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class MarchandOrdersChart extends ChartWidget
{
    protected static ?string $heading = 'Répartition des commandes par statut';

    protected static ?int $sort = 3;

    protected static ?string $maxHeight = '300px';

    protected static ?string $pollingInterval = null;

    public ?Model $record = null;

    protected function getData(): array
    {
        // Récupérer le marchand
        $marchand = $this->record;

        if (!$marchand) {
            return [];
        }

        // Récupérer les commandes par statut
        $orders = $marchand->commandes()
            ->select('statut', DB::raw('count(*) as total'))
            ->groupBy('statut')
            ->get();

        // Préparer les données pour le graphique
        $labels = [];
        $data = [];
        $backgroundColor = [];

        // Définir les couleurs pour chaque statut
        $colors = [
            'EnAttente' => 'rgb(54, 162, 235)',
            'EnCoursDeTraitement' => 'rgb(255, 159, 64)',
            'Expédié' => 'rgb(75, 192, 192)',
            'Livré' => 'rgb(46, 204, 113)',
            'Annulé' => 'rgb(231, 76, 60)',
            'Remboursé' => 'rgb(155, 89, 182)',
        ];

        // Traduction des statuts
        $statusTranslations = [
            'EnAttente' => 'En attente',
            'EnCoursDeTraitement' => 'En cours de traitement',
            'Expédié' => 'Expédié',
            'Livré' => 'Livré',
            'Annulé' => 'Annulé',
            'Remboursé' => 'Remboursé',
        ];

        // Remplir les données
        foreach ($orders as $order) {
            $labels[] = $statusTranslations[$order->statut] ?? $order->statut;
            $data[] = $order->total;
            $backgroundColor[] = $colors[$order->statut] ?? 'rgb(201, 203, 207)';
        }

        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Commandes',
                    'data' => $data,
                    'backgroundColor' => $backgroundColor,
                ],
            ],
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
}
