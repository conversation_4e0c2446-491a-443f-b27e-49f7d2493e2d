<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProduitResource\Pages;
use App\Filament\Resources\ProduitResource\RelationManagers;
use App\Models\Produit;
use App\Models\Categorie;
use App\Models\Marchand;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class ProduitResource extends Resource
{
    protected static ?string $model = Produit::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';

    protected static ?string $navigationGroup = 'Catalogue';

    protected static ?int $navigationSort = 2;

    protected static ?string $recordTitleAttribute = 'nom';

    protected static ?string $recordRouteKeyName = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informations Générales')
                            ->schema([
                                Forms\Components\Select::make('marchand_id')
                                    ->label('Marchand')
                                    ->options(Marchand::all()->pluck('nomEntreprise', 'id'))
                                    ->required()
                                    ->searchable(),

                                Forms\Components\TextInput::make('nom')
                                    ->required()
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                        if ($operation === 'create' || $operation === 'edit') {
                                            $set('slug', \Illuminate\Support\Str::slug($state));
                                        }
                                    }),

                                Forms\Components\TextInput::make('slug')
                                    ->label('Slug')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true)
                                    ->dehydrated(fn ($state) => filled($state))
                                    ->afterStateHydrated(function (Forms\Get $get, Forms\Set $set, $state) {
                                        // Si le slug est vide, le générer à partir du nom
                                        if (blank($state) && filled($get('nom'))) {
                                            $set('slug', \Illuminate\Support\Str::slug($get('nom')));
                                        }
                                    }),

                                Forms\Components\Select::make('categorie_id')
                                    ->label('Catégorie')
                                    ->options(Categorie::all()->pluck('nom', 'id'))
                                    ->required()
                                    ->searchable(),

                                Forms\Components\Textarea::make('description')
                                    ->required()
                                    ->columnSpanFull(),
                            ]),

                        Forms\Components\Section::make('Prix et Stock')
                            ->schema([
                                Forms\Components\TextInput::make('prix')
                                    ->label('Prix (€)')
                                    ->numeric()
                                    ->required()
                                    ->prefix('€'),

                                Forms\Components\TextInput::make('discount_price')
                                    ->label('Prix promotionnel (€)')
                                    ->numeric()
                                    ->prefix('€'),

                                Forms\Components\DateTimePicker::make('discount_start_date')
                                    ->label('Début de la promotion'),

                                Forms\Components\DateTimePicker::make('discount_end_date')
                                    ->label('Fin de la promotion'),

                                Forms\Components\TextInput::make('stock')
                                    ->label('Stock disponible')
                                    ->numeric()
                                    ->required(),
                            ]),

                        Forms\Components\Section::make('Caractéristiques')
                            ->schema([
                                Forms\Components\TextInput::make('poids')
                                    ->label('Poids (kg)')
                                    ->numeric()
                                    ->suffix('kg'),

                                Forms\Components\KeyValue::make('dimensions')
                                    ->label('Dimensions')
                                    ->keyLabel('Dimension')
                                    ->valueLabel('Valeur')
                                    ->addButtonLabel('Ajouter une dimension'),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Images')
                            ->schema([
                                Forms\Components\FileUpload::make('images')
                                    ->label('Images du produit')
                                    ->multiple()
                                    ->image()
                                    ->imageEditor()
                                    ->disk('public_images')
                                    ->directory('products')
                                    ->visibility('public')
                                    ->maxFiles(5)
                                    ->reorderable(),
                            ]),

                        Forms\Components\Section::make('Statut')
                            ->schema([
                                Forms\Components\DateTimePicker::make('creeLe')
                                    ->label('Créé le')
                                    ->default(now()),

                                Forms\Components\DateTimePicker::make('misAJourLe')
                                    ->label('Mis à jour le')
                                    ->default(now()),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('images')
                    ->label('Image')
                    ->circular()
                    ->stacked()
                    ->disk('public_images')
                    ->limit(3),

                Tables\Columns\TextColumn::make('nom')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('slug')
                    ->label('Slug')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('marchand.nomEntreprise')
                    ->label('Marchand')
                    ->sortable(),

                Tables\Columns\TextColumn::make('categorie.nom')
                    ->label('Catégorie')
                    ->sortable(),

                Tables\Columns\TextColumn::make('prix')
                    ->money('EUR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('discount_price')
                    ->label('Prix promo')
                    ->money('EUR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('stock')
                    ->sortable(),

                Tables\Columns\TextColumn::make('creeLe')
                    ->label('Créé le')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('marchand_id')
                    ->label('Marchand')
                    ->options(Marchand::all()->pluck('nomEntreprise', 'id'))
                    ->searchable(),

                Tables\Filters\SelectFilter::make('categorie_id')
                    ->label('Catégorie')
                    ->options(Categorie::all()->pluck('nom', 'id'))
                    ->searchable(),

                Tables\Filters\Filter::make('en_promotion')
                    ->label('En promotion')
                    ->query(fn (Builder $query) => $query->whereNotNull('discount_price')),

                Tables\Filters\Filter::make('en_stock')
                    ->label('En stock')
                    ->query(fn (Builder $query) => $query->where('stock', '>', 0)),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProduits::route('/'),
            'create' => Pages\CreateProduit::route('/create'),
            'edit' => Pages\EditProduit::route('/{record}/edit'),
        ];
    }
}
