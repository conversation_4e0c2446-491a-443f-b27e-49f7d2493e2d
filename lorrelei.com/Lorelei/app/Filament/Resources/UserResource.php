<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Hash;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Gestion des Utilisateurs';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('Tabs')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Informations de base')
                            ->schema([
                                Forms\Components\Section::make('Informations de l\'utilisateur')
                                    ->schema([
                                        Forms\Components\TextInput::make('name')
                                            ->label('Nom')
                                            ->required()
                                            ->maxLength(255),

                                        Forms\Components\TextInput::make('email')
                                            ->email()
                                            ->required()
                                            ->maxLength(255)
                                            ->unique(ignoreRecord: true),

                                        Forms\Components\TextInput::make('password')
                                            ->password()
                                            ->dehydrateStateUsing(fn (string $state): string => Hash::make($state))
                                            ->dehydrated(fn (?string $state): bool => filled($state))
                                            ->required(fn (string $operation): bool => $operation === 'create'),

                                        Forms\Components\Select::make('role')
                                            ->options([
                                                'Client' => 'Client',
                                                'Marchand' => 'Marchand',
                                                'Admin' => 'Admin',
                                            ])
                                            ->required(),

                                        Forms\Components\Toggle::make('is_active')
                                            ->label('Actif')
                                            ->default(true),

                                        Forms\Components\DateTimePicker::make('last_login_at')
                                            ->label('Dernière connexion')
                                            ->disabled(),
                                    ]),
                            ]),

                        Forms\Components\Tabs\Tab::make('Informations client')
                            ->schema([
                                Forms\Components\Section::make('Détails du client')
                                    ->schema([
                                        Forms\Components\TextInput::make('client.prenom')
                                            ->label('Prénom'),

                                        Forms\Components\TextInput::make('client.nom')
                                            ->label('Nom'),

                                        Forms\Components\TextInput::make('client.telephone')
                                            ->label('Téléphone')
                                            ->tel(),

                                        Forms\Components\DatePicker::make('client.dateDeNaissance')
                                            ->label('Date de naissance'),
                                    ])
                                    ->visible(fn (\App\Models\User $record): bool => $record->role === 'Client'),
                            ])
                            ->visible(fn (\App\Models\User $record): bool => $record->role === 'Client'),

                        Forms\Components\Tabs\Tab::make('Informations marchand')
                            ->schema([
                                Forms\Components\Section::make('Détails du marchand')
                                    ->schema([
                                        Forms\Components\TextInput::make('marchand.nomEntreprise')
                                            ->label('Nom de l\'entreprise'),

                                        Forms\Components\TextInput::make('marchand.siret')
                                            ->label('SIRET'),

                                        Forms\Components\TextInput::make('marchand.telephone')
                                            ->label('Téléphone')
                                            ->tel(),

                                        Forms\Components\TextInput::make('marchand.siteWeb')
                                            ->label('Site web')
                                            ->url(),
                                    ])
                                    ->visible(fn (\App\Models\User $record): bool => $record->role === 'Marchand'),
                            ])
                            ->visible(fn (\App\Models\User $record): bool => $record->role === 'Marchand'),

                        Forms\Components\Tabs\Tab::make('Adresses')
                            ->schema([
                                Forms\Components\Section::make('Adresses de l\'utilisateur')
                                    ->schema([
                                        Forms\Components\Repeater::make('adresses')
                                            ->relationship('adresses')
                                            ->schema([
                                                Forms\Components\Select::make('type')
                                                    ->label('Type')
                                                    ->options([
                                                        'Livraison' => 'Livraison',
                                                        'Facturation' => 'Facturation',
                                                        'Entreprise' => 'Entreprise',
                                                    ])
                                                    ->required(),

                                                Forms\Components\Textarea::make('rue')
                                                    ->label('Rue')
                                                    ->required(),

                                                Forms\Components\Grid::make(3)
                                                    ->schema([
                                                        Forms\Components\TextInput::make('codePostal')
                                                            ->label('Code postal')
                                                            ->required(),

                                                        Forms\Components\TextInput::make('ville')
                                                            ->label('Ville')
                                                            ->required(),

                                                        Forms\Components\TextInput::make('etat')
                                                            ->label('État/Province')
                                                            ->required(),
                                                    ]),

                                                Forms\Components\TextInput::make('pays')
                                                    ->label('Pays')
                                                    ->required(),
                                            ])
                                            ->columns(1)
                                            ->itemLabel(fn (array $state): ?string => $state['type'] . ' - ' . ($state['ville'] ?? ''))
                                    ])
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),

                Tables\Columns\TextColumn::make('role')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Admin' => 'danger',
                        'Marchand' => 'warning',
                        'Client' => 'success',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Actif')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('last_login_at')
                    ->label('Dernière connexion')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->options([
                        'Client' => 'Client',
                        'Marchand' => 'Marchand',
                        'Admin' => 'Admin',
                    ]),

                Tables\Filters\Filter::make('is_active')
                    ->label('Utilisateurs actifs')
                    ->query(fn ($query) => $query->where('is_active', true)),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('toggle_active')
                    ->label(fn (User $record): string => $record->is_active ? 'Désactiver' : 'Activer')
                    ->icon(fn (User $record): string => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn (User $record): string => $record->is_active ? 'danger' : 'success')
                    ->action(function (User $record): void {
                        $record->update([
                            'is_active' => !$record->is_active,
                        ]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activer')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(fn (array $records) => User::whereIn('id', $records)->update(['is_active' => true])),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Désactiver')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(fn (array $records) => User::whereIn('id', $records)->update(['is_active' => false])),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // Utilisons une approche différente pour les relations
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
