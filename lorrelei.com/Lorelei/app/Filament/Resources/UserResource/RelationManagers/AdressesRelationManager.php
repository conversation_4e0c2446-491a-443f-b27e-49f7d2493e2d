<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class AdressesRelationManager extends RelationManager
{
    protected static string $relationship = 'adresses';

    protected static ?string $recordTitleAttribute = 'ville';

    protected static ?string $title = 'Adresses';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('type')
                    ->label('Type')
                    ->options([
                        'Livraison' => 'Livraison',
                        'Facturation' => 'Facturation',
                        'Entreprise' => 'Entreprise',
                    ])
                    ->required(),
                Forms\Components\Textarea::make('rue')
                    ->label('Rue')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('codePostal')
                    ->label('Code postal')
                    ->required()
                    ->maxLength(20),
                Forms\Components\TextInput::make('ville')
                    ->label('Ville')
                    ->required()
                    ->maxLength(100),
                Forms\Components\TextInput::make('etat')
                    ->label('État/Province')
                    ->required()
                    ->maxLength(100),
                Forms\Components\TextInput::make('pays')
                    ->label('Pays')
                    ->required()
                    ->maxLength(100),
                Forms\Components\Hidden::make('user_id')
                    ->default(fn () => auth()->id()),
            ])
            ->mutateFormDataBeforeCreate(function (array $data): array {
                // S'assurer que toutes les données sont des chaînes de caractères
                return [
                    'type' => (string) ($data['type'] ?? ''),
                    'rue' => (string) ($data['rue'] ?? ''),
                    'codePostal' => (string) ($data['codePostal'] ?? ''),
                    'ville' => (string) ($data['ville'] ?? ''),
                    'etat' => (string) ($data['etat'] ?? ''),
                    'pays' => (string) ($data['pays'] ?? ''),
                    'user_id' => (int) ($data['user_id'] ?? auth()->id()),
                ];
            })
            ->mutateFormDataBeforeSave(function (array $data): array {
                // S'assurer que toutes les données sont des chaînes de caractères
                return [
                    'type' => (string) ($data['type'] ?? ''),
                    'rue' => (string) ($data['rue'] ?? ''),
                    'codePostal' => (string) ($data['codePostal'] ?? ''),
                    'ville' => (string) ($data['ville'] ?? ''),
                    'etat' => (string) ($data['etat'] ?? ''),
                    'pays' => (string) ($data['pays'] ?? ''),
                    'user_id' => (int) ($data['user_id'] ?? auth()->id()),
                ];
            });
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Livraison' => 'success',
                        'Facturation' => 'warning',
                        'Entreprise' => 'info',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('rue')
                    ->label('Rue')
                    ->limit(30),
                Tables\Columns\TextColumn::make('codePostal')
                    ->label('Code postal'),
                Tables\Columns\TextColumn::make('ville')
                    ->label('Ville'),
                Tables\Columns\TextColumn::make('pays')
                    ->label('Pays'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
