<?php

namespace App\Filament\Resources\ProduitResource\Pages;

use App\Filament\Resources\ProduitResource;
use App\Filament\Traits\HandlesImageStorage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\CreateRecord;
use App\Models\Categorie;
use App\Models\Currency;
use App\Models\Marchand;
use Filament\Forms\Components\Tabs;

class CreateProduit extends CreateRecord
{
    use HandlesImageStorage;
    protected static string $resource = ProduitResource::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema($this->getFormSchema());
    }

    protected function getFormSchema(): array
    {
        return [
            Forms\Components\Group::make()
                ->schema([
                    Forms\Components\Section::make('Informations Générales')
                        ->schema([
                            Forms\Components\Select::make('marchand_id')
                                ->label('Marchand')
                                ->options(Marchand::all()->pluck('nomEntreprise', 'id'))
                                ->required()
                                ->searchable(),

                            Tabs::make('Traductions')
                                ->tabs([
                                    Tabs\Tab::make('Français')
                                        ->icon('heroicon-m-flag')
                                        ->schema([
                                            Forms\Components\TextInput::make('nom.fr')
                                                ->label('Nom du produit (FR)')
                                                ->required()
                                                ->maxLength(255)
                                                ->live(onBlur: true)
                                                ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                                    if ($operation === 'create' || $operation === 'edit') {
                                                        $set('slug', \Illuminate\Support\Str::slug($state));
                                                    }
                                                }),

                                            Forms\Components\Textarea::make('description.fr')
                                                ->label('Description (FR)')
                                                ->required()
                                                ->rows(5),
                                        ]),

                                    Tabs\Tab::make('English')
                                        ->icon('heroicon-m-flag')
                                        ->schema([
                                            Forms\Components\TextInput::make('nom.en')
                                                ->label('Nom du produit (EN)')
                                                ->maxLength(255),

                                            Forms\Components\Textarea::make('description.en')
                                                ->label('Description (EN)')
                                                ->rows(5),
                                        ]),
                                ])
                                ->columnSpanFull(),

                            Forms\Components\TextInput::make('slug')
                                ->label('Slug')
                                ->required()
                                ->maxLength(255)
                                ->unique(ignoreRecord: true)
                                ->dehydrated(fn ($state) => filled($state))
                                ->afterStateHydrated(function (Forms\Get $get, Forms\Set $set, $state) {
                                    // Si le slug est vide, le générer à partir du nom en français
                                    if (blank($state) && filled($get('nom.fr'))) {
                                        $set('slug', \Illuminate\Support\Str::slug($get('nom.fr')));
                                    }
                                }),

                            Forms\Components\TextInput::make('product_code')
                                ->label('Code produit (Product code)')
                                ->helperText('Code EAN ou référence du produit (optionnel)')
                                ->maxLength(255),

                            Forms\Components\TextInput::make('marque')
                                ->label('Marque (Brand)')
                                ->helperText('Marque du produit (optionnel)')
                                ->maxLength(255),

                            Forms\Components\Select::make('categorie_id')
                                ->label('Catégorie (Category)')
                                ->options(Categorie::all()->pluck('nom', 'id'))
                                ->required()
                                ->searchable(),
                        ]),

                    Forms\Components\Section::make('Prix et Stock')
                        ->schema([
                            Forms\Components\Grid::make()
                                ->schema([
                                    Forms\Components\TextInput::make('prix')
                                        ->label('Prix (Price)')
                                        ->numeric()
                                        ->required(),

                                    Forms\Components\Select::make('currency')
                                        ->label('Devise (Currency)')
                                        ->options(Currency::where('is_active', true)->pluck('name', 'code'))
                                        ->default('FCFA')
                                        ->required(),
                                ])
                                ->columns(2),

                            Forms\Components\Grid::make()
                                ->schema([
                                    Forms\Components\TextInput::make('discount_price')
                                        ->label('Prix promotionnel (Discount price)')
                                        ->numeric(),

                                    Forms\Components\Placeholder::make('currency_placeholder')
                                        ->label('Devise (Currency)')
                                        ->content(fn (Forms\Get $get): string => $get('currency') ?: 'FCFA'),
                                ])
                                ->columns(2),

                            Forms\Components\DateTimePicker::make('discount_start_date')
                                ->label('Début de la promotion (Discount start)'),

                            Forms\Components\DateTimePicker::make('discount_end_date')
                                ->label('Fin de la promotion (Discount end)'),

                            Forms\Components\TextInput::make('stock')
                                ->label('Stock disponible (Available stock)')
                                ->numeric()
                                ->required(),
                        ]),

                    Forms\Components\Section::make('Caractéristiques')
                        ->schema([
                            Forms\Components\TextInput::make('poids')
                                ->label('Poids (Weight in kg)')
                                ->numeric()
                                ->suffix('kg'),

                            Forms\Components\Repeater::make('attributs')
                                ->label('Attributs du produit')
                                ->schema([
                                    // ... (contenu existant pour les attributs)
                                ])
                                ->itemLabel(function (array $state): ?string {
                                    $type = $state['type'] ?? null;

                                    if ($type === 'couleur' && isset($state['nom'])) {
                                        return ucfirst($type) . ': ' . $state['nom'];
                                    }

                                    if (isset($state['valeur'])) {
                                        return ucfirst($type) . ': ' . $state['valeur'];
                                    }

                                    return ucfirst($type ?? 'Attribut');
                                })
                                ->collapsible()
                                ->collapsed(false)
                                ->addActionLabel('Ajouter un attribut (Add attribute)')
                                ->reorderable()
                                ->columnSpanFull()
                                ->helperText('Ajoutez des attributs comme la couleur, la taille, le matériau, etc. Pour les couleurs, vous pourrez sélectionner une couleur dans la palette.'),
                        ]),

                    // Section des variantes au niveau principal
                    Forms\Components\Section::make('Variantes du produit')
                        ->description('Les variantes sont optionnelles. Vous pouvez créer le produit sans variante et les ajouter plus tard.')
                        ->schema([
                            // ... (contenu existant pour les variantes)
                        ]),
                ])
                ->columnSpan(['lg' => 2]),

            Forms\Components\Group::make()
                ->schema([
                    Forms\Components\Section::make('Images')
                        ->schema([
                            // ... (contenu existant pour les images)
                        ]),

                    Forms\Components\Section::make('Statut')
                        ->schema([
                            Forms\Components\DateTimePicker::make('creeLe')
                                ->label('Créé le')
                                ->default(now()),

                            Forms\Components\DateTimePicker::make('misAJourLe')
                                ->label('Mis à jour le')
                                ->default(now()),
                        ]),
                ])
                ->columnSpan(['lg' => 1]),
        ];
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Définir la date de création
        $data['creeLe'] = now();

        return $data;
    }

    protected function afterCreate(): void
    {
        // Déplacer les images du dossier temporaire vers le dossier basé sur l'ID
        self::moveImagesAfterCreate($this->record, 'products');

        // Créer les variantes si elles ont été définies
        $this->createVariants();
    }

    /**
     * Crée les variantes du produit à partir des données du formulaire
     */
    protected function createVariants(): void
    {
        // Récupérer les données du formulaire
        $data = $this->data;

        // Vérifier si des variantes ont été définies
        if (!isset($data['variants']) || !is_array($data['variants']) || empty($data['variants'])) {
            return;
        }

        // Créer chaque variante
        foreach ($data['variants'] as $variantData) {
            // Créer la variante
            $variant = $this->record->variants()->create([
                'sku' => $variantData['sku'] ?? null,
                'prix_supplement' => $variantData['prix_supplement'] ?? 0,
                'stock' => $variantData['stock'] ?? 0,
                'attributs' => $variantData['attributs'] ?? [],
                'images' => [], // Les images seront traitées séparément
            ]);

            // Traiter les images si elles existent
            if (isset($variantData['images']) && is_array($variantData['images']) && !empty($variantData['images'])) {
                // Déplacer les images du dossier temporaire vers le dossier basé sur l'ID
                $variantImagesPath = "variants/{$variant->id}";
                $publicPath = public_path('images');
                $variantPublicPath = "{$publicPath}/{$variantImagesPath}";

                // Créer le dossier s'il n'existe pas
                if (!file_exists($variantPublicPath)) {
                    mkdir($variantPublicPath, 0755, true);
                }

                $processedImages = [];

                foreach ($variantData['images'] as $image) {
                    $tempPath = public_path("storage/{$image}");

                    if (file_exists($tempPath)) {
                        // Générer un nom de fichier unique
                        $filename = uniqid() . '_' . basename($image);
                        $targetPath = "{$variantPublicPath}/{$filename}";

                        // Copier l'image
                        copy($tempPath, $targetPath);

                        // Ajouter le chemin relatif à la liste des images traitées
                        $processedImages[] = "{$variantImagesPath}/{$filename}";
                    }
                }

                // Mettre à jour la variante avec les chemins des images
                $variant->update(['images' => $processedImages]);
            }
        }

        // Afficher une notification de succès
        \Filament\Notifications\Notification::make()
            ->title('Variantes créées')
            ->body('Les variantes du produit ont été créées avec succès.')
            ->success()
            ->send();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
