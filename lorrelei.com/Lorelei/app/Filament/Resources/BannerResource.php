<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BannerResource\Pages;
use App\Models\Banner;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class BannerResource extends Resource
{
    protected static ?string $model = Banner::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationGroup = 'Marketing';

    protected static ?int $navigationSort = 1;

    protected static ?string $recordTitleAttribute = 'position';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informations de la bannière')
                            ->schema([
                                Forms\Components\TextInput::make('position')
                                    ->label('Position')
                                    ->required()
                                    ->maxLength(100)
                                    ->helperText('Ex: accueil, categorie-vetements'),

                                Forms\Components\TextInput::make('target_url')
                                    ->label('URL de destination')
                                    ->url()
                                    ->maxLength(255),

                                Forms\Components\DateTimePicker::make('start_date')
                                    ->label('Date de début'),

                                Forms\Components\DateTimePicker::make('end_date')
                                    ->label('Date de fin'),

                                Forms\Components\TextInput::make('priorite')
                                    ->label('Priorité')
                                    ->numeric()
                                    ->default(0)
                                    ->helperText('Plus la valeur est élevée, plus la bannière sera prioritaire'),

                                Forms\Components\Toggle::make('is_active')
                                    ->label('Actif')
                                    ->default(true),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Image')
                            ->schema([
                                Forms\Components\FileUpload::make('image_url')
                                    ->label('Image')
                                    ->image()
                                    ->imageEditor()
                                    ->disk('public_images')
                                    ->directory('banners')
                                    ->visibility('public')
                                    ->required(),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image_url')
                    ->label('Image')
                    ->disk('public_images'),

                Tables\Columns\TextColumn::make('position')
                    ->label('Position')
                    ->searchable(),

                Tables\Columns\TextColumn::make('target_url')
                    ->label('URL de destination')
                    ->limit(30),

                Tables\Columns\TextColumn::make('start_date')
                    ->label('Début')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('end_date')
                    ->label('Fin')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('priorite')
                    ->label('Priorité')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Actif')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\Filter::make('active')
                    ->label('Bannières actives')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true)),

                Tables\Filters\Filter::make('current')
                    ->label('Bannières en cours')
                    ->query(function (Builder $query): Builder {
                        return $query
                            ->where('is_active', true)
                            ->where(function (Builder $query) {
                                $query->whereNull('start_date')
                                    ->orWhere('start_date', '<=', now());
                            })
                            ->where(function (Builder $query) {
                                $query->whereNull('end_date')
                                    ->orWhere('end_date', '>=', now());
                            });
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activer')
                        ->icon('heroicon-o-check-circle')
                        ->action(fn (array $records) => Banner::whereIn('id', $records)->update(['is_active' => true])),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Désactiver')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(fn (array $records) => Banner::whereIn('id', $records)->update(['is_active' => false])),
                ]),
            ])
            ->defaultSort('priorite', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBanners::route('/'),
            'create' => Pages\CreateBanner::route('/create'),
            'edit' => Pages\EditBanner::route('/{record}/edit'),
        ];
    }
}
