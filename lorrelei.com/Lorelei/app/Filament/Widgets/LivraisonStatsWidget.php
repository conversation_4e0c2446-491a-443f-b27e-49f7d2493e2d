<?php

namespace App\Filament\Widgets;

use App\Models\MarchandZoneLivraison;
use App\Models\ProduitZoneLivraison;
use App\Models\ZoneLivraison;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class LivraisonStatsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '60s';

    protected int|string|array $columnSpan = 'full';

    protected function getStats(): array
    {
        // Nombre total de zones de livraison
        $totalZones = ZoneLivraison::count();
        $activeZones = ZoneLivraison::where('actif', true)->count();
        $zonesByType = ZoneLivraison::selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        // Nombre de marchands avec des zones de livraison
        $marchandsWithZones = MarchandZoneLivraison::distinct('marchand_id')->count('marchand_id');
        $totalMarchandZones = MarchandZoneLivraison::count();
        $activeMarchandZones = MarchandZoneLivraison::where('actif', true)->count();

        // Nombre de produits avec des zones de livraison spécifiques
        $produitsWithZones = ProduitZoneLivraison::distinct('produit_id')->count('produit_id');
        $totalProduitZones = ProduitZoneLivraison::count();
        $activeProduitZones = ProduitZoneLivraison::where('actif', true)->count();

        return [
            Stat::make('Zones de livraison', $totalZones)
                ->description($activeZones . ' zones actives')
                ->descriptionIcon('heroicon-m-map')
                ->chart([
                    $zonesByType['Pays'] ?? 0,
                    $zonesByType['Region'] ?? 0,
                    $zonesByType['Ville'] ?? 0,
                    $zonesByType['Quartier'] ?? 0,
                ])
                ->color('success'),

            Stat::make('Marchands avec zones', $marchandsWithZones)
                ->description($activeMarchandZones . ' zones actives sur ' . $totalMarchandZones)
                ->descriptionIcon('heroicon-m-building-storefront')
                ->chart([
                    $activeMarchandZones,
                    $totalMarchandZones - $activeMarchandZones,
                ])
                ->color('warning'),

            Stat::make('Produits avec zones spécifiques', $produitsWithZones)
                ->description($activeProduitZones . ' associations actives sur ' . $totalProduitZones)
                ->descriptionIcon('heroicon-m-shopping-bag')
                ->chart([
                    $activeProduitZones,
                    $totalProduitZones - $activeProduitZones,
                ])
                ->color('danger'),
        ];
    }
}
