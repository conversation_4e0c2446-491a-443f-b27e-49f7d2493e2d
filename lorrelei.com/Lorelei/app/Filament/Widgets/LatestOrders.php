<?php

namespace App\Filament\Widgets;

use App\Models\Commande;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class LatestOrders extends BaseWidget
{
    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 2;

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Commande::query()
                    ->latest('creeLe')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->searchable(),

                Tables\Columns\TextColumn::make('client.nom')
                    ->label('Client')
                    ->formatStateUsing(fn (Commande $record): string =>
                        $record->client->prenom . ' ' . $record->client->nom
                    )
                    ->searchable(),

                Tables\Columns\TextColumn::make('marchand.nomEntreprise')
                    ->label('Marchand')
                    ->searchable(),

                Tables\Columns\TextColumn::make('montantTotal')
                    ->label('Montant')
                    ->money('EUR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('statut')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'EnAttente' => 'gray',
                        'EnCoursDeTraitement' => 'warning',
                        'Expédié' => 'info',
                        'Livré' => 'success',
                        'Annulé' => 'danger',
                        'Remboursé' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'EnAttente' => 'En attente',
                        'EnCoursDeTraitement' => 'En traitement',
                        'Expédié' => 'Expédié',
                        'Livré' => 'Livré',
                        'Annulé' => 'Annulé',
                        'Remboursé' => 'Remboursé',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('creeLe')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('Voir')
                    ->url(fn (Commande $record): string => route('filament.admin.resources.commandes.edit', ['record' => $record]))
                    ->icon('heroicon-m-eye'),
            ]);
    }
}
