<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class FilamentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['Admin', 'Marchand']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, $model): bool
    {
        // Admin peut tout voir
        if ($user->role === 'Admin') {
            return true;
        }

        // Marchand ne peut voir que ses propres ressources
        if ($user->role === 'Marchand') {
            // Si le modèle a un champ marchand_id, vérifier qu'il appartient au marchand
            if (property_exists($model, 'marchand_id')) {
                $marchandId = $user->marchands->first()->id ?? null;
                return $model->marchand_id === $marchandId;
            }
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['Admin', 'Marchand']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, $model): bool
    {
        // Admin peut tout modifier
        if ($user->role === 'Admin') {
            return true;
        }

        // Marchand ne peut modifier que ses propres ressources
        if ($user->role === 'Marchand') {
            // Si le modèle a un champ marchand_id, vérifier qu'il appartient au marchand
            if (property_exists($model, 'marchand_id')) {
                $marchandId = $user->marchands->first()->id ?? null;
                return $model->marchand_id === $marchandId;
            }
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, $model): bool
    {
        // Admin peut tout supprimer
        if ($user->role === 'Admin') {
            return true;
        }

        // Marchand ne peut supprimer que ses propres ressources
        if ($user->role === 'Marchand') {
            // Si le modèle a un champ marchand_id, vérifier qu'il appartient au marchand
            if (property_exists($model, 'marchand_id')) {
                $marchandId = $user->marchands->first()->id ?? null;
                return $model->marchand_id === $marchandId;
            }
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, $model): bool
    {
        return $user->role === 'Admin';
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, $model): bool
    {
        return $user->role === 'Admin';
    }
}
