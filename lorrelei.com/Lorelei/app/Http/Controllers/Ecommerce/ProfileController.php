<?php

namespace App\Http\Controllers\Ecommerce;

use App\Http\Controllers\Controller;
use App\Models\Adresse;
use App\Models\Client;
use App\Models\Commande;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ProfileController extends Controller
{
    /**
     * Affiche la page de profil de l'utilisateur
     */
    public function show(Request $request)
    {
        $user = $request->user();

        // Récupérer les informations du client si l'utilisateur est un client
        $client = null;
        $adresses = [];
        $commandes = [];

        if ($user->role === 'Client') {
            // Vérifier si un enregistrement client existe, sinon le créer
            $client = Client::where('user_id', $user->id)->with(['adresse', 'user'])->first();

            if (!$client) {
                // Créer un nouvel enregistrement client
                $nameParts = explode(' ', $user->name, 2);
                $prenom = $nameParts[0] ?? '';
                $nom = $nameParts[1] ?? '';

                $client = new Client([
                    'user_id' => $user->id,
                    'prenom' => $prenom,
                    'nom' => $nom,
                ]);
                $client->save();

                // Recharger le client avec ses relations adresse et user
                $client = Client::where('user_id', $user->id)->with(['adresse', 'user'])->first();
            }

            // Récupérer toutes les adresses de l'utilisateur
            // Nous devons créer une table pivot ou utiliser une autre approche
            // Pour l'instant, récupérons les adresses créées par l'utilisateur
            $adresses = Adresse::where('user_id', $user->id)->get();

            // Récupérer les commandes de l'utilisateur
            if ($client->id) {
                $commandes = Commande::where('client_id', $client->id)
                    ->with(['articleCommandes.produit', 'adresse'])
                    ->orderBy('creeLe', 'desc')
                    ->get();
            }
        }

        // Créer un objet client avec les informations de base de l'utilisateur si aucun client n'existe
        if (!$client) {
            $client = (object) [
                'user' => $user,
                'prenom' => '',
                'nom' => '',
                'telephone' => '',
                'dateDeNaissance' => null,
            ];
        }

        return Inertia::render('ecommerce/profile', [
            'client' => $client,
            'adresses' => $adresses,
            'commandes' => $commandes,
        ]);
    }

    /**
     * Met à jour les informations personnelles de l'utilisateur
     */
    public function updatePersonalInfo(Request $request)
    {
        $user = $request->user();

        // Valider les données
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'prenom' => ['required', 'string', 'max:100'],
            'nom' => ['required', 'string', 'max:100'],
            'telephone' => ['nullable', 'string', 'max:20'],
            'dateDeNaissance' => ['nullable', 'date'],
        ]);

        // Mettre à jour l'utilisateur
        $user->name = $validated['name'];
        $user->email = $validated['email'];
        $user->save();

        // Mettre à jour ou créer le client
        if ($user->role === 'Client') {
            $client = Client::where('user_id', $user->id)->first();

            if (!$client) {
                // Créer un nouvel enregistrement client
                $client = new Client([
                    'user_id' => $user->id,
                ]);
            }

            // Mettre à jour les informations du client
            $client->prenom = $validated['prenom'];
            $client->nom = $validated['nom'];
            $client->telephone = $validated['telephone'];
            $client->dateDeNaissance = $validated['dateDeNaissance'];
            $client->save();
        }

        return redirect()->back()->with('success', 'Informations personnelles mises à jour avec succès.');
    }

    /**
     * Ajoute une nouvelle adresse pour l'utilisateur
     */
    public function addAddress(Request $request)
    {
        $user = $request->user();

        // Déboguer les données reçues
        \Log::info('Données d\'adresse reçues:', $request->all());

        // Valider les données
        $validated = $request->validate([
            'rue' => ['required', 'string', 'max:255'],
            'ville' => ['required', 'string', 'max:100'],
            'etat' => ['required', 'string', 'max:100'],
            'pays' => ['required', 'string', 'max:100'],
            'codePostal' => ['required', 'string', 'max:20'],
            'type' => ['required', 'in:Livraison,Facturation,Entreprise'],
        ]);

        // S'assurer que toutes les données sont des chaînes de caractères
        $data = [
            'rue' => (string) $validated['rue'],
            'ville' => (string) $validated['ville'],
            'etat' => (string) $validated['etat'],
            'pays' => (string) $validated['pays'],
            'codePostal' => (string) $validated['codePostal'],
            'type' => (string) $validated['type'],
        ];

        // Déboguer les données après conversion
        \Log::info('Données d\'adresse après conversion:', $data);

        try {
            // Créer l'adresse manuellement pour éviter les problèmes de type
            $adresse = new Adresse();
            $adresse->rue = (string) $data['rue'];
            $adresse->ville = (string) $data['ville'];
            $adresse->etat = (string) $data['etat'];
            $adresse->pays = (string) $data['pays'];
            $adresse->codePostal = (string) $data['codePostal'];
            $adresse->type = (string) $data['type'];
            $adresse->user_id = (int) $user->id; // Associer l'adresse à l'utilisateur
            $adresse->save();

            \Log::info('Adresse créée avec succès:', ['id' => $adresse->id]);

            // Message flash de succès
            session()->flash('success', 'Adresse ajoutée avec succès');
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la création de l\'adresse:', ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);

            // Message flash d'erreur
            session()->flash('error', 'Erreur lors de la création de l\'adresse: ' . $e->getMessage());
            return redirect()->back();
        }

        // Associer l'adresse au client
        if ($user->role === 'Client') {
            $client = Client::where('user_id', $user->id)->first();

            if ($client) {
                // Si c'est la première adresse, la définir comme adresse principale
                if (!$client->adresse_id) {
                    $client->adresse_id = $adresse->id;
                    $client->save();
                }
            }
        }

        return redirect()->back()->with('success', 'Adresse ajoutée avec succès.');
    }

    /**
     * Met à jour une adresse existante
     */
    public function updateAddress(Request $request, $id)
    {
        // Déboguer les données reçues
        \Log::info('Données de mise à jour d\'adresse reçues:', $request->all());

        // Valider les données
        $validated = $request->validate([
            'rue' => ['required', 'string', 'max:255'],
            'ville' => ['required', 'string', 'max:100'],
            'etat' => ['required', 'string', 'max:100'],
            'pays' => ['required', 'string', 'max:100'],
            'codePostal' => ['required', 'string', 'max:20'],
            'type' => ['required', 'in:Livraison,Facturation,Entreprise'],
        ]);

        // S'assurer que toutes les données sont des chaînes de caractères
        $data = [
            'rue' => (string) $validated['rue'],
            'ville' => (string) $validated['ville'],
            'etat' => (string) $validated['etat'],
            'pays' => (string) $validated['pays'],
            'codePostal' => (string) $validated['codePostal'],
            'type' => (string) $validated['type'],
        ];

        // Déboguer les données après conversion
        \Log::info('Données de mise à jour d\'adresse après conversion:', $data);

        try {
            // Mettre à jour l'adresse manuellement pour éviter les problèmes de type
            $adresse = Adresse::findOrFail($id);
            $adresse->rue = (string) $data['rue'];
            $adresse->ville = (string) $data['ville'];
            $adresse->etat = (string) $data['etat'];
            $adresse->pays = (string) $data['pays'];
            $adresse->codePostal = (string) $data['codePostal'];
            $adresse->type = (string) $data['type'];
            $adresse->save();

            \Log::info('Adresse mise à jour avec succès:', ['id' => $adresse->id]);

            // Message flash de succès
            session()->flash('success', 'Adresse mise à jour avec succès');
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la mise à jour de l\'adresse:', ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);

            // Message flash d'erreur
            session()->flash('error', 'Erreur lors de la mise à jour de l\'adresse: ' . $e->getMessage());
            return redirect()->back();
        }

        return redirect()->back();
    }

    /**
     * Supprime une adresse
     */
    public function deleteAddress(Request $request, $id)
    {
        $user = $request->user();
        $adresse = Adresse::findOrFail($id);

        // Vérifier si l'adresse est utilisée comme adresse principale
        if ($user->role === 'Client') {
            $client = Client::where('user_id', $user->id)->first();

            if ($client && $client->adresse_id === $adresse->id) {
                // Trouver une autre adresse à utiliser comme adresse principale
                $autreAdresse = Adresse::whereHas('clients', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                })->where('id', '!=', $id)->first();

                if ($autreAdresse) {
                    $client->adresse_id = $autreAdresse->id;
                } else {
                    $client->adresse_id = null;
                }

                $client->save();
            }
        }

        // Supprimer l'adresse
        $adresse->delete();

        // Message flash de succès
        session()->flash('success', 'Adresse supprimée avec succès');
        return redirect()->back();
    }

    /**
     * Définit une adresse comme adresse principale
     */
    public function setDefaultAddress(Request $request, $id)
    {
        $user = $request->user();

        if ($user->role === 'Client') {
            $client = Client::where('user_id', $user->id)->first();

            if ($client) {
                $client->adresse_id = $id;
                $client->save();
            }
        }

        // Message flash de succès
        session()->flash('success', 'Adresse principale définie avec succès');
        return redirect()->back();
    }
}
