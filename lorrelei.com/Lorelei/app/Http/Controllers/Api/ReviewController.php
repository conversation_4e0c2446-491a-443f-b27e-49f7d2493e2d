<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Produit;
use App\Models\Review;
use App\Models\ReviewVote;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ReviewController extends Controller
{
    /**
     * Récupère les avis d'un produit
     *
     * @param string $productId
     * @return JsonResponse
     */
    public function getProductReviews(Request $request, string $productId): JsonResponse
    {
        $perPage = $request->input('per_page', 5); // Par défaut 5 avis par page
        $perPage = max(2, min(10, (int)$perPage)); // Limiter entre 2 et 10

        $sortBy = $request->input('sort_by', 'created_at'); // Par défaut tri par date de création
        $sortOrder = $request->input('sort_order', 'desc'); // Par défaut ordre décroissant
        $filterRating = $request->input('rating'); // Filtrer par note (optionnel)
        $withImages = $request->input('with_images'); // Filtrer les avis avec images (optionnel)

        // Valider les paramètres de tri
        $validSortFields = ['created_at', 'rating', 'likes', 'dislikes'];
        if (!in_array($sortBy, $validSortFields)) {
            $sortBy = 'created_at';
        }

        $validSortOrders = ['asc', 'desc'];
        if (!in_array($sortOrder, $validSortOrders)) {
            $sortOrder = 'desc';
        }

        $query = Review::where('produit_id', $productId)
            ->where('is_approved', true)
            ->with('user:id,name');

        // Appliquer le filtre par note si spécifié
        if ($filterRating !== null && is_numeric($filterRating)) {
            $query->where('rating', (int)$filterRating);
        }

        // Appliquer le filtre pour les avis avec images
        if ($withImages === 'true' || $withImages === '1') {
            $query->whereNotNull('images')->where('images', '!=', '[]');
        }

        // Appliquer le tri
        $query->orderBy($sortBy, $sortOrder);

        // Paginer les résultats
        $reviews = $query->paginate($perPage);

        return response()->json($reviews);
    }

    /**
     * Ajoute un avis pour un produit
     *
     * @param Request $request
     * @param string $productId
     * @return JsonResponse
     */
    public function store(Request $request, string $productId): JsonResponse
    {
        // Vérifier si le produit existe
        $product = Produit::findOrFail($productId);

        // Valider les données
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'email' => 'nullable|email|max:255',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'required|string|min:10',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Traiter les images
        $imageNames = [];
        if ($request->hasFile('images')) {
            // Déterminer le dossier de stockage basé sur l'ID du produit
            $productIdInt = (int)$productId;
            $folderPrefix = $productIdInt < 1000 ? '0' : substr((string)$productIdInt, 0, -3);

            // Créer le dossier s'il n'existe pas
            $storagePath = public_path("images/reviews/{$folderPrefix}");
            if (!file_exists($storagePath)) {
                mkdir($storagePath, 0755, true);
            }

            foreach ($request->file('images') as $image) {
                $imageName = Str::uuid() . '.' . $image->getClientOriginalExtension();
                $image->move($storagePath, $imageName);
                $imageNames[] = [
                    'name' => $imageName,
                    'folder' => $folderPrefix
                ];
            }
        }

        // Créer l'avis
        $review = new Review([
            'produit_id' => $productId,
            'user_id' => null, // Pour l'instant, on ne gère pas les utilisateurs connectés
            'name' => $request->name,
            'email' => $request->email,
            'rating' => $request->rating,
            'comment' => $request->comment,
            'images' => $imageNames,
            'ip_address' => $request->ip(),
            'is_approved' => true, // Auto-approuvé pour l'instant
        ]);

        $review->save();

        return response()->json($review, 201);
    }

    /**
     * Vote pour un avis (like/dislike)
     *
     * @param Request $request
     * @param string $reviewId
     * @return JsonResponse
     */
    public function vote(Request $request, string $reviewId): JsonResponse
    {
        // Valider les données
        $validator = Validator::make($request->all(), [
            'vote_type' => 'required|in:like,dislike',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Vérifier si l'avis existe
        $review = Review::findOrFail($reviewId);

        // Vérifier si l'utilisateur a déjà voté
        $existingVote = ReviewVote::where('review_id', $reviewId)
            ->where('ip_address', $request->ip())
            ->first();

        if ($existingVote) {
            // Si le vote est du même type, le supprimer (annuler le vote)
            if ($existingVote->vote_type === $request->vote_type) {
                // Mettre à jour le compteur
                if ($request->vote_type === 'like') {
                    $review->likes = max(0, $review->likes - 1);
                } else {
                    $review->dislikes = max(0, $review->dislikes - 1);
                }
                $review->save();
                $existingVote->delete();

                return response()->json([
                    'message' => 'Vote annulé',
                    'likes' => $review->likes,
                    'dislikes' => $review->dislikes,
                ]);
            }

            // Si le vote est d'un type différent, le mettre à jour
            // Mettre à jour le compteur
            if ($existingVote->vote_type === 'like') {
                $review->likes = max(0, $review->likes - 1);
                $review->dislikes++;
            } else {
                $review->dislikes = max(0, $review->dislikes - 1);
                $review->likes++;
            }
            $existingVote->vote_type = $request->vote_type;
            $existingVote->save();
            $review->save();

            return response()->json([
                'message' => 'Vote mis à jour',
                'likes' => $review->likes,
                'dislikes' => $review->dislikes,
            ]);
        }

        // Créer un nouveau vote
        $vote = new ReviewVote([
            'review_id' => $reviewId,
            'user_id' => null, // Pour l'instant, on ne gère pas les utilisateurs connectés
            'ip_address' => $request->ip(),
            'vote_type' => $request->vote_type,
        ]);

        $vote->save();

        // Mettre à jour le compteur
        if ($request->vote_type === 'like') {
            $review->likes++;
        } else {
            $review->dislikes++;
        }
        $review->save();

        return response()->json([
            'message' => 'Vote enregistré',
            'likes' => $review->likes,
            'dislikes' => $review->dislikes,
        ]);
    }
}
