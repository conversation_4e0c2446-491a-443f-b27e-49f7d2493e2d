<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Produit;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProduitController extends Controller
{
    /**
     * Récupère tous les produits
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $produits = Produit::with(['categorie', 'marchand'])->get();
        return response()->json($produits);
    }

    /**
     * Récupère un produit spécifique par son ID
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        $produit = Produit::with(['categorie', 'marchand'])->findOrFail($id);
        return response()->json($produit);
    }

    /**
     * Récupère un produit spécifique par son slug
     *
     * @param string $slug
     * @return JsonResponse
     */
    public function getBySlug(string $slug): JsonResponse
    {
        $produit = Produit::with(['categorie', 'marchand'])->where('slug', $slug)->firstOrFail();
        return response()->json($produit);
    }

    /**
     * Récupère les produits d'une catégorie spécifique
     *
     * @param string $categorieId
     * @param Request $request
     * @return JsonResponse
     */
    public function getByCategorie(string $categorieId, Request $request): JsonResponse
    {
        $limit = $request->input('limit', 0);

        $query = Produit::with(['marchand'])
            ->where('categorie_id', $categorieId);

        if ($limit > 0) {
            $query->limit($limit);
        }

        $produits = $query->get();
        return response()->json($produits);
    }

    /**
     * Récupère les produits en vedette
     *
     * @param int $limit Nombre de produits à récupérer
     * @return JsonResponse
     */
    public function getFeatured(int $limit = 8): JsonResponse
    {
        $produits = Produit::with(['categorie', 'marchand'])
            ->inRandomOrder()
            ->limit($limit)
            ->get();
        return response()->json($produits);
    }

    /**
     * Récupère les produits en promotion
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDiscounted(Request $request): JsonResponse
    {
        $limit = $request->input('limit', 0);

        $query = Produit::with(['categorie', 'marchand'])
            ->whereNotNull('discount_price')
            ->where('discount_price', '>', 0)
            ->where(function($q) {
                $now = now();
                $q->whereNull('discount_start_date')
                  ->orWhere('discount_start_date', '<=', $now);
            })
            ->where(function($q) {
                $now = now();
                $q->whereNull('discount_end_date')
                  ->orWhere('discount_end_date', '>=', $now);
            });

        if ($limit > 0) {
            $query->limit($limit);
        }

        $produits = $query->get();
        return response()->json($produits);
    }

    /**
     * Recherche des produits
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->input('q', '');

        $produits = Produit::with(['categorie', 'marchand'])
            ->where('nom', 'like', "%{$query}%")
            ->orWhere('description', 'like', "%{$query}%")
            ->get();

        return response()->json($produits);
    }
}
