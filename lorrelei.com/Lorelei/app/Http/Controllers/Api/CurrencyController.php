<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Services\CurrencyService;
use Illuminate\Http\Request;

class CurrencyController extends Controller
{
    /**
     * Get all active currencies.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $currencies = Currency::where('is_active', true)->get();
        return response()->json($currencies);
    }

    /**
     * Get the current currency.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function current()
    {
        $currency = CurrencyService::getCurrentCurrency();
        return response()->json($currency);
    }

    /**
     * Set the current currency.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function setCurrent(Request $request)
    {
        $request->validate([
            'currency' => 'required|string|exists:currencies,code',
        ]);

        $currency = CurrencyService::setCurrentCurrency($request->currency);

        if (!$currency) {
            return response()->json(['error' => 'Invalid currency'], 400);
        }

        return response()->json($currency);
    }

    /**
     * Get the default currency.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function default()
    {
        $currency = CurrencyService::getDefaultCurrency();
        return response()->json($currency);
    }

    /**
     * Convert a price from one currency to another.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function convert(Request $request)
    {
        $request->validate([
            'price' => 'required|numeric',
            'from' => 'required|string|exists:currencies,code',
            'to' => 'required|string|exists:currencies,code',
        ]);

        $price = $request->price;
        $fromCurrency = Currency::where('code', $request->from)->first();
        $toCurrency = Currency::where('code', $request->to)->first();

        if (!$fromCurrency || !$toCurrency) {
            return response()->json(['error' => 'Invalid currency'], 400);
        }

        // Convertir d'abord en devise par défaut
        $defaultCurrency = CurrencyService::getDefaultCurrency();
        $priceInDefaultCurrency = $price;

        if ($fromCurrency->code !== $defaultCurrency->code) {
            $priceInDefaultCurrency = $price / $fromCurrency->exchange_rate;
        }

        // Puis convertir de la devise par défaut à la devise cible
        $convertedPrice = $priceInDefaultCurrency;
        if ($toCurrency->code !== $defaultCurrency->code) {
            $convertedPrice = $priceInDefaultCurrency * $toCurrency->exchange_rate;
        }

        return response()->json([
            'original_price' => $price,
            'from_currency' => $fromCurrency->code,
            'to_currency' => $toCurrency->code,
            'converted_price' => $convertedPrice,
        ]);
    }
}
