<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Categorie;
use App\Models\Produit;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CategorieController extends Controller
{
    /**
     * Récupère toutes les catégories
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $categories = Categorie::all();
        return response()->json($categories);
    }

    /**
     * Récupère les catégories principales (sans parent) qui ont des produits
     * soit directement, soit via leurs sous-catégories
     *
     * @return JsonResponse
     */
    public function getMainCategories(): JsonResponse
    {
        // 1. Récupérer les IDs des catégories qui ont des produits directement
        $categoriesWithDirectProducts = Produit::select('categorie_id')
            ->distinct()
            ->pluck('categorie_id')
            ->toArray();

        if (empty($categoriesWithDirectProducts)) {
            return response()->json([]);
        }

        // 2. Récupérer toutes les catégories qui ont des produits
        $categoriesWithProducts = Categorie::whereIn('id', $categoriesWithDirectProducts)->get();

        // 3. Extraire les IDs des catégories principales à partir des chemins de catégorie
        $mainCategoryIds = [];

        foreach ($categoriesWithProducts as $category) {
            // Si c'est déjà une catégorie principale
            if ($category->categorie_parent_id === null) {
                $mainCategoryIds[] = $category->id;
                continue;
            }

            // Sinon, extraire l'ID de la catégorie principale à partir du chemin
            if (!empty($category->category_path)) {
                // Le chemin est au format "id_parent/id_category" ou simplement "id" pour les catégories principales
                $pathParts = explode('/', $category->category_path);
                if (!empty($pathParts[0])) {
                    $mainCategoryIds[] = (int)$pathParts[0];
                }
            }
        }

        // 4. Récupérer les catégories principales uniques qui ont des produits
        $mainCategories = Categorie::whereIn('id', array_unique($mainCategoryIds))
            ->whereNull('categorie_parent_id')
            ->get();

        return response()->json($mainCategories);
    }

    /**
     * Récupère une catégorie spécifique par son ID
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        $categorie = Categorie::with('categorieParent')->findOrFail($id);
        return response()->json($categorie);
    }

    /**
     * Récupère une catégorie spécifique par son slug
     *
     * @param string $slug
     * @return JsonResponse
     */
    public function getBySlug(string $slug): JsonResponse
    {
        $categorie = Categorie::with('categorieParent')->where('slug', $slug)->firstOrFail();
        return response()->json($categorie);
    }

    /**
     * Récupère les sous-catégories d'une catégorie
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getSubcategories(string $id): JsonResponse
    {
        $subcategories = Categorie::where('categorie_parent_id', $id)->get();
        return response()->json($subcategories);
    }

    /**
     * Récupère les catégories parentes d'une catégorie
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getParents(string $id): JsonResponse
    {
        $categorie = Categorie::findOrFail($id);
        $parents = [];

        // Récupérer récursivement les catégories parentes
        $currentParent = $categorie->categorieParent;
        while ($currentParent) {
            $parents[] = $currentParent;
            $currentParent = $currentParent->categorieParent;
        }

        // Inverser l'ordre pour avoir les parents du plus haut niveau au plus bas
        return response()->json(array_reverse($parents));
    }

    /**
     * Récupère les sous-catégories avec des produits en vedette
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getSubcategoriesWithFeaturedProducts(string $id): JsonResponse
    {
        $subcategories = Categorie::where('categorie_parent_id', $id)->get();

        $result = $subcategories->map(function ($subcategory) {
            // Récupérer un produit en vedette pour cette sous-catégorie
            $featuredProduct = Produit::where('categorie_id', $subcategory->id)
                ->inRandomOrder()
                ->first();

            return [
                'category' => $subcategory,
                'featuredProduct' => $featuredProduct
            ];
        });

        return response()->json($result);
    }
}
