<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Imports\CategorieImport;
use App\Imports\ProduitImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ImportController extends Controller
{
    /**
     * Affiche la page d'importation
     */
    public function index()
    {
        // Vérifier si des données d'aperçu sont disponibles dans la session
        $previewCategories = session('preview_categories');
        $previewTotal = session('preview_total');
        $showPreview = session('show_preview', false);

        return view('admin.import.index', [
            'preview_categories' => $previewCategories,
            'preview_total' => $previewTotal,
            'show_preview' => $showPreview
        ]);
    }

    /**
     * Analyse le fichier d'importation des catégories et affiche un aperçu
     */
    public function importCategories(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt,xls,xlsx|max:10240',
        ]);

        try {
            // Récupérer le fichier téléchargé
            $file = $request->file('file');
            // Vérifier si le fichier existe et est lisible
            if (!$file || !$file->isValid()) {
                throw new \Exception('Le fichier téléchargé est invalide ou corrompu.');
            }

            // Stocker temporairement le fichier dans un emplacement sûr
            $path = $file->storeAs('imports', $file->getClientOriginalName(), 'local');
            $fullPath = storage_path('app/' . $path);

            // Vérifier si le fichier a été correctement stocké
            if (!file_exists($fullPath) || !is_readable($fullPath)) {
                throw new \Exception('Impossible d\'accéder au fichier téléchargé.');
            }

            // Créer et exécuter l'importation (analyse seulement)
            $import = new CategorieImport();
            $results = $import->import($fullPath);

            // Stocker le chemin du fichier en session pour l'utiliser lors de l'importation réelle
            session(['import_categories_file_path' => $fullPath]);

            // Déboguer les résultats
            Log::info('Résultats de l\'importation des catégories', $results);

            // Vérifier si les clés attendues existent
            if (!isset($results['categories']) || !isset($results['total'])) {
                Log::error('Les clés attendues n\'existent pas dans les résultats', $results);
                return redirect()->route('admin.import.index')->with([
                    'error' => 'Format de données incorrect lors de l\'importation des catégories'
                ]);
            }

            // Retourner directement à la page d'index avec les données
            return redirect()->route('admin.import.index')->with([
                'preview_categories' => $results['categories'],
                'preview_total' => $results['total'],
                'show_preview' => true
            ]);
        } catch (\Exception $e) {
            return redirect()->route('admin.import.index')->with([
                'error' => 'Erreur lors de l\'analyse des catégories: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Affiche l'aperçu des catégories à importer
     */
    public function previewCategories()
    {
        // Récupérer les données de la session
        $categories = session('preview_categories');
        $total = session('preview_total');

        // Déboguer les données
        Log::info('Données de prévisualisation des catégories', [
            'categories' => $categories,
            'total' => $total
        ]);

        if (!$categories || !$total) {
            return redirect()->route('admin.import.index')->with([
                'error' => 'Aucune donnée d\'importation disponible. Veuillez réessayer.'
            ]);
        }

        try {
            return view('admin.import.preview-categories', [
                'categories' => $categories,
                'total' => $total
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors du rendu de la vue preview-categories', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('admin.import.index')->with([
                'error' => 'Erreur lors de l\'affichage de l\'aperçu des catégories: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Confirme l'importation des catégories après prévisualisation
     */
    public function confirmImportCategories()
    {
        // Récupérer le chemin du fichier stocké en session
        $fullPath = session('import_categories_file_path');

        if (!$fullPath || !file_exists($fullPath) || !is_readable($fullPath)) {
            return redirect()->route('admin.import.index')->with([
                'error' => 'Le fichier d\'importation n\'est plus disponible. Veuillez réessayer.'
            ]);
        }

        try {
            // TODO: Implémenter l'importation réelle des catégories
            // Pour l'instant, nous allons simplement supprimer le fichier

            // Supprimer le fichier temporaire
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }

            // Supprimer les données de session
            session()->forget('import_categories_file_path');
            session()->forget('preview_categories');
            session()->forget('preview_total');
            session()->forget('show_preview');

            return redirect()->route('admin.import.index')->with([
                'success' => 'Aperçu des catégories terminé. L\'importation réelle sera implémentée ultérieurement.'
            ]);
        } catch (\Exception $e) {
            return redirect()->route('admin.import.index')->with([
                'error' => 'Erreur lors de l\'importation des catégories: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Importe des produits à partir d'un fichier CSV/Excel
     */
    public function importProducts(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt,xls,xlsx|max:10240',
            'marchand_id' => 'nullable|exists:marchands,id',
        ]);

        try {
            // Récupérer le fichier téléchargé et l'ID du marchand
            $file = $request->file('file');
            $marchandId = $request->input('marchand_id');

            // Vérifier si le fichier existe et est lisible
            if (!$file || !$file->isValid()) {
                throw new \Exception('Le fichier téléchargé est invalide ou corrompu.');
            }

            // Stocker temporairement le fichier dans un emplacement sûr
            $path = $file->storeAs('imports', $file->getClientOriginalName(), 'local');
            $fullPath = storage_path('app/' . $path);

            // Vérifier si le fichier a été correctement stocké
            if (!file_exists($fullPath) || !is_readable($fullPath)) {
                throw new \Exception('Impossible d\'accéder au fichier téléchargé.');
            }

            // Créer et exécuter l'importation
            $import = new ProduitImport($marchandId);
            $import->import($fullPath);
            $results = $import->getResults();

            // Supprimer le fichier temporaire
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }

            return redirect()->route('admin.import.index')->with([
                'success' => 'Importation des produits terminée',
                'results' => $results
            ]);
        } catch (\Exception $e) {
            return redirect()->route('admin.import.index')->with([
                'error' => 'Erreur lors de l\'importation des produits: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Télécharge un modèle de fichier CSV pour l'importation de catégories
     */
    public function downloadCategoriesTemplate()
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="categories_template.csv"',
        ];

        $callback = function() {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['nom_fr', 'nom_en', 'description_fr', 'description_en', 'categorie_parent', 'image_url']);

            // Exemple de données
            fputcsv($file, ['Électronique', 'Electronics', 'Produits électroniques', 'Electronic products', '', '']);
            fputcsv($file, ['Ordinateurs', 'Computers', 'Ordinateurs et accessoires', 'Computers and accessories', 'Électronique', '']);
            fputcsv($file, ['Smartphones', 'Smartphones', 'Téléphones intelligents', 'Smartphones and accessories', 'Électronique', '']);

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Télécharge un modèle de fichier CSV pour l'importation de produits
     */
    public function downloadProductsTemplate()
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="products_template.csv"',
        ];

        $callback = function() {
            $file = fopen('php://output', 'w');
            fputcsv($file, [
                'nom_fr', 'nom_en', 'description_fr', 'description_en', 'categorie',
                'prix', 'currency', 'stock', 'prix_remise', 'date_debut_remise', 'date_fin_remise',
                'poids', 'longueur', 'largeur', 'hauteur', 'product_code', 'marque'
            ]);

            // Exemple de données
            fputcsv($file, [
                'Ordinateur portable XYZ', 'XYZ Laptop', 'Description en français', 'Description in English', 'Ordinateurs',
                '999.99', 'FCFA', '10', '899.99', '2023-01-01', '2023-12-31',
                '2.5', '35', '25', '2', 'XYZ123456789', 'Dell'
            ]);

            fputcsv($file, [
                'Smartphone ABC', 'ABC Smartphone', 'Description en français', 'Description in English', 'Smartphones',
                '599.99', 'EUR', '20', '', '', '',
                '0.2', '15', '7', '1', 'ABC987654321', 'Samsung'
            ]);

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
