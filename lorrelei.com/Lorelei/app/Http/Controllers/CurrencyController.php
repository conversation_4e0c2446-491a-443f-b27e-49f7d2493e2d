<?php

namespace App\Http\Controllers;

use App\Services\CurrencyService;
use Illuminate\Http\Request;

class CurrencyController extends Controller
{
    /**
     * Change the currency.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function change(Request $request)
    {
        $request->validate([
            'currency' => 'required|string|size:4',
        ]);
        
        $currency = CurrencyService::setCurrentCurrency($request->currency);
        
        if (!$currency) {
            return back()->with('error', 'Devise non valide.');
        }
        
        return back()->with('success', 'Devise changée avec succès.');
    }
}
