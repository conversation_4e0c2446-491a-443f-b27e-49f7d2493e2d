<?php

namespace App\Providers;

use App\Models\Adresse;
use App\Models\ProductVariant;
use App\Helpers\ImageStorage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use App\Observers\AdresseObserver;
use App\Observers\ProductVariantObserver;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Enregistrer les observateurs
        Adresse::observe(AdresseObserver::class);
        ProductVariant::observe(ProductVariantObserver::class);

        // Étendre la classe TemporaryUploadedFile pour ajouter notre méthode de stockage personnalisée
        TemporaryUploadedFile::macro('storeWithIdFolder', function ($id, $baseDir) {
            return ImageStorage::storeImage($this, $id, $baseDir);
        });
    }
}
