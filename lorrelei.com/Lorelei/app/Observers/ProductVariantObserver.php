<?php

namespace App\Observers;

use App\Models\ProductVariant;
use App\Helpers\ImageStorage;

class ProductVariantObserver
{
    /**
     * Handle the ProductVariant "created" event.
     */
    public function created(ProductVariant $variant): void
    {
        $this->moveImagesAfterCreate($variant);
    }

    /**
     * Déplace les images du dossier temporaire vers le dossier basé sur l'ID
     */
    private function moveImagesAfterCreate(ProductVariant $variant): void
    {
        $images = $variant->images;
        if (empty($images)) {
            return;
        }

        $produitId = $variant->produit_id;
        $variantId = $variant->id;
        $folderPrefix = ImageStorage::getFolderPrefix($produitId);
        $newPath = "products/{$folderPrefix}/variants/{$variantId}";

        // Créer le dossier s'il n'existe pas
        $fullPath = public_path("images/{$newPath}");
        if (!file_exists($fullPath)) {
            mkdir($fullPath, 0755, true);
        }

        // Si c'est un tableau d'images
        if (is_array($images)) {
            $newImages = [];
            foreach ($images as $image) {
                // Vérifier si l'image est dans le dossier temporaire
                if (strpos($image, "products/variants/0/") === 0) {
                    $filename = basename($image);
                    $oldPath = public_path("images/{$image}");
                    $newFilePath = "{$newPath}/{$filename}";
                    $newFullPath = public_path("images/{$newFilePath}");

                    // Déplacer le fichier
                    if (file_exists($oldPath)) {
                        rename($oldPath, $newFullPath);
                        $newImages[] = $newFilePath;
                    } else {
                        $newImages[] = $image;
                    }
                } else {
                    $newImages[] = $image;
                }
            }

            // Mettre à jour l'enregistrement
            $variant->images = $newImages;
            $variant->save();
        }
        // Si c'est une seule image
        else if (is_string($images) && strpos($images, "products/variants/0/") === 0) {
            $filename = basename($images);
            $oldPath = public_path("images/{$images}");
            $newFilePath = "{$newPath}/{$filename}";
            $newFullPath = public_path("images/{$newFilePath}");

            // Déplacer le fichier
            if (file_exists($oldPath)) {
                rename($oldPath, $newFullPath);
                $variant->images = $newFilePath;
                $variant->save();
            }
        }
    }
}
