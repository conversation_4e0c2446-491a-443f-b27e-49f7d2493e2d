<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ZoneLivraison extends Model
{
    use HasFactory;

    /**
     * La table associée au modèle.
     *
     * @var string
     */
    protected $table = 'zones_livraison';

    /**
     * Les attributs qui sont assignables en masse.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'nom',
        'type',
        'parent_id',
        'code',
        'actif',
    ];

    /**
     * Les attributs à caster.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'actif' => 'boolean',
    ];

    /**
     * Obtenir la zone parente.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(ZoneLivraison::class, 'parent_id');
    }

    /**
     * Obtenir les zones enfants.
     */
    public function enfants(): HasMany
    {
        return $this->hasMany(ZoneLivraison::class, 'parent_id');
    }

    /**
     * Obtenir les marchands qui livrent dans cette zone.
     */
    public function marchands()
    {
        return $this->belongsToMany(User::class, 'marchand_zones_livraison', 'zone_livraison_id', 'marchand_id')
            ->withPivot(['frais_livraison', 'delai_livraison_min', 'delai_livraison_max', 'actif'])
            ->withTimestamps();
    }

    /**
     * Obtenir les adresses associées à cette zone.
     */
    public function adresses(): HasMany
    {
        return $this->hasMany(Adresse::class, 'zone_livraison_id');
    }

    /**
     * Obtenir les zones de livraison des marchands associées à cette zone.
     */
    public function marchandZonesLivraison(): HasMany
    {
        return $this->hasMany(MarchandZoneLivraison::class, 'zone_livraison_id');
    }
}
