<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Translatable\HasTranslations;

class SizeGuide extends Model
{
    use HasFactory, HasTranslations;

    /**
     * Les attributs qui sont traduisibles.
     *
     * @var array
     */
    public $translatable = ['name', 'instructions'];

    /**
     * Les attributs qui sont mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name_fr',
        'name_en',
        'category',
        'measurement_types',
        'size_systems',
        'size_chart',
        'instructions_fr',
        'instructions_en',
        'fitting_tips',
        'image',
        'is_active',
    ];

    /**
     * Les attributs qui doivent être castés.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'measurement_types' => 'array',
        'size_systems' => 'array',
        'size_chart' => 'array',
        'fitting_tips' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Obtient le nom du guide en fonction de la langue actuelle.
     *
     * @return string
     */
    public function getLocalizedNameAttribute(): string
    {
        $locale = app()->getLocale();
        return $locale === 'fr' ? $this->name_fr : $this->name_en;
    }

    /**
     * Obtient les instructions en fonction de la langue actuelle.
     *
     * @return string|null
     */
    public function getLocalizedInstructionsAttribute(): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'fr' ? $this->instructions_fr : $this->instructions_en;
    }

    /**
     * Relation avec les catégories.
     *
     * @return BelongsToMany
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Categorie::class, 'categorie_size_guide', 'size_guide_id', 'categorie_id')
            ->withTimestamps();
    }
}
