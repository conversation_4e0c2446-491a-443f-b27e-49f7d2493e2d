<?php

namespace App\Models;

use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class User extends Authenticatable implements FilamentUser
{
    use HasFactory, Notifiable;

    /**
     * The relationships that should be eager loaded.
     *
     * @var array
     */
    protected $with = ['client', 'marchand', 'adresses'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'last_login_at',
        'is_active',
        'email_verification_token',
        'password_reset_token',
        'password_reset_expires_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'created_at' => 'timestamp',
            'last_login_at' => 'timestamp',
            'is_active' => 'boolean',
            'password_reset_expires_at' => 'timestamp',
        ];
    }

    public function clients(): HasMany
    {
        return $this->hasMany(Client::class);
    }

    public function client(): HasOne
    {
        return $this->hasOne(Client::class);
    }

    public function marchands(): HasMany
    {
        return $this->hasMany(Marchand::class);
    }

    public function marchand(): HasOne
    {
        return $this->hasOne(Marchand::class);
    }

    public function adresses(): HasMany
    {
        return $this->hasMany(Adresse::class);
    }

    /**
     * Determine if the user can access the given Filament panel.
     *
     * @param Panel $panel
     * @return bool
     */
    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() === 'admin') {
            return $this->role === 'Admin';
        }

        if ($panel->getId() === 'marchand') {
            return $this->role === 'Marchand';
        }

        return false;
    }

    /**
     * Get the user's name.
     *
     * @return string
     */
    public function getName(): string
    {
        // Si le champ name est rempli, on l'utilise
        if ($this->name) {
            return $this->name;
        }

        // Si l'utilisateur est un client ou un marchand, on récupère son nom
        if ($this->role === 'Client' && $this->clients->first()) {
            return $this->clients->first()->prenom . ' ' . $this->clients->first()->nom;
        }

        if ($this->role === 'Marchand' && $this->marchands->first()) {
            return $this->marchands->first()->nomEntreprise;
        }

        // Sinon, on utilise l'email
        return $this->email;
    }
}
