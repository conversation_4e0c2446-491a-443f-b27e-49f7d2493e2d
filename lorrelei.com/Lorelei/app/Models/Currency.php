<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Currency extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'name',
        'symbol',
        'is_default',
        'is_active',
        'exchange_rate',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'exchange_rate' => 'float',
    ];

    /**
     * Get the default currency.
     *
     * @return self
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->first() ?? static::first();
    }

    /**
     * Get all active currencies.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getActive()
    {
        return static::where('is_active', true)->get();
    }

    /**
     * Format a price with this currency.
     *
     * @param float $price
     * @return string
     */
    public function format($price)
    {
        return number_format($price, 2) . ' ' . $this->symbol;
    }

    /**
     * Convert a price from the default currency to this currency.
     *
     * @param float $price
     * @return float
     */
    public function convert($price)
    {
        $defaultCurrency = static::getDefault();

        if ($this->code === $defaultCurrency->code) {
            return $price;
        }

        return $price * $this->exchange_rate;
    }
}
