<?php

namespace App\Services;

use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Collection;

class SpreadsheetImporter
{
    /**
     * Importe un fichier CSV ou Excel et retourne les données sous forme de collection
     *
     * @param string $filePath Chemin du fichier à importer
     * @param bool $hasHeader Si le fichier a une ligne d'en-tête
     * @return Collection
     */
    public static function import(string $filePath, bool $hasHeader = true): Collection
    {
        // Détecter le type de fichier et créer le reader approprié
        $spreadsheet = IOFactory::load($filePath);
        $worksheet = $spreadsheet->getActiveSheet();

        // Convertir les données en tableau
        $data = $worksheet->toArray();

        // Si le fichier a un en-tête, l'utiliser comme clés
        if ($hasHeader && count($data) > 0) {
            $headers = array_shift($data);

            return collect($data)->map(function ($row) use ($headers) {
                $combinedRow = array_combine($headers, $row);

                // S'assurer que toutes les chaînes sont correctement encodées en UTF-8
                return array_map(function ($value) {
                    if (is_string($value)) {
                        // Détecter l'encodage actuel
                        $encoding = mb_detect_encoding($value, ['UTF-8', 'ISO-8859-1', 'Windows-1252'], true);
                        // Convertir en UTF-8 si nécessaire
                        if ($encoding !== 'UTF-8') {
                            $value = mb_convert_encoding($value, 'UTF-8', $encoding ?: 'ISO-8859-1');
                        }

                        // Nettoyer les caractères de contrôle et autres caractères spéciaux indésirables
                        $value = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/u', '', $value);
                        // Remplacer les caractères non imprimables par des espaces
                        $value = preg_replace('/[^\P{C}\t\r\n]/u', ' ', $value);

                        // Normaliser les espaces (supprimer les espaces multiples)
                        $value = preg_replace('/\s+/', ' ', $value);

                        // Supprimer les BOM (Byte Order Mark) qui peuvent causer des problèmes
                        $value = preg_replace('/^\xEF\xBB\xBF/', '', $value);

                        return trim($value);
                    }

                    return $value;
                }, $combinedRow);
            });
        }
        return collect($data);
    }
}
