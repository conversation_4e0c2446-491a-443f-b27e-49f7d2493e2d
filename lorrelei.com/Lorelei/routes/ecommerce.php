<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\Ecommerce\ProfileController;

// Routes e-commerce (à la racine du site)

// Page d'accueil
Route::get('/', function () {
    return Inertia::render('ecommerce/home');
})->name('home');

// Page de toutes les catégories
Route::get('/categories', function () {
    return Inertia::render('ecommerce/categories');
})->name('categories');

// Page de catégorie spécifique (par slug)
Route::get('/categories/{categorySlug}', function ($categorySlug) {
    return Inertia::render('ecommerce/category', [
        'categorySlug' => $categorySlug,
    ]);
})->name('category');

// Route de compatibilité pour les anciens liens par ID
Route::get('/category/{categoryId}', function ($categoryId) {
    // Récupérer la catégorie par ID
    $category = \App\Models\Categorie::find($categoryId);

    // Rediriger vers la nouvelle URL avec le slug
    if ($category && $category->slug) {
        return redirect()->route('category', ['categorySlug' => $category->slug], 301);
    }

    // Fallback si la catégorie n'existe pas
    return redirect()->route('categories');
});

// Page de tous les produits
Route::get('/products', function () {
    return Inertia::render('ecommerce/products');
})->name('products');

// Page de produit spécifique (par slug)
Route::get('/products/{productSlug}', function ($productSlug) {
    return Inertia::render('ecommerce/product', [
        'productSlug' => $productSlug,
    ]);
})->name('product');

// Route de compatibilité pour les anciens liens par ID
Route::get('/product/{productId}', function ($productId) {
    // Récupérer le produit par ID
    $product = \App\Models\Produit::find($productId);

    // Rediriger vers la nouvelle URL avec le slug
    if ($product && $product->slug) {
        return redirect()->route('product', ['productSlug' => $product->slug], 301);
    }

    // Fallback si le produit n'existe pas
    return redirect()->route('products');
});

// Page de panier
Route::get('/cart', function () {
    return Inertia::render('ecommerce/cart');
})->name('cart');

// Page de liste de souhaits
Route::get('/wishlist', function () {
    return Inertia::render('ecommerce/wishlist');
})->name('wishlist');

// Page de paiement (checkout) - nécessite authentification
Route::middleware(['auth'])->group(function () {
    Route::get('/checkout', function () {
        $user = request()->user();
        $adresses = \App\Models\Adresse::where('user_id', $user->id)->get();

        // Vérifier si l'utilisateur a une adresse de livraison
        $hasShippingAddress = $adresses->contains('type', 'Livraison');

        return Inertia::render('ecommerce/checkout', [
            'adresses' => $adresses,
            'hasShippingAddress' => $hasShippingAddress,
            'user' => $user
        ]);
    })->name('checkout');
});

// Page de recherche
Route::get('/search', function () {
    return Inertia::render('ecommerce/search', [
        'query' => request('q', ''),
    ]);
})->name('search');

// Routes du profil utilisateur (nécessitent authentification)
Route::middleware(['auth'])->group(function () {
    // Page de profil
    Route::get('/my-account', [ProfileController::class, 'show'])->name('my-account');

    // Mise à jour des informations personnelles
    Route::post('/my-account/personal-info', [ProfileController::class, 'updatePersonalInfo'])->name('my-account.personal-info.update');

    // Gestion des adresses
    Route::post('/my-account/addresses', [ProfileController::class, 'addAddress'])->name('my-account.addresses.add');
    Route::put('/my-account/addresses/{id}', [ProfileController::class, 'updateAddress'])->name('my-account.addresses.update');
    Route::delete('/my-account/addresses/{id}', [ProfileController::class, 'deleteAddress'])->name('my-account.addresses.delete');
    Route::post('/my-account/addresses/{id}/default', [ProfileController::class, 'setDefaultAddress'])->name('my-account.addresses.default');
});
