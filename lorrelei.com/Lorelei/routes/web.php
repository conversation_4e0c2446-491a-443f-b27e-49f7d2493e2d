<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Filament\Resources\ImportResource\Pages\ImportIndex as AdminImportIndex;
use App\Filament\Marchand\Resources\ImportResource\Pages\ImportIndex as MerchantImportIndex;
use App\Http\Controllers\CurrencyController;

// Routes originales du starter kit
Route::get('/welcome', function () {
    return Inertia::render('welcome');
})->name('welcome');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Redirection de /profile vers /settings/profile
    Route::redirect('profile', 'settings/profile');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

// Les routes e-commerce sont à la racine du site
require __DIR__.'/ecommerce.php';

// Routes d'administration
require __DIR__.'/admin.php';

// Routes pour les modèles d'importation
Route::get('/admin/importations/download-categories-template', [AdminImportIndex::class, 'downloadCategoriesTemplate'])
    ->name('filament.admin.resources.importations.download-categories-template')
    ->middleware(['web', 'auth']);

Route::get('/admin/importations/download-products-template', [AdminImportIndex::class, 'downloadProductsTemplate'])
    ->name('filament.admin.resources.importations.download-products-template')
    ->middleware(['web', 'auth']);

// Route fallback pour les pages 404
Route::fallback(function () {
    return Inertia::render('not-found');
});

Route::get('/merchant/importations/download-products-template', [MerchantImportIndex::class, 'downloadProductsTemplate'])
    ->name('filament.marchand.resources.importations.download-products-template')
    ->middleware(['web', 'auth']);

// Routes pour la gestion des devises
Route::post('/currency/change', [CurrencyController::class, 'change'])->name('currency.change');



