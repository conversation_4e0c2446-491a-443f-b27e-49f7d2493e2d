server {
    listen 80;
    listen [::]:80;
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name lorrelei.franckeldev.com www.lorrelei.com;
    root /var/www/lorrelei.com/Lorelei/public;

    # SSL Configuration (vous pouvez utiliser un certificat auto-signé temporairement)
    ssl_certificate /etc/ssl/certs/ssl-cert-snakeoil.pem;
    ssl_certificate_key /etc/ssl/private/ssl-cert-snakeoil.key;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ ^/index\.php(/|$) {
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}


# server {
#     listen 80;
#     server_name cdn.lorrelei.com;
#     root /var/www/cdn/cdn.lorrelei.com;

#     location / {
#         try_files $uri $uri/ =404;

#         # Cache headers
#         expires 1y;
#         add_header Cache-Control "public, max-age=31536000, immutable";

#         # Compression
#         gzip on;
#         gzip_types image/svg+xml;

#         # Désactiver PHP
#         location ~ \.php$ {
#             deny all;
#         }
#     }

#     access_log /var/log/nginx/cdn.lorelei.com-access.log;
#     error_log /var/log/nginx/cdn.lorelei.com-error.log;
# }

